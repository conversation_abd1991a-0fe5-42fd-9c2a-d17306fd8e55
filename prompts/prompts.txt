Act as a customer service representative manager that has just received this comment form via the website. 
The full name of the customer is: {{text://input.payload:name}} email: {{text://input.payload:email}}  subject: {{text://input.payload:subject}}  message:{{text://input.payload:message}}   
Respond with a json formatted message that contains
- Priority based on the importance of the customer (using number and size of transactions, where transactions larger than 500 are considered important) 
- The urgency of the request (based on what what the customer requested and how the customer is feeling). 
- A summary of the last 3 transactions for the customer 
- all customer contact information, including name, phone number and address information, calculating latitude and longitude for mapping purposes.   

Put all of this into the payload of the message, do not use a separate file. 
You must format the JSON object to match the following JSON schema  

{
    "$schema": "http://json-schema.org/draft-04/schema#",
    "type": "object",
    "properties": {
        "customer_service_assessment": {
            "type": "object",
            "properties": {
                "priority": {
                    "type": "object",
                    "properties": {
                        "level": {
                            "type": "string"
                        },
                        "reason": {
                            "type": "string"
                        }
                    },
                    "required": [
                        "level",
                        "reason"
                    ]
                },
                "urgency": {
                    "type": "object",
                    "properties": {
                        "level": {
                            "type": "string"
                        },
                        "reason": {
                            "type": "string"
                        }
                    },
                    "required": [
                        "level",
                        "reason"
                    ]
                },
                "recent_transactions": {
                    "type": "array",
                    "items": [
                        {
                            "type": "object",
                            "properties": {
                                "date": {
                                    "type": "string"
                                },
                                "type": {
                                    "type": "string"
                                },
                                "amount": {
                                    "type": "number"
                                }
                            },
                            "required": [
                                "date",
                                "type",
                                "amount"
                            ]
                        },
                        {
                            "type": "object",
                            "properties": {
                                "date": {
                                    "type": "string"
                                },
                                "type": {
                                    "type": "string"
                                },
                                "amount": {
                                    "type": "number"
                                }
                            },
                            "required": [
                                "date",
                                "type",
                                "amount"
                            ]
                        }
                    ]
                },
                "customer_location": {
                    "type": "object",
                    "properties": {
                        "first_name": {
                            "type": "string"
                        },
                        "last_name": {
                            "type": "string"
                        },
                        "phone_number": {
                            "type": "string"
                        },
                        "address": {
                            "type": "string"
                        },
                        "city": {
                            "type": "string"
                        },
                        "province": {
                            "type": "string"
                        },
                        "postal_code": {
                            "type": "string"
                        },
                        "country": {
                            "type": "string"
                        },
                        "coordinates": {
                            "type": "object",
                            "properties": {
                                "latitude": {
                                    "type": "number"
                                },
                                "longitude": {
                                    "type": "number"
                                }
                            },
                            "required": [
                                "latitude",
                                "longitude"
                            ]
                        }
                    },
                    "required": [
                        "first_name",
                        "last_name",
                        "phone_number",
                        "address",
                        "city",
                        "province",
                        "postal_code",
                        "country",
                        "coordinates"
                    ]
                }
            },
            "required": [
                "priority",
                "urgency",
                "recent_transactions",
                "customer_location"
            ]
        }
    },
    "required": [
        "customer_service_assessment"
    ]
}