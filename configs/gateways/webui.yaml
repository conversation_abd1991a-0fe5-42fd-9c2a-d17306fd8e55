# Solace AI Connector Configuration for A2A Web UI Backend

log:
  stdout_log_level: INFO
  log_file_level: DEBUG
  log_file: webui_app.log

# Shared SAM config
!include ../shared_config.yaml

apps:
  - name: a2a_webui_app
    app_base_path: .
    app_module: solace_agent_mesh.gateway.http_sse.app

    broker:
      <<: *broker_connection
      # Queue name is generated automatically by WebUIBackendApp based on namespace/gateway_id

    app_config:
      # --- Required ---
      namespace: ${NAMESPACE}
      session_secret_key: ${SESSION_SECRET_KEY}

      authorization_service:
        type: "default_rbac"
        role_definitions_path: "configs/auth/dev-roles.yaml"
        user_assignments_path: "configs/auth/dev-users.yaml"

      # Force all user identities to sam_dev_user (overrides web-client-xxxxx)
      force_user_identity: "sam_dev_user"
      # Fallback for null identities (kept for completeness)
      default_user_identity: "sam_dev_user"

      # --- Artifact Service (Required) ---
      artifact_service: *default_artifact_service

      # --- Optional with Defaults ---
      gateway_id: ${WEBUI_GATEWAY_ID} # Optional: Unique ID for this instance. If omitted, one will be generated.
      fastapi_host: ${FASTAPI_HOST}
      fastapi_port: ${FASTAPI_PORT}
      cors_allowed_origins: # List of allowed origins for CORS
        - "http://localhost:3000" # Example: Allow local React dev server
        - "http://127.0.0.1:3000"
        # Add other origins as needed, or use ["*"] for wide open (less secure)

      # --- Embed Resolution Config ---
      enable_embed_resolution: ${ENABLE_EMBED_RESOLUTION} # Enable late-stage resolution # TODO, create a flag option for this and keep it as env var
      gateway_artifact_content_limit_bytes: ${GATEWAY_ARTIFACT_LIMIT_BYTES, 10000000} # Max size for late-stage embeds

      system_purpose: >
            The system is an AI Chatbot with agentic capabilities.
            It will use the agents available to provide information,
            reasoning and general assistance for the users in this system.
            **Always return useful artifacts and files that you create to the user.**
            Provide a status update before each tool call.
            Your external name is Agent Mesh.

      response_format: >
            Responses should be clear, concise, and professionally toned.
            Format responses to the user in Markdown using appropriate formatting.

      # --- Frontend Config Passthrough ---
      frontend_welcome_message: >
            
      frontend_bot_name: Solace Agent Mesh
      frontend_collect_feedback: false
