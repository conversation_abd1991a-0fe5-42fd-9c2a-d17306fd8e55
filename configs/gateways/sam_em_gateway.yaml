# Example Configuration for SAM Event Mesh Gateway Plugin
#
# This file provides a template for configuring the Event Mesh Gateway.
# It should be included or adapted into your main Solace Agent Mesh Host YAML file.

log:
  stdout_log_level: INFO
  log_file_level: DEBUG # Changed from INFO to DEBUG to capture ADK INFO logs
  log_file: sam_em_gateway.log

# !include shared_config.yaml
!include ../shared_config.yaml

# --- App Definition (to be placed under the 'apps:' list in your main config) ---
apps:
  - name: my_event_mesh_gateway_app # Unique name for this gateway app instance
    app_module: sam_event_mesh_gateway.app # Points to the plugin's App class
    app_base_path: plugins/sam-event-mesh-gateway/src # Base path for the app module
    broker: # Standard SAC broker config for the A2A control plane
      <<: *broker_connection

    app_config:
      namespace: ${NAMESPACE}
      gateway_id: "event-mesh-gw-01" # Unique ID for this gateway instance
      artifact_service: # Configuration for shared ADK Artifact Service
        type: "filesystem"
        base_path: "/tmp/samv2"
      authorization_service:
        type: "default_rbac"
        role_definitions_path: "configs/auth/dev-roles.yaml"
        user_assignments_path: "configs/auth/dev-users.yaml"
      # Force all user identities to sam_dev_user (overrides web-client-xxxxx)
      force_user_identity: "sam_dev_user"
      # Fallback for null identities (kept for completeness)
      default_user_identity: "sam_dev_user"

      # --- Event Mesh Gateway Specific Parameters ---
      event_mesh_broker_config: # For the data plane Solace client
        broker_url: ${DATAPLANE_SOLACE_BROKER_URL} # Can be same or different from control plane
        broker_vpn: ${DATAPLANE_SOLACE_BROKER_VPN}
        broker_username: ${DATAPLANE_SOLACE_BROKER_USERNAME}
        broker_password: ${DATAPLANE_SOLACE_BROKER_PASSWORD}
        # Other data plane client settings (e.g., client_name, reconnection_strategy)

      event_handlers: # List of handlers for incoming Solace messages
        - name: "generic_json_event_handler"
          subscriptions:
            - topic: "fab/customer/feedback/submitted/v1/>"
              qos: 1
          input_expression: |
            template:Act as a customer service representative manager that has just received this comment form via the website. 
            The full name of the customer is: {{text://input.payload:name}} email: {{text://input.payload:email}}  subject: {{text://input.payload:subject}}  message:{{text://input.payload:message}}   
            Respond with a json formatted message that contains
            - Priority based on the importance of the customer (using number and size of transactions, where transactions larger than 500 are considered important) 
            - The urgency of the request (based on what what the customer requested and how the customer is feeling). 
            - A summary of the last 3 transactions for the customer 
            - all customer contact information, including name, phone number and address information, calculating latitude and longitude for mapping purposes.   

            Put all of this into the payload of the message, do not use a separate file. 
            You must format the JSON object to match the following JSON schema  

            {
                "$schema": "http://json-schema.org/draft-04/schema#",
                "type": "object",
                "properties": {
                    "customer_service_assessment": {
                        "type": "object",
                        "properties": {
                            "priority": {
                                "type": "object",
                                "properties": {
                                    "level": {
                                        "type": "string"
                                    },
                                    "reason": {
                                        "type": "string"
                                    }
                                },
                                "required": [
                                    "level",
                                    "reason"
                                ]
                            },
                            "urgency": {
                                "type": "object",
                                "properties": {
                                    "level": {
                                        "type": "string"
                                    },
                                    "reason": {
                                        "type": "string"
                                    }
                                },
                                "required": [
                                    "level",
                                    "reason"
                                ]
                            },
                            "recent_transactions": {
                                "type": "array",
                                "items": [
                                    {
                                        "type": "object",
                                        "properties": {
                                            "date": {
                                                "type": "string"
                                            },
                                            "type": {
                                                "type": "string"
                                            },
                                            "amount": {
                                                "type": "number"
                                            }
                                        },
                                        "required": [
                                            "date",
                                            "type",
                                            "amount"
                                        ]
                                    },
                                    {
                                        "type": "object",
                                        "properties": {
                                            "date": {
                                                "type": "string"
                                            },
                                            "type": {
                                                "type": "string"
                                            },
                                            "amount": {
                                                "type": "number"
                                            }
                                        },
                                        "required": [
                                            "date",
                                            "type",
                                            "amount"
                                        ]
                                    }
                                ]
                            },
                            "customer_location": {
                                "type": "object",
                                "properties": {
                                    "first_name": {
                                        "type": "string"
                                    },
                                    "last_name": {
                                        "type": "string"
                                    },
                                    "phone_number": {
                                        "type": "string"
                                    },
                                    "address": {
                                        "type": "string"
                                    },
                                    "city": {
                                        "type": "string"
                                    },
                                    "province": {
                                        "type": "string"
                                    },
                                    "postal_code": {
                                        "type": "string"
                                    },
                                    "country": {
                                        "type": "string"
                                    },
                                    "coordinates": {
                                        "type": "object",
                                        "properties": {
                                            "latitude": {
                                                "type": "number"
                                            },
                                            "longitude": {
                                                "type": "number"
                                            }
                                        },
                                        "required": [
                                            "latitude",
                                            "longitude"
                                        ]
                                    }
                                },
                                "required": [
                                    "first_name",
                                    "last_name",
                                    "phone_number",
                                    "address",
                                    "city",
                                    "province",
                                    "postal_code",
                                    "country",
                                    "coordinates"
                                ]
                            }
                        },
                        "required": [
                            "priority",
                            "urgency",
                            "recent_transactions",
                            "customer_location"
                        ]
                    }
                },
                "required": [
                    "customer_service_assessment"
                ]
            }
          
          payload_encoding: "utf-8"
          payload_format: "json"
          user_identity_expression: "static:sam_dev_user"
          on_success: "success_response_handler"
          on_error: "error_response_handler"
          target_agent_name: "OrchestratorAgent" 
        # --- Example: Handler with Artifact Processing ---
        # This handler processes a JSON payload containing a list of base64-encoded documents.
        # It creates an artifact for each document and then calls an agent.
        # - name: "json_with_embedded_artifact_handler"
        #   subscriptions:
        #     - topic: "acme/documents/new"
        #   payload_format: "json"
        #   # --- New Artifact Processing Block ---
        #   artifact_processing:
        #     extract_artifacts_expression: "input.payload:documents" # Points to a list in the payload
        #     artifact_definition:
        #       # These expressions are evaluated for EACH item in the 'documents' list
        #       filename: "list_item:docName"
        #       content: "list_item:docContent"
        #       mime_type: "list_item:docType"
        #       content_encoding: "static:base64" # Explicitly state the content is a base64 string
        #   # --- Main Prompt ---
        #   input_expression: "template:Please process insurance case {{text://input.payload:caseId}}. The relevant documents have been attached."
        #   target_agent_name: "ClaimsProcessingAgent"
        #   on_success: "success_response_handler"
        #   on_error: "error_response_handler"
        #   forward_context:
        #     correlation_id: "input.payload:caseId"

  # Example of a second event handler, commented out
  #       - name: "text_event_to_specific_agent"
  #         subscriptions:
  #           - topic: "external/systemB/events/text/>"
  #         input_expression: "template:User query from System B: {{text://input.payload}}"
  #         payload_encoding: "utf-8" # Or "none" if payload is already string
  #         payload_format: "text"
  #         on_success: "text_response_to_systemB"
  #         target_agent_name_expression: "static:TextAnalysisAgent" # Example of static via expression

      output_handlers: # Optional: List of handlers for publishing A2A responses
        - name: "success_response_handler"
          max_file_size_for_base64_bytes: 5242880 # 5MB limit for embedded files
          topic_expression: "template:fab/customer/feedback/enriched/v1/"
          payload_expression: "task_response:text" # Use the simplified payload's text field
          payload_encoding: "utf-8"
          payload_format: "text"
          # output_schema: # Optional: Embedded JSON schema for validation
          #   type: "object"
          #   properties:
          #     processed_data: { "type": "string" }
          #   required: ["processed_data"]
          # on_validation_error: "log" # Or "drop"
        - name: "error_response_handler"
          topic_expression: "template:event_mesh/errors/{{text://user_data.forward_context:correlation_id}}"
          payload_expression: "task_response:a2a_task_response.error" # Send the full error object
          payload_encoding: "utf-8"
          payload_format: "json"


  # Example of a second output handler, commented out
  #       - name: "text_response_to_systemB"
  #         topic_expression: "template:external/systemB/responses/{{text://task_response:id}}"
  #         payload_expression: "task_response:status.message.parts.0.text" # Direct access
  #         payload_encoding: "utf-8"
  #         payload_format: "text"
