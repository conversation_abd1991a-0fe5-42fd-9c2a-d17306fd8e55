# This is an example configuration for an A2A ADK Host application
# that uses the SQL Database Agent Plugin.
#
# How to use:
# 1. Copy this structure into your main A2A ADK Host YAML configuration file,
#    typically under the 'apps:' list.
# 2. Replace placeholder values (like ${YOUR_..._ENV_VAR}) with actual values
#    or environment variable references.
# 3. Adjust configurations (model, instructions, database details, etc.)
#    to match your specific requirements.

#    or environment variable references.
# 3. Adjust configurations (model, instructions, database details, etc.)
#    to match your specific requirements.

log:
  stdout_log_level: INFO
  log_file_level: DEBUG
  log_file: transactions.log

# Shared SAM config
# !include ../shared_config.yaml
!include shared_config.yaml   #done this way so that field reports doesn't have access to image analysis tool to force orchestrator to use MM agent

# shared_config:
#   - broker_connection: &broker_connection
#       dev_mode: ${SOLACE_DEV_MODE, false}
#       broker_url: ${SOLACE_BROKER_URL, ws://localhost:8080}
#       broker_username: ${SOLACE_BROKER_USERNAME, default}
#       broker_password: ${SOLACE_BROKER_PASSWORD, default}
#       broker_vpn: ${SOLACE_BROKER_VPN, default}
#       temporary_queue: ${USE_TEMPORARY_QUEUES, true}

#   - models:
#     general: &general_model
#       # This dictionary structure tells ADK to use the LiteLlm wrapper.
#       # 'model' uses the specific model identifier your endpoint expects.
#       model: ${LLM_SERVICE_GENERAL_MODEL_NAME} # Use env var for model name
#       # 'api_base' tells LiteLLM where to send the request.
#       api_base: ${LLM_SERVICE_ENDPOINT} # Use env var for endpoint URL
#       # 'api_key' provides authentication.
#       api_key: ${LLM_SERVICE_API_KEY} # Use env var for API key

apps:
  - name: transactions-app
    app_module: solace_agent_mesh.agent.sac.app 
    broker:
      <<: *broker_connection
    app_config:
      namespace: "${NAMESPACE}" # Your A2A topic namespace
      agent_name: "RealTimeTransactions"
      display_name: "Real Time Transactions Agent" 
      supports_streaming: false # SQL agent tools are typically request-response

      model: *general_model 
      instruction: |
        You are an expert SQL assistant for the connected database.
        The database schema and query examples will be provided to you.
        Your primary goal is to translate user questions into accurate SQL queries.
        If a user asks to query the database, generate the SQL and call the 'execute_sql_query' tool.
        If the 'execute_sql_query' tool returns an error, analyze the error message and the original SQL,
        then try to correct the SQL query and call the tool again.
        If the results are large and the tool indicates they were saved as an artifact, inform the user about the artifact.
        Always use the 'execute_sql_query' tool to interact with the database.

      # --- Configurable Agent Initialization & Cleanup ---
      agent_init_function:
        module: "sam_sql_database.lifecycle"
        name: "initialize_sql_agent"
        config: # This is the custom_agent_init_config, validated by SqlAgentInitConfigModel
          db_type: "sqlite" # REQUIRED: "postgresql", "mysql", or "sqlite"
          db_host: "${DB_HOST}" # Optional: e.g., "localhost" (required for mysql/postgres)
          db_port: ${DB_PORT} # Optional: e.g., 5432 (required for mysql/postgres)
          db_user: "${DB_USER}" # Optional: (required for mysql/postgres)
          db_password: "${DB_PASSWORD}" # Optional: (required for mysql/postgres)
          db_name: "crm.db" # REQUIRED: Database name or file path for SQLite
          query_timeout: 30 # Optional: Default 30 seconds
          database_purpose: "Latest real time transactions from our bank customers taken from operational data marts." # Optional: Helps LLM understand context
          data_description: "Contains tables that show latest debits and credits. Timestamps are in UTC." # Optional
          auto_detect_schema: true # Optional: Default true. If false, schema_override is required.
          database_schema_override: "" # Optional: YAML/text string of schema if auto_detect_schema is false.
          schema_summary_override: "" # Optional: Natural language summary if auto_detect_schema is false.
          query_examples: # Optional: List of natural language to SQL examples
            - natural_language: "Show all transactions for customer with customer id 41"
              sql_query: "SELECT * FROM transactions WHERE CustomerID = 41;"
          # csv_files: # Optional: List of CSV file paths to import on startup
          #   - "data/field_reports.csv"
          #   # - "/path/to/your/data/products.csv"
          csv_directories: ["data/transactions"] # Optional: List of directories to scan for CSVs

      agent_cleanup_function:
        module: "sam_sql_database.lifecycle"
        name: "cleanup_sql_agent_resources"

      # --- ADK Tools Configuration ---
      tools:
        - tool_type: python
          component_module: "sam_sql_database.tools" # Points to the plugin's tools module
          function_name: "execute_sql_query"
          # required_scopes: ["database:query"] # Example scope, if you implement authorization

      # --- Standard ADK Host Configs (Session, Artifacts, etc.) ---
      session_service:
        type: "memory" # Or "database", "vertex"
        # db_url: "${SESSION_DB_URL}" # If type is "database"
        default_behavior: "PERSISTENT" # Or "RUN_BASED"

      artifact_service:
        type: "filesystem" # Or "memory", "gcs"
        base_path: "/tmp/a2a_sql_agent_artifacts" # Required for filesystem
        artifact_scope: "app" # Or "namespace", "custom"
        # artifact_scope_value: "my_project_sql_files" # If scope is "custom"
        # bucket_name: "${ARTIFACT_GCS_BUCKET}" # If type is "gcs"

      # Enable built-in artifact tools for the LLM to use (e.g., to list artifacts it created)
      enable_builtin_artifact_tools:
        enabled: true
        # Optionally define required_scopes for each built-in artifact tool if needed

      # Agent Card, Discovery, and Inter-Agent Communication
      agent_card:
        description: "SQL Database Agent that can answer questions by querying a database of realtime transactions, including the latest credit and debit card transactions."
        defaultInputModes: ["text"]
        defaultOutputModes: ["text", "file"] # Can output text or files (artifacts)
        skills:
          - id: "sql_query"
            name: "Real Time Transactions Query"
            description: "Answers questions by querying realtime transactions from our bank customers."
            examples:
              - "Show last three transactions from Customer id 41"
              - "List the last 10 transactions"

      agent_card_publishing:
        interval_seconds: 30

      agent_discovery:
        enabled: false # Allows this agent to discover and potentially delegate to other agents

      inter_agent_communication:
        allow_list: [] # Allow delegation to any discovered agent
        deny_list: []
        request_timeout_seconds: 60
