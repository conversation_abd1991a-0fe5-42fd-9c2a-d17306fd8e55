# Solace AI Connector A2A ADK Orchestrator Component Configurations
#
# This file demonstrates various ways to configure the A2A_ADK_HostComponent.
# The app-level 'namespace' will be automatically prepended
# to queue names by the custom A2A_ADK_App class.
# The required A2A subscriptions are also automatically generated by A2A_ADK_App.
# Remember to set required environment variables (e.g., <PERSON><PERSON><PERSON>E_*, GOOGLE_API_KEY,
# GOOGLE_CLOUD_PROJECT, GOOGLE_CLOUD_LOCATION, specific LLM keys, NAMESPACE).

log:
  stdout_log_level: INFO
  log_file_level: DEBUG # Changed from INFO to DEBUG to capture ADK INFO logs
  log_file: orchestrator_agent.log

# Shared SAM config
!include ../shared_config.yaml

apps:
  - name: orchestrator_agent_app
    app_base_path: .
    app_module: solace_agent_mesh.agent.sac.app
    broker:
      <<: *broker_connection

    # App Level Config
    app_config:
      namespace: ${NAMESPACE} 
      supports_streaming: true # Host capability flag
      agent_name: "OrchestratorAgent"
      display_name: "OrchestratorAgent"
      model: *planning_model 

      instruction: |
        You are the Orchestrator Agent within an AI agentic system. Your primary responsibilities are to:
        1. Process tasks received from external sources via the system Gateway.
        2. Analyze each task to determine the optimal execution strategy:
           a. Single Agent Delegation: If the task can be fully addressed by a single peer agent (based on their declared capabilities/description), delegate the task to that agent.
           b. Multi-Agent Coordination: If task completion requires a coordinated effort from multiple peer agents: first, devise a logical execution plan (detailing the sequence of agent invocations and any necessary data handoffs). Then, manage the execution of this plan, invoking each agent in the defined order.
           c. Direct Execution: If the task is not suitable for delegation (neither to a single agent nor a multi-agent sequence) and falls within your own capabilities, execute the task yourself.

        Artifact Management Guidelines:
        - If the task requires creating an artifact (e.g., a file): create the artifact using the appropriate tools and make it immediately available to the user by using the signal_artifact_for_return tool.
        - For file creation tasks: directly proceed with writing the file content using the appropriate tool without generating a preview of the content beforehand. This is to ensure efficiency.
        - Be aware that once an artifact (like a file) is created and made available, the user will have mechanisms to access it.
        - Throughout task execution, provide regular progress updates using `status_update` embed directives. It is crucial to issue a `status_update` immediately before initiating any tool call.
        - Provide artifacts to the user by using the `signal_artifact_for_return` tool and not by embedding them in the response. Only embed them in the response if it is important to the flow of the conversation. 

      inject_system_purpose: true
      inject_response_format: true
      session_service: *default_session_service
      artifact_service: *default_artifact_service
      artifact_handling_mode: "embed" # Embed artifacts created by *this* agent
      enable_embed_resolution: true # Enable embed feature and instruction injection
      enable_artifact_content_instruction: true # Enable instruction for late-stage embed
      enable_builtin_artifact_tools: # Enable artifact tools and instruction injection
        enabled: true
      enable_builtin_data_tools: # Enable data analysis tools and instruction injection
        enabled: true
      data_tools_config: *default_data_tools_config # Use the default data tools config

      # Agent Card Definition (Simplified)
      agent_card:
        description: "The Orchestrator component. It manages tasks, and coordinating multi-agent workflows."
        defaultInputModes: [text] # Optional, Defaults to ["text"] if omitted
        defaultOutputModes: [text, file] # Indicate potential file output
        skills: [] # Keep, but now optional (defaults to empty list)
        # documentationUrl: Optional
        # provider: Optional
      # Discovery & Communication
      agent_card_publishing: 
        interval_seconds: 10
      agent_discovery: 
        enabled: true # Enable discovery and peer delegation instruction injection
      inter_agent_communication:
        allow_list: ["*"]
        
        request_timeout_seconds: 240
