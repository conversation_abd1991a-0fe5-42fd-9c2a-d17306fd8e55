{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"customer_service_assessment": {"type": "object", "properties": {"priority": {"type": "object", "properties": {"level": {"type": "string"}, "reason": {"type": "string"}}, "required": ["level", "reason"]}, "urgency": {"type": "object", "properties": {"level": {"type": "string"}, "reason": {"type": "string"}}, "required": ["level", "reason"]}, "recent_transactions": {"type": "array", "items": [{"type": "object", "properties": {"date": {"type": "string"}, "type": {"type": "string"}, "amount": {"type": "number"}}, "required": ["date", "type", "amount"]}, {"type": "object", "properties": {"date": {"type": "string"}, "type": {"type": "string"}, "amount": {"type": "number"}}, "required": ["date", "type", "amount"]}]}, "customer_location": {"type": "object", "properties": {"first_name": {"type": "string"}, "last_name": {"type": "string"}, "phone_number": {"type": "string"}, "address": {"type": "string"}, "city": {"type": "string"}, "province": {"type": "string"}, "postal_code": {"type": "string"}, "country": {"type": "string"}, "coordinates": {"type": "object", "properties": {"latitude": {"type": "number"}, "longitude": {"type": "number"}}, "required": ["latitude", "longitude"]}}, "required": ["first_name", "last_name", "phone_number", "address", "city", "province", "postal_code", "country", "coordinates"]}}, "required": ["priority", "urgency", "recent_transactions", "customer_location"]}}, "required": ["customer_service_assessment"]}