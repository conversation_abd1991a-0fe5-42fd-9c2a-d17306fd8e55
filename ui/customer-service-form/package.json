{"name": "customer-service-form", "version": "1.0.0", "description": "First Abu Dhabi Bank customer feedback form with Solace event publishing", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["customer-service", "feedback", "solace", "express", "nodejs"], "author": "", "license": "ISC", "dependencies": {"body-parser": "^2.2.0", "express": "^5.1.0", "solclientjs": "^10.18.0", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.0.1"}}