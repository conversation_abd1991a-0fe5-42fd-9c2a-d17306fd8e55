# Customer Service Form

A Node.js Express application that provides a customer feedback form for First Abu Dhabi Bank (FAB) with Solace event publishing capabilities.

## Overview

This application consists of:
1. **Express Backend**: Handles form submissions and publishes events to Solace
2. **Frontend**: HTML/CSS/JS interface styled to match First Abu Dhabi Bank's branding
3. **Event Publishing**: Integrates with Solace PubSub+ for real-time event streaming

## Features

- ✅ Customer feedback form with validation
- ✅ First Abu Dhabi Bank branded UI
- ✅ Star rating system
- ✅ Category-based feedback classification
- ✅ Real-time event publishing to Solace
- ✅ Responsive design for mobile and desktop
- ✅ JSON payload formatting with metadata

## Prerequisites

- Node.js (v14 or higher)
- npm (Node Package Manager)
- Access to a Solace PubSub+ broker

## Installation

1. **Clone or navigate to the project directory:**
   ```bash
   cd ui/customer-service-form
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

## Configuration

Before running the application, update the Solace connection parameters in `app.js`:

```javascript
const SOLACE_HOST = "wss://your-solace-host:443";
const SOLACE_VPN = "your-vpn-name";
const SOLACE_USERNAME = "your-username";
const SOLACE_PASSWORD = "your-password";
const SOLACE_TOPIC = "your/topic/path";
```

## Usage

### Start the Application

```bash
npm start
```

The application will start on port 3000 (or the port specified in the `PORT` environment variable).

### Development Mode

For development with auto-restart on file changes:

```bash
npm run dev
```

*Note: This requires nodemon to be installed as a dev dependency.*

### Access the Application

Open your browser and navigate to:
```
http://localhost:3000
```

## API Endpoints

### GET /
- **Description**: Serves the main feedback form page
- **Response**: HTML page with the customer feedback form

### POST /submit_comment
- **Description**: Processes form submissions and publishes to Solace
- **Content-Type**: `application/json`
- **Request Body**:
  ```json
  {
    "name": "Customer Name",
    "email": "<EMAIL>",
    "subject": "Feedback Subject",
    "message": "Customer feedback message",
    "category": "General|Product|Service|Technical|Other",
    "rating": "1-5"
  }
  ```
- **Response**:
  ```json
  {
    "status": "success|error",
    "message": "Response message"
  }
  ```

## Event Payload Structure

When a form is submitted, the following JSON payload is published to Solace:

```json
{
  "commentId": "uuid-v4-generated",
  "timestamp": "2025-01-01T12:00:00.000Z",
  "name": "Customer Name",
  "email": "<EMAIL>",
  "subject": "Feedback Subject",
  "message": "Customer feedback message",
  "category": "General",
  "rating": 5
}
```

## Project Structure

```
ui/customer-service-form/
├── app.js                 # Main Express application
├── package.json           # Project dependencies and scripts
├── package-lock.json      # Dependency lock file
├── README.md             # This file
├── public/               # Static files
│   ├── index.html        # Main HTML page
│   └── images/           # Image assets
└── node_modules/         # Installed dependencies
```

## Dependencies

### Production Dependencies
- **express**: Web framework for Node.js
- **body-parser**: Middleware for parsing request bodies
- **solclientjs**: Solace JavaScript API for event publishing
- **uuid**: UUID generation for unique comment IDs

### Development Dependencies
- **nodemon**: Auto-restart development server

## Environment Variables

- `PORT`: Server port (default: 3000)

## Error Handling

The application includes comprehensive error handling:
- Form validation on both client and server side
- Solace connection error handling
- Graceful shutdown on SIGINT

## Browser Support

- Modern browsers with ES6 support
- Mobile responsive design
- Tested on Chrome, Firefox, Safari, and Edge

## Contributing

1. Make changes to the codebase
2. Test locally using `npm start`
3. Ensure all form submissions work correctly
4. Verify Solace event publishing functionality

## License

ISC License

## Support

For technical support or questions about this application, please contact the development team.
