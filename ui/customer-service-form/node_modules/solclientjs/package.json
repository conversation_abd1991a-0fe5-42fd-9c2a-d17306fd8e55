{"author": "Solace Corporation", "name": "solc<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "description": "Solace Messaging API for Node.js", "keywords": ["messaging", "mq", "solace", "pubsub", "microservices"], "version": "10.18.0", "homepage": "https://solace.dev", "repository": "https://solace.dev", "license": "SEE LICENSE IN LICENSE.txt", "main": "./lib/solclientjs-exports.js", "browser": "./lib-browser/solclient.js", "devDependencies": {"@types/events": "^3.0.0", "@types/node": "^16.11.6", "long": "5.2.0"}, "optionalDependencies": {}, "engines": {"node": ">=10.0.0"}, "types": "./lib/index.d.ts"}