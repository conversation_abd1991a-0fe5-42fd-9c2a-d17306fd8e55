LICENSE SUMMARY
===============

License terms can be found at the bottom of this file.

Apache 2.0
MIT
Solace


THIRD-PARTY SOFTWARE USED
=========================

browser-util-inspect-0.2.0.tgz
------------------------------
Licensed under MIT
License terms can be found at: https://www.npmjs.org/package/browser-util-inspect
Copyright 2010-2014 Joyent, Inc
Home page: https://registry.npmjs.org/browser-util-inspect/-/browser-util-inspect-0.2.0.tgz

clone-2.1.2.tgz
---------------
Licensed under MIT
License terms can be found at: https://github.com/pvorb/clone/blob/master/LICENSE
Copyright 2011-2016 "<PERSON>" (https://paul.vorba.ch/) 
Home page: https://github.com/pvorb/node-clone#readme

fflate-0.8.1.tgz
----------------
Licensed under MIT
License terms can be found at: https://github.com/101arrowz/fflate?tab=MIT-1-ov-file#readme
Copyright 2023 Arjun Barrett
Home page: https://101arrowz.github.io/fflate

long-5.2.0.tgz
--------------
Licensed under Apache 2.0
License terms can be found at: https://www.npmjs.org/package/long
Copyright Daniel Wirtz
Home page: https://github.com/dcodeIO/long.js#readme

ws-8.18.1.tgz
-------------
Licensed under MIT
License terms can be found at: https://www.npmjs.org/package/ws
Copyright 2013 Arnout Kazemier and contributors
Copyright 2016 Luigi Pinca and contributors
Copyright 2011 Einar Otto Stangvik <<EMAIL>>
Home page: https://github.com/websockets/ws


LICENSE REQUIREMENTS & SPECIFICATIONS
======================================

Apache 2.0
----------


				 Apache License
			   Version 2.0, January 2004
			http://www.apache.org/licenses/

   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

 "License" shall mean the terms and conditions for use, reproduction,
 and distribution as defined by Sections 1 through 9 of this document.

 "Licensor" shall mean the copyright owner or entity authorized by
 the copyright owner that is granting the License.

 "Legal Entity" shall mean the union of the acting entity and all
 other entities that control, are controlled by, or are under common
 control with that entity. For the purposes of this definition,
 "control" means (i) the power, direct or indirect, to cause the
 direction or management of such entity, whether by contract or
 otherwise, or (ii) ownership of fifty percent (50%) or more of the
 outstanding shares, or (iii) beneficial ownership of such entity.

 "You" (or "Your") shall mean an individual or Legal Entity
 exercising permissions granted by this License.

 "Source" form shall mean the preferred form for making modifications,
 including but not limited to software source code, documentation
 source, and configuration files.

 "Object" form shall mean any form resulting from mechanical
 transformation or translation of a Source form, including but
 not limited to compiled object code, generated documentation,
 and conversions to other media types.

 "Work" shall mean the work of authorship, whether in Source or
 Object form, made available under the License, as indicated by a
 copyright notice that is included in or attached to the work
 (an example is provided in the Appendix below).

 "Derivative Works" shall mean any work, whether in Source or Object
 form, that is based on (or derived from) the Work and for which the
 editorial revisions, annotations, elaborations, or other modifications
 represent, as a whole, an original work of authorship. For the purposes
 of this License, Derivative Works shall not include works that remain
 separable from, or merely link (or bind by name) to the interfaces of,
 the Work and Derivative Works thereof.

 "Contribution" shall mean any work of authorship, including
 the original version of the Work and any modifications or additions
 to that Work or Derivative Works thereof, that is intentionally
 submitted to Licensor for inclusion in the Work by the copyright owner
 or by an individual or Legal Entity authorized to submit on behalf of
 the copyright owner. For the purposes of this definition, "submitted"
 means any form of electronic, verbal, or written communication sent
 to the Licensor or its representatives, including but not limited to
 communication on electronic mailing lists, source code control systems,
 and issue tracking systems that are managed by, or on behalf of, the
 Licensor for the purpose of discussing and improving the Work, but
 excluding communication that is conspicuously marked or otherwise
 designated in writing by the copyright owner as "Not a Contribution."

 "Contributor" shall mean Licensor and any individual or Legal Entity
 on behalf of whom a Contribution has been received by Licensor and
 subsequently incorporated within the Work.

 2. Grant of Copyright License. Subject to the terms and conditions of
 this License, each Contributor hereby grants to You a perpetual,
 worldwide, non-exclusive, no-charge, royalty-free, irrevocable
 copyright license to reproduce, prepare Derivative Works of,
 publicly display, publicly perform, sublicense, and distribute the
 Work and such Derivative Works in Source or Object form.

 3. Grant of Patent License. Subject to the terms and conditions of
 this License, each Contributor hereby grants to You a perpetual,
 worldwide, non-exclusive, no-charge, royalty-free, irrevocable
 (except as stated in this section) patent license to make, have made,
 use, offer to sell, sell, import, and otherwise transfer the Work,
 where such license applies only to those patent claims licensable
 by such Contributor that are necessarily infringed by their
 Contribution(s) alone or by combination of their Contribution(s)
 with the Work to which such Contribution(s) was submitted. If You
 institute patent litigation against any entity (including a
 cross-claim or counterclaim in a lawsuit) alleging that the Work
 or a Contribution incorporated within the Work constitutes direct
 or contributory patent infringement, then any patent licenses
 granted to You under this License for that Work shall terminate
 as of the date such litigation is filed.

 4. Redistribution. You may reproduce and distribute copies of the
 Work or Derivative Works thereof in any medium, with or without
 modifications, and in Source or Object form, provided that You
 meet the following conditions:

 (a) You must give any other recipients of the Work or
 Derivative Works a copy of this License; and

 (b) You must cause any modified files to carry prominent notices
 stating that You changed the files; and

 (c) You must retain, in the Source form of any Derivative Works
 that You distribute, all copyright, patent, trademark, and
 attribution notices from the Source form of the Work,
 excluding those notices that do not pertain to any part of
 the Derivative Works; and

 (d) If the Work includes a "NOTICE" text file as part of its
 distribution, then any Derivative Works that You distribute must
 include a readable copy of the attribution notices contained
 within such NOTICE file, excluding those notices that do not
 pertain to any part of the Derivative Works, in at least one
 of the following places: within a NOTICE text file distributed
 as part of the Derivative Works; within the Source form or
 documentation, if provided along with the Derivative Works; or,
 within a display generated by the Derivative Works, if and
 wherever such third-party notices normally appear. The contents
 of the NOTICE file are for informational purposes only and
 do not modify the License. You may add Your own attribution
 notices within Derivative Works that You distribute, alongside
    or as an addendum to the NOTICE text from the Work, provided
 that such additional attribution notices cannot be construed
 as modifying the License.

 You may add Your own copyright statement to Your modifications and
 may provide additional or different license terms and conditions
 for use, reproduction, or distribution of Your modifications, or
 for any such Derivative Works as a whole, provided Your use,
 reproduction, and distribution of the Work otherwise complies with
 the conditions stated in this License.

 5. Submission of Contributions. Unless You explicitly state otherwise,
 any Contribution intentionally submitted for inclusion in the Work
 by You to the Licensor shall be under the terms and conditions of
 this License, without any additional terms or conditions.
 Notwithstanding the above, nothing herein shall supersede or modify
 the terms of any separate license agreement you may have executed
 with Licensor regarding such Contributions.

 6. Trademarks. This License does not grant permission to use the trade
 names, trademarks, service marks, or product names of the Licensor,
 except as required for reasonable and customary use in describing the
 origin of the Work and reproducing the content of the NOTICE file.

 7. Disclaimer of Warranty. Unless required by applicable law or
 agreed to in writing, Licensor provides the Work (and each
 Contributor provides its Contributions) on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
 implied, including, without limitation, any warranties or conditions
 of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
 PARTICULAR PURPOSE. You are solely responsible for determining the
 appropriateness of using or redistributing the Work and assume any
 risks associated with Your exercise of permissions under this License.

 8. Limitation of Liability. In no event and under no legal theory,
 whether in tort (including negligence), contract, or otherwise,
 unless required by applicable law (such as deliberate and grossly
      negligent acts) or agreed to in writing, shall any Contributor be
 liable to You for damages, including any direct, indirect, special,
 incidental, or consequential damages of any character arising as a
 result of this License or out of the use or inability to use the
 Work (including but not limited to damages for loss of goodwill,
 work stoppage, computer failure or malfunction, or any and all
 other commercial damages or losses), even if such Contributor
 has been advised of the possibility of such damages.

 9. Accepting Warranty or Additional Liability. While redistributing
 the Work or Derivative Works thereof, You may choose to offer,
 and charge a fee for, acceptance of support, warranty, indemnity,
 or other liability obligations and/or rights consistent with this
 License. However, in accepting such obligations, You may act only
 on Your own behalf and on Your sole responsibility, not on behalf
 of any other Contributor, and only if You agree to indemnify,
 defend, and hold each Contributor harmless for any liability
 incurred by, or claims asserted against, such Contributor by reason
 of your accepting any such warranty or additional liability.

 END OF TERMS AND CONDITIONS

   APPENDIX: How to apply the Apache License to your work.

 To apply the Apache License to your work, attach the following
 boilerplate notice, with the fields enclosed by brackets "[]"
 replaced with your own identifying information. (Don't include
 the brackets!)  The text should be enclosed in the appropriate
 comment syntax for the file format. We also recommend that a
 file or class name and description of purpose be included on the
 same "printed page" as the copyright notice for easier
 identification within third-party archives.

 Copyright [yyyy] [name of copyright owner]

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

 http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.

MIT
---

MIT License

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and
associated documentation files (the "Software"), to deal in the Software without restriction,
including without limitation the rights to use, copy, modify, merge, publish, distribute,
sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or
substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.

Solace
------

SOLACE API LICENSE AGREEMENT

Version 1.1

This Solace API License Agreement Version 1.1 ("Agreement") sets forth the 
terms and conditions on which Solace Corporation ("Solace") makes available 
certain of its APIs (defined below). BY INSTALLING, DOWNLOADING, ACCESSING, OR 
USING ANY OF THE APIs, YOU AGREE TO THE TERMS AND CONDITIONS OF THIS AGREEMENT.
IF YOU DO NOT AGREE TO SUCH TERMS AND CONDITIONS, YOU MUST NOT USE THE APIs. IF
YOU ARE RECEIVING THE APIs ON BEHALF OF A LEGAL ENTITY, YOU REPRESENT AND 
WARRANT THAT YOU HAVE THE ACTUAL AUTHORITY TO AGREE TO THE TERMS AND CONDITIONS
OF THIS AGREEMENT ON BEHALF OF SUCH ENTITY.

"API" means a collection of routines, classes, function parameters, protocols, 
tools, and software (including related libraries) created by Solace that allow 
access to or interoperability with Solace products, as may be updated or 
modified by Solace from time to time.

"Application" means the software created or modified by the Licensee to enable 
its product to access or otherwise interoperate with Solace products via an 
API.

"License" means the license granted in Section 1.1.

"Licensee" means you, an individual, or the entity on whose behalf you are 
receiving the Software.

1	LICENSE GRANT AND CONDITIONS.

1.1.	License. Subject to the terms and conditions of this Agreement, Solace 
hereby grants to Licensee a non-exclusive, royalty-free, worldwide, 
non-transferable, non-sublicensable, license during the term of this Agreement 
to (a) use APIs for the sole purpose of creating Applications including the 
incorporation of API software into such Applications and (b) distribute such 
Applications to its customers for the sole purpose of enabling interoperability
between Licensee products and Solace products. 

1.2.	Conditions. In consideration of the License, Licensee's use and 
distribution of Applications is subject to the following conditions:

1.2.1.	Licensee shall not use or attempt to use the APIs (a) in violation or 
contravention of any applicable law, regulation or generally accepted practices
 or guidelines in the relevant jurisdictions or (b) other than as expressly set
forth in this Agreement.

1.2.2.	Licensee acknowledges that future updates or modifications to the APIs 
by Solace may: (a) remove or restrict previously existing functionality and/or 
(b) require the Licensee to update or modify its Applications.

1.2.3.	In any distribution of the Applications, Licensee will retain and 
reproduce in their entirety any Solace or third-party disclaimers, copyright 
notices, licenses, and other proprietary notices provided with the APIs. 
Licensee will ensure that any API software incorporated into the Application 
will remain subject to the terms and conditions set out in this Agreement.

1.2.4.	Licensee shall not reverse engineer, disassemble, decompile, translate,
or otherwise attempt to derive the source code version of any API software 
provided in object code form, except if and only to the extent expressly 
permitted by applicable law, and provided that Licensee first approaches 
Solace and seeks permission in writing.

1.3.	Publicly Available Software. The APIs may include software programs 
that are distributed by Solace pursuant to the terms and conditions of a 
license granted by the copyright owner of such software programs and which 
governs Licensee's use of such software programs ("Publicly Available 
Software"). The Licensee's use of Publicly Available Software in conjunction 
with the APIs in a manner consistent with the terms of this Agreement is 
permitted, however, the Licensee may have broader rights under the applicable 
license for Publicly Available Software and nothing contained herein is 
intended to impose restrictions or limitations on the Licensee's use of the 
Publicly Available Software.

2.	TERM AND TERMINATION. 

This Agreement will commence upon Licensee's first use of an API and will 
continue unless and until earlier terminated as set forth herein. If Licensee 
breaches any of its conditions or obligations under this Agreement, this 
Agreement and the License will terminate automatically and permanently. 

3.	INTELLECTUAL PROPERTY.

As between the parties, Solace will retain all right, title, and interest in 
the APIs, and all intellectual property rights therein. Solace hereby reserves 
all rights not expressly granted to Licensee in this Agreement. Solace hereby 
reserves all rights in its trademarks and service marks, and no licenses 
therein are granted in this Agreement. Subject to Solace's rights in the APIs, 
Solace agrees that it obtains no right, title, or interest from the Licensee 
under this Agreement in or to any Applications.

4.	DISCLAIMER.

THE APIs ARE PROVIDED "AS IS" AND "AS AVAILABLE" WITHOUT WARRANTY OF ANY KIND. 
SOLACE HEREBY DISCLAIMS ANY AND ALL WARRANTIES AND CONDITIONS, EXPRESS, 
IMPLIED, STATUTORY, OR OTHERWISE, AND SPECIFICALLY DISCLAIMS ANY WARRANTY OF 
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF 
THIRD-PARTY INTELLECTUAL PROPERTY RIGHTS, WITH RESPECT TO THE APIs.

5.	LIMITATION OF LIABILITY.

THE LICENSEE AGREES THAT SOLACE WILL HAVE NO LIABILITY WHATSOEVER FOR ANY USE 
THE LICENSEE MAKES OF THE APIs. SOLACE WILL NOT BE LIABLE FOR ANY DAMAGES OF 
ANY KIND, INCLUDING BUT NOT LIMITED TO, LOST PROFITS OR ANY CONSEQUENTIAL, 
SPECIAL, INCIDENTAL, INDIRECT, OR DIRECT DAMAGES, HOWEVER CAUSED AND ON ANY 
THEORY OF LIABILITY, ARISING OUT OF THIS AGREEMENT. THE FOREGOING SHALL APPLY 
TO THE EXTENT PERMITTED BY APPLICABLE LAW. 

6.	GENERAL.

6.1.	Governing Law. This Agreement will be governed by and interpreted in 
accordance with the laws of the Province of Ontario, without reference to its 
conflict of laws principles. If Licensee is located within Canada, all disputes
arising out of this Agreement are subject to the exclusive jurisdiction of 
courts located in Ottawa, Ontario, Canada. If Licensee is located outside of 
Canada, any dispute, controversy, or claim arising out of or relating to this 
Agreement will be referred to and finally determined by arbitration in 
accordance with the JAMS International Arbitration Rules. The tribunal will 
consist of one arbitrator. The place of arbitration will be Ottawa, Ontario, 
Canada. The language to be used in the arbitral proceedings will be English. 
Judgment upon the award rendered by the arbitrator may be entered in any court 
having jurisdiction thereof.

6.2.	Assignment. Licensee is not authorized to assign its rights under this 
Agreement to any third party. Solace may freely assign its rights under this 
Agreement to any third party.

6.3.	Other. This Agreement is the entire agreement between the parties 
regarding the subject matter hereof. No amendment or modification of this 
Agreement will be valid or binding upon the parties unless made in writing and 
signed by the duly authorized representatives of both parties. If any 
provision, including without limitation any condition, of this Agreement is 
held to be unenforceable, this Agreement and all licenses and rights granted 
hereunder will immediately terminate. Waiver by Solace of a breach of any 
provision of this Agreement or the failure by Solace to exercise any right 
hereunder will not be construed as a waiver of any subsequent breach of that 
right or as a waiver of any other right.

- End -

Version 1.1 
November 2, 2023


