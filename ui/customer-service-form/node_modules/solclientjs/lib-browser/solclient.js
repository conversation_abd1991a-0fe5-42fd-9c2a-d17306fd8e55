/*! For license information please see solclient.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("solace",[],t):"object"==typeof exports?exports.solace=t():e.solace=t()}(this,(()=>(()=>{var e={43:(e,t,n)=>{const s=n(178),r=n(5898),i=n(3450),o=n(4386),a=n(8205),{assert:c}=n(7444),{CapabilityType:u}=n(2484),{Check:l}=n(802),{CorrelatedRequest:h}=n(8165),{Destination:p}=n(9620),{ErrorResponseSubcodeMapper:d,ErrorSubcode:_,OperationError:E}=n(6706),{FsmEvent:g,State:T,StateMachine:S}=n(7414),{Hex:m}=n(9783),{LogFormatter:f}=n(2694),{Message:I,MessageOutcome:R,MessageDeliveryModeType:C}=n(6247),{P2PUtil:A}=n(7603),{SessionEvent:O}=n(8229),{SessionEventCode:N}=n(6334),{SessionEventName:y}=n(6324),{SessionFSMEvent:P}=n(1122),{SessionRequestType:D}=n(168),{SessionStateName:b}=n(6057),{SslDowngrade:M}=n(6415),{StatType:v,StatsByMode:w}=n(5747),{StringUtils:L}=n(968),{SubscriptionUpdateTimeoutMessages:U}=n(4356),{Codec:F}=n(769),{Convert:x}=n(9783),{anythingToBuffer:B}=x,{formatHexString:G}=m,{stripNullTerminate:k}=L,{STAT_TX_BYMODE_BYTES:W,STAT_TX_BYMODE_MSGS:$,STAT_RX_BYMODE_BYTES:q,STAT_RX_BYMODE_MSGS:V,STAT_TX_BYMODE_REDELIVERED:H,STAT_TX_BYMODE_BYTES_REDELIVERED:Y}=w,Q=(n(97),n(595));e.exports.SessionFSM=class extends S{constructor(e,t,n,r){super({name:"SessionFSM"});const i=this,o=this.logger=new f((function(...e){return[`[session-fsm=${i.sessionIdHex||"(N/A)"}]`,`[${i.getCurrentStateName()}]`,...e]})),{LOG_TRACE:u,LOG_DEBUG:l,LOG_INFO:h}=o;this.log=o.wrap(this.log,this),this._sessionProperties=e,this._session=t,this._sessionStatistics=n,this._hosts=r,this._consumers=new s.ConsumerFlows,this._flowInterfaceFactory=e=>({getCorrelationTag:this.getCorrelationTag.bind(this),incStat:this.incStat.bind(this),sendData:t=>this.send(t,e,!1),sendToTransport:t=>this.sendToTransport(t,e,!1),sendControl:t=>this.send(t,e,!0),enqueueRequest:this.enqueueOutstandingCorrelatedReq.bind(this),createDestinationFromDescriptor:t.createDestinationFromDescriptor.bind(t),createTemporaryDestination:t.createTemporaryDestination.bind(t),isCapable:t.isCapable.bind(t),getCapability:t.getCapability.bind(t),getCurrentStateName:this.getCurrentStateName.bind(this),updateQueueSubscription:t.updateQueueSubscription.bind(t),get sessionIdHex(){return i.sessionIdHex},get canAck(){return t.canAck}}),this._userBackpressured=!1,this.clearCurrentError(),this.initial((function(){return this.transitionTo(i.SessionDisconnected,(e=>e.getStateMachine().reset()))})),this.unhandledEventReaction((function(e){const t=i.getCurrentState();switch(e.getName()){case y.CREATE_SUBSCRIBER:return c(i._consumers,"collection has lifetime of FSM instance"),i._consumers.add(e.guaranteedFlowObject),this;case y.DISPOSE:return t.terminate((()=>i.disposeInternal()));case y.FLOW_UP:return e.guaranteedFlowObject,this;default:return e.getName(),i.getCurrentStateName(),this}})),this.SessionConnecting=new T({name:b.CONNECTING,parentContext:i},{handleTransportDestroyed(){if(i.clearConnectTimer(),i._currentHost=i._hosts.getNextHost(),null===i._currentHost)return this.transitionToExitPoint(i.SessionConnecting,"ErrorExit");const{connectWaitTimeInMsecs:e}=i._hosts;return e>0&&!i._connectWaitTimer?(i._connectWaitTimer=setTimeout((()=>{i._connectWaitTimer=null,i.processEvent(new P({name:y.CONNECT_WAIT_TIMEOUT}))}),e),this.transitionTo(i.WaitingForInterConnectTimeout)):this.transitionTo(i.WaitingForTransport)}}).entry((()=>{i.setConnectTimer()})).entryPoint("DisconnectTransport",(function(){return i._hosts.reset({wasConnected:void 0,disconnected:!0}),i._connectFailEvent=N.DISCONNECTED,i._connectSuccessEvent=N.DISCONNECTED,this.transitionTo(i.DestroyingTransport)})).entryPoint("ReconnectTransport",(function(){i._connectFailEvent=N.DOWN_ERROR,i._connectSuccessEvent=N.RECONNECTED_NOTICE;const e=0===i._sessionProperties._reconnectRetries;if(i._hosts.reset({wasConnected:!0,disconnected:e}),!e){const e=i._currentError||{},t=[e.eventText,e.responseCode,e.errorSubcode,void 0,e.eventReason];i.setPostEventAction((()=>{i.setConnectTimer(),i.emitSessionEvent(O.build(N.RECONNECTING_NOTICE,...t))}))}return this.transitionTo(i.DestroyingTransport)})).initial((()=>(i.clearCurrentError(),i._connectFailEvent=N.CONNECT_FAILED_ERROR,i._connectSuccessEvent=N.UP_NOTICE,i._hosts.resolveHosts((e=>i._hosts?e?(i.setCurrentError({errorSubcode:_.UNRESOLVED_HOSTS,eventText:e}),i._hosts.reset({disconnected:!0}),this.processEvent(new P({name:y.EXCEPTION}))):(i._hosts.reset({wasConnected:!1}),i._currentHost=i._hosts.getNextHost(),this.processEvent(new P({name:y.DNS_RESOLUTION_COMPLETE}))):null)),this.transitionTo(i.WaitingForDNS)))).reaction(y.DNS_RESOLUTION_COMPLETE,(function(){return this.transitionTo(i.WaitingForTransport)})).reaction(y.DISCONNECT,(function(){return this.transitionToEntryPoint(i.SessionConnecting,"DisconnectTransport")})).reaction(y.CONNECT_TIMEOUT,(function(){return i.setCurrentError({errorSubcode:_.TIMEOUT,eventText:"Connect timeout"}),this.transitionTo(i.DestroyingTransport)})).reaction(y.SEND_ERROR,(function(e){return h(`SEND_ERROR reached SessionConnecting. ${e}`),i.setCurrentError(e),this.transitionTo(i.DestroyingTransport)})).reaction(y.EXCEPTION,(function(e){return i.setCurrentError(e),this.transitionTo(i.DestroyingTransport)})).reaction(y.TRANSPORT_DESTROYED,(function(e){return i.setCurrentError(e),h("TRANSPORT_DESTROYED event"),this.transitionTo(i.DestroyingTransport)})).exit((()=>{i.clearConnectTimer(),i._connectWaitTimer&&(clearTimeout(i._connectWaitTimer),i._connectWaitTimer=null)})).exitPoint("ConnectedExit",(()=>(i.setPostEventAction((()=>{if(i.emitSessionEvent(O.build(i._connectSuccessEvent,`'${i._hosts.currentHostToString()}'`,200,0,null,null)),i._userBackpressured){const e=O.build(N.CAN_ACCEPT_DATA,"",null,0,null,"");i.emitSessionEvent(e),i._userBackpressured=!1}})),i.clearCurrentError(),this.transitionTo(i.SessionTransportUp)))).exitPoint("ErrorExit",(function(){return i.setPostEventAction((()=>{const e=i._currentError||{},t=[e.eventText,e.responseCode,e.errorSubcode,void 0,e.eventReason];i.emitSessionEvent(O.build(i._connectFailEvent,...t))})),this.transitionTo(i.SessionDisconnected)})),this.WaitingForDNS=new T({name:b.WAITING_FOR_DNS,parentContext:i.SessionConnecting}),this.DestroyingTransport=new T({name:b.DESTROYING_TRANSPORT,parentContext:i.SessionConnecting}).entry((()=>{h("Connecting, disposing transport"),i.clearConnectTimer(),i.destroyTransportSession("Disconnecting session",0)})).reaction(y.TRANSPORT_DESTROYED,(function(e){return i.setCurrentError(e),i.SessionConnecting.handleTransportDestroyed.call(this)})),this.WaitingForInterConnectTimeout=new T({name:b.WAITING_FOR_INTERCONNECT_TIMEOUT,parentContext:i.SessionConnecting}).reaction(y.CONNECT_WAIT_TIMEOUT,(function(){return this.transitionTo(i.WaitingForTransport)})),this.WaitingForTransport=new T({name:b.WAITING_FOR_TRANSPORT,parentContext:i.SessionConnecting}).initial((()=>{const e="Cannot establish transport session: creation failed";i.setConnectTimer(),i.clearCurrentError();try{i.initTransport()}catch(t){return this.setCurrentError({eventText:t.message===e?e:`${e}: ${t.message}`,errorSubcode:t.subcode||_.INTERNAL_ERROR,eventReason:t}),i.SessionConnecting.handleTransportDestroyed.call(this)}return this.transitionTo(i.WaitingForTransportUp)})),this.WaitingForTransportUp=new T({name:b.WAITING_FOR_TRANSPORT_UP,parentContext:this.WaitingForTransport}).entry((()=>{this._justEntered=!0})).initial((()=>{if(!this._justEntered)return this;this._justEntered=!1;const e="Cannot establish transport session: connection failed";try{const t=i._transport.connect();if(t!==a.TransportReturnCode.OK)throw new E(e,_.CONNECTION_ERROR,a.TransportReturnCode.describe(t));h(`Attempting to connect session '${i.sessionId}' to ${i._hosts.currentHostToString()}`)}catch(t){return this.setCurrentError({eventText:t.message===e?e:`${e}: ${t.message}`,errorSubcode:t.subcode||_.INTERNAL_ERROR,eventReason:t}),i.SessionConnecting.handleTransportDestroyed.call(this)}return this})).reaction(y.SEND_ERROR,(function(e){return h(`SEND_ERROR while waiting for transport up, doing nothing. ${e}`),this.internalTransition(null)})).reaction(y.TRANSPORT_UP,(function(e){if(i.sessionId=e.sessionId||"",i.sendClientCtrlLogin()===a.TransportReturnCode.OK)return this.transitionTo(i.WaitingForLogin);const t={eventText:"Failed to send Client Control Login",errorSubcode:_.LOGIN_FAILURE,responseCode:400};return i.setCurrentError(t),this.transitionTo(i.DestroyingTransport)})),this.WaitingForLogin=new T({name:b.WAITING_FOR_LOGIN,parentContext:i.SessionConnecting}).entry((()=>{i.setClientCtrlTimer()})).reaction(y.TRANSPORT_PROTOCOL_CLIENTCTRL,(function(e){const t=e.smfMsg,n=t.getResponse(),s=n.responseCode,r={responseCode:s};if(i._responseCode=s,200===s)if(i.checkNoLocal(t)){if(i.checkCompressedSsl(t))return i.updateReadonlySessionProps(t),this.transitionTo(i.WaitForTransportChange);Object.assign(r,{eventText:"Compressed TLS is not supported by the Solace Message Router",errorSubcode:_.COMPRESSED_TLS_NOT_SUPPORTED})}else Object.assign(r,{eventText:"No Local is not supported by the Solace Message Router",errorSubcode:_.NO_LOCAL_NOT_SUPPORTED});else{const e=d.getErrorSubcode(s,n.responseString),t=e===_.UNKNOWN_ERROR?_.LOGIN_FAILURE:e;h(`Login failed. Subcode: ${t} respCode: ${s} respString: ${n.responseString}`),Object.assign(r,{eventText:n.responseString,errorSubcode:t})}return i.setCurrentError(r),this.transitionTo(i.DestroyingTransport)})).reaction(y.DOWNGRADE_TIMEOUT,(function(){return i._transport.requestDowngrade&&!1!==i._transport.requestDowngrade("ClientCtrl timeout",_.TIMEOUT)?this.transitionTo(i.WaitingForTransportUp):(i.setCurrentError({eventText:"ClientCtrl timeout",errorSubcode:_.TIMEOUT}),this.transitionTo(i.DestroyingTransport))})).exit((()=>{i.clearClientCtrlTimer()})),this.WaitForTransportChange=new T({name:b.WAITING_FOR_TRANSPORT_CHANGE,parentContext:i.SessionConnecting}).initial((function(){const e=i.updateTransportCompression(function(e){this._transport=e,this.processEvent(new P({name:y.TRANSPORT_CHANGE_DONE}))}.bind(i));return null===e?this:(i._transport=e,this.transitionTo(i.ReapplyingSubscriptions))})).reaction(y.TRANSPORT_CHANGE_DONE,(function(){return this.transitionTo(i.ReapplyingSubscriptions)})),this.ReapplyingSubscriptions=new T({name:b.REAPPLYING_SUBSCRIPTIONS,parentContext:i.SessionConnecting}).entry((()=>{h("ReapplyingSubscriptions: entry"),i.copySubscriptionCacheKeys()})).initial((function(){return!0===i.reapplySubscriptions()?this.transitionTo(i.WaitForSubConfirm):this.transitionTo(i.WaitForCanAcceptData)})).reaction(y.SUBSCRIBE_TIMEOUT,(function(){const e=_.TIMEOUT;return i.setCurrentError({eventText:"Subscription timeout while reapplying",errorSubcode:e}),this.transitionTo(i.DestroyingTransport)})).exit((function(){return i.clearSubscriptionCacheKeys(),this})),this.WaitForSubConfirm=new T({name:b.WAITING_FOR_SUBCONFIRM,parentContext:i.ReapplyingSubscriptions}).reaction(y.TRANSPORT_PROTOCOL_SMP,(function(e){const t=e.smfMsg.smfHeader,n=k(e.smfMsg.encodedUtf8Subscription),s=t.pm_respcode,r=t.pm_respstr;if(200!==s){const e=d.getErrorSubcode(s,r);return h(`Waiting for subscription confirmation, got ${s} (${e}) '${r}' on subscription ${n}`),i.setCurrentError({eventText:r,responseCode:s,errorSubcode:e}),this.transitionTo(i.DestroyTransport)}return i._session.canConnectPublisher&&i._defaultPublisher&&i._defaultPublisher.isBindWaiting()?this.transitionTo(i.WaitingForMessagePublisher):this.transitionToExitPoint(i.SessionConnecting,"ConnectedExit")})),this.WaitForCanAcceptData=new T({name:b.WAITING_FOR_CAN_ACCEPT_DATA,parentContext:i.ReapplyingSubscriptions}).reaction(y.TRANSPORT_PROTOCOL_SMP,(function(e){const t=e.smfMsg.smfHeader,n=k(e.smfMsg.encodedUtf8Subscription),s=t.pm_respcode,r=t.pm_respstr;if(200!==s){const e=d.getErrorSubcode(s,r);return i.setCurrentError({eventText:r,responseCode:s,errorSubcode:e}),this.transitionTo(i.DestroyingTransport)}return h(`Unexpected 200 OK response to subscription add for ${n}`),this.internalTransition(null)})).reaction(y.TRANSPORT_CAN_ACCEPT_DATA,(function(){return!0===i.reapplySubscriptions()?this.transitionTo(i.WaitForSubConfirm):this.internalTransition(null)})),this.WaitingForMessagePublisher=new T({name:b.WAITING_FOR_PUBFLOW,parentContext:i.SessionConnecting}).entry((()=>{i._defaultPublisher.connect(),i.sendPublisherSessionUpEvent(i._defaultPublisher)})).reaction(y.FLOW_UP,(function(){return i._defaultPublisher.isBindWaiting()?this.internalTransition(null):this.transitionToExitPoint(i.SessionConnecting,"ConnectedExit")})).reaction(y.FLOW_FAILED,(function(e){return i.setCurrentError({eventText:`Guaranteed Message Publisher Failed: ${e.eventText}`,errorSubcode:_.LOGIN_FAILURE}),this.transitionTo(i.DestroyingTransport)})),this.SessionTransportUp=new T({name:b.TRANSPORT_UP,parentContext:i}).entry((function(){return i.clearConnectTimer(),this})).initial((function(){return i._session.canConnectConsumer&&(i._consumers.flows.forEach((e=>i.sendConsumerSessionUpEvent(e))),i._consumers.reconnectingFlows.forEach((e=>i.sendConsumerSessionUpEvent(e)))),this.transitionTo(i.FullyConnected)})).reaction(y.DISCONNECT,(function(){return this.transitionTo(i.SessionDisconnecting)})).reaction(y.EXCEPTION,(function(e){return i.setCurrentError(e),i.cleanupSession(),this.transitionToEntryPoint(i.SessionConnecting,"ReconnectTransport")})).reaction(y.SEND_ERROR,(function(e){return i.setCurrentError(e),i.cleanupSession(),this.transitionToEntryPoint(i.SessionConnecting,"ReconnectTransport")})).reaction(y.TRANSPORT_DESTROYED,(function(e){return i.setCurrentError(e),h("Received unsolicited TRANSPORT_DESTROYED event while transport is up"),i.cleanupSession(),this.transitionToEntryPoint(i.SessionConnecting,"ReconnectTransport")})).reaction(y.TRANSPORT_PROTOCOL_SMP,(function(e){const t=e.smfMsg.smfHeader,n=k(e.smfMsg.encodedUtf8Subscription),s=t.pm_respcode,r=t.pm_respstr;return i.handleSubscriptionUpdateError(s,r,n,void 0,!1),this.internalTransition(null)})).reaction(y.TRANSPORT_CAN_ACCEPT_DATA,(function(e){const t=O.build(N.CAN_ACCEPT_DATA,"",null,0,null,e.toString());return i.emitSessionEvent(t),i._userBackpressured=!1,this.internalTransition(null)})).reaction(y.CREATE_SUBSCRIBER,(function(e){const t=i._consumers.add(e.guaranteedFlowObject);return i.sendConsumerSessionUpEvent(t),this})).exit((function(){return i.clearKeepAlive(),this})),this.FullyConnected=new T({name:b.FULLY_CONNECTED,parentContext:i.SessionTransportUp}).entry((()=>{i._connectFailEvent=N.DOWN_ERROR,i.scheduleKeepAlive()})),this.SessionDisconnected=new T({name:b.DISCONNECTED,parentContext:i}).reaction(y.DISCONNECT,(function(){return i.setPostEventAction((()=>{i.emitSessionEvent(O.build(N.DISCONNECTED))})),this.internalTransition(null)})).reaction(y.CONNECT,(function(){return this.transitionTo(i.SessionConnecting)})).reaction(y.EXCEPTION,(function(){return this.internalTransition(null)})),this.SessionDisconnecting=new T({name:b.DISCONNECTING,parentContext:i}).initial((()=>(h(`Disconnecting session ${i}`),this.transitionTo(i.DisconnectingFlows)))).reaction(y.DISCONNECT,(function(){return this.internalTransition(null)})).reaction(y.EXCEPTION,(function(e){return i.setCurrentError({errEvent:e}),i.cleanupSession(),this.transitionToEntryPoint(i.SessionConnecting,"DisconnectTransport")})).reaction(y.TRANSPORT_DESTROYED,(function(){return h("Received unsolicited TRANSPORT_DESTROYED while disconnecting transport"),i.cleanupSession(),this.transitionToEntryPoint(i.SessionConnecting,"DisconnectTransport")})),this.DisconnectingFlows=new T({name:b.DISCONNECTING_FLOWS,parentContext:i.SessionDisconnecting},{gatherPendingFlows(){const{MessageConsumerEventName:e}=s;c(!this.isGathering),this.isGathering=!0;const t=(e,t,n)=>{if(c(e,"Trying to listen to undefined flow"),this.known.has(e))return;this.known.add(e),this.pending.add(e);const s=()=>{n.forEach((t=>e._removeListener(t,s))),this.pending.delete(e),this.isGathering||this.checkPendingFlows()};n.forEach((n=>t.call(e,n,s)));try{e._disconnectSession()}catch(e){s()}};i._consumers&&i._consumers.flows.forEach((n=>{t(n,n._once,[e.DOWN,e.DOWN_ERROR])})),this.isGathering=!1},checkPendingFlows(){this.pending,0===this.pending.size&&(this.gatherPendingFlows(),0===this.pending.size&&this.proceed())},proceed(){this.known=null,this.pending=null,h("All flows disconnected"),i.processEvent(new g({name:y.FLOWS_DISCONNECTED}))}}).entry((function(){this.known=new Set,this.pending=new Set,this.checkPendingFlows()})).reaction(y.FLOWS_DISCONNECTED,(function(){return this.transitionTo(i.FlushingTransport)})),this.FlushingTransport=new T({name:b.FLUSHING_TRANSPORT,parentContext:i.SessionDisconnecting},{flushTransport(){i.cleanupSession(),i.flushTransportSession((()=>this.onTransportFlushed())),this.sessionId=null},onTransportFlushed(){i.processEvent(new g({name:y.TRANSPORT_FLUSHED}))}}).entry((function(){h("Flushing transport"),this.flushTransport()})).reaction(y.TRANSPORT_FLUSHED,(function(){return h("Handle Transport Flushed"),this.transitionToEntryPoint(i.SessionConnecting,"DisconnectTransport")}))}addToSubscriptionCache(e){if(l.nothing(e)||!this._subscriptionCache)return;const{LOG_DEBUG:t}=this.logger,n=e.name;null===this._subscriptionCache[n]||void 0===this._subscriptionCache[n]?(this._subscriptionCache[n]=e,this._subscriptionCacheCount++):this._subscriptionCache[n]=e}cancelOutstandingCorrelatedReq(e){if(l.nothing(e)||!this._correlatedReqs)return null;const t=this._correlatedReqs[e];if(null==t)return null;const{LOG_DEBUG:n,LOG_ERROR:s}=this.logger;t.timer&&(clearTimeout(t.timer),t.timer=null);try{delete this._correlatedReqs[e]||s(`Cannot delete ctrl request ${e}`)}catch(t){s(`Cannot delete ctrl request ${e}`,t)}return t}cleanupSession(){const{LOG_INFO:e}=this.logger;e("Clean up session");const{ConsumerFSMEvent:t,ConsumerFSMEventNames:n}=s;this._correlatedReqs&&Object.keys(this._correlatedReqs).forEach((e=>this.cancelOutstandingCorrelatedReq(e))),this.clearConnectTimer(),this.clearClientCtrlTimer(),this.clearKeepAlive(),this._consumers.flows.forEach((e=>{e.processFSMEvent(new t({name:n.SESSION_DOWN}))})),this._consumers.reconnectingFlows.forEach((e=>{e.processFSMEvent(new t({name:n.SESSION_DOWN}))})),this._defaultPublisher&&this._defaultPublisher.processFSMEvent(new r.PublisherFSMEvent({name:r.PublisherFSMEventNames.SESSION_DOWN})),this._session.cleanupSession()}clearClientCtrlTimer(){this._clientCtrlTimer&&(clearTimeout(this._clientCtrlTimer),this._clientCtrlTimer=null)}clearConnectTimer(){this._connectTimer&&(clearTimeout(this._connectTimer),this._connectTimer=void 0)}clearKeepAlive(){const{LOG_INFO:e}=this.logger;this._keepAliveTimer&&(e("Cancel keepalive timer"),clearInterval(this._keepAliveTimer),this._keepAliveTimer=null),this.resetKeepAliveCounter()}checkNoLocal(e){let t=!0;if(!0===this._sessionProperties.noLocal){const n=e.getRouterCapabilities();t=!!n&&"boolean"==typeof n[u.NO_LOCAL]&&n[u.NO_LOCAL]}return t}checkCompressedSsl(e){const{LOG_TRACE:t}=this.logger;if(this._compressedTLS){const t=e.getRouterCapabilities();return!(!t||"boolean"!=typeof t[u.COMPRESSED_SSL])&&!0===t[u.COMPRESSED_SSL]}return!0}checkSessionDestinationCapability(e){let t=null;return e&&e.getType()&&e.getSubscriptionInfo()&&(e.getSubscriptionInfo().isShare||e.getSubscriptionInfo().isNoExport)&&!this._session.isCapable(u.SHARED_SUBSCRIPTIONS)&&(t=new E("Shared subscriptions are not allowed by router for this client",_.SHARED_SUBSCRIPTIONS_NOT_SUPPORTED,null)),t}clearCurrentError(){this._currentError=null}clearSubscriptionCacheKeys(){this._subscriptionCacheKeys=null}copySubscriptionCacheKeys(){this.clearSubscriptionCacheKeys(),this._subscriptionCacheKeys=Object.keys(this._subscriptionCache||{});const e=A.getP2PTopicSubscription(this._sessionProperties.p2pInboxBase);this._subscriptionCacheKeys.push(e)}createMessagePublisher(){const{LOG_DEBUG:e}=this.logger;if(!this._sessionProperties.publisherProperties.enabled)return;const{MessagePublisher:t,MessagePublisherEventName:n}=r,s=new t({properties:this._sessionProperties.publisherProperties,sessionInterfaceFactory:this._flowInterfaceFactory});s.on(n.UP,(()=>this.processEvent(new P({name:y.FLOW_UP},{guaranteedFlowObject:s})))),s.on(n.CONNECT_FAILED_ERROR,(e=>this.processEvent(new P({name:y.FLOW_FAILED},{guaranteedFlowObject:s,event:e,eventText:e.description})))),s.on(n.REJECTED_MESSAGE,((e,t)=>{const n=t.smfHeader,s=n.pm_respcode,r=n.pm_respstr,i=d.getADErrorSubcode(s,r),o=O.build(N.REJECTED_MESSAGE_ERROR,r,s,i,e.getCorrelationKey());o.message=e,this.emitSessionEvent(o)})),s.on(n.ACKNOWLEDGED_MESSAGE,(e=>{const t=O.build(N.ACKNOWLEDGED_MESSAGE,"Message(s) acknowledged",void 0,0,e.getCorrelationKey());t.message=e,this.emitSessionEvent(t)})),s.on(n.FLOW_NAME_CHANGED,(e=>{const{messages:t,count:n}=e;if(n>0){const e=O.build(N.REPUBLISHING_UNACKED_MESSAGES,`Republishing ${n} messages due to Guaranteed Message Publisher failed to reconnect`);e.messages=t,e.count=n,this.emitSessionEvent(e)}})),s.on(n.CAN_SEND,(()=>{this.emitSessionEvent(O.build(N.CAN_ACCEPT_DATA,`${s} window is now open and can send`))})),s.on(n.GUARANTEED_MESSAGING_DOWN,(()=>{this.emitSessionEvent(O.build(N.GUARANTEED_MESSAGE_PUBLISHER_DOWN,"Guaranteed Message Publishing shut down"))})),this._defaultPublisher=s}sendConsumerSessionUpEvent(e){const{ConsumerFSMEvent:t,ConsumerFSMEventNames:n}=s,r=new t({name:this._session.canConnectConsumer?n.SESSION_UP:n.SESSION_UP_NO_AD});r.guaranteedFlowObject=e,e.processFSMEvent(r)}sendPublisherSessionUpEvent(e){const t=this._session.canConnectPublisher,n=new r.PublisherFSMEvent({name:t?r.PublisherFSMEventNames.SESSION_UP:r.PublisherFSMEventNames.SESSION_UP_NO_AD});n.guaranteedFlowObject=e,e.processFSMEvent(n)}createMessageConsumer(e){const{MessageConsumer:t}=s,n=new t({properties:e,sessionInterfaceFactory:this._flowInterfaceFactory}),r=n.getProperties();if(r.topicEndpointSubscription){const e=this.checkSessionDestinationCapability(r.topicEndpointSubscription);if(e)throw e}const{LOG_WARN:i}=this.logger,o=r.requiredSettlementOutcomes;if(o&&o.length>0&&o.some((e=>e===R.FAILED||R.REJECTED))&&!this._session.isCapable(u.AD_APP_ACK_FAILED)){const e=`Session.capabilitySettlementOutcomeNotSupported: [ ${o.map((e=>`solace.MessageOutcome.${R.nameOf(e)}`)).join(", ")} ]`;throw i(e),new E(e,_.INVALID_OPERATION,null)}const a={guaranteedFlowObject:n};return this.processEvent(new P({name:y.CREATE_SUBSCRIBER},a)),n}createQueueBrowser(e){const{MessageConsumerAcknowledgeMode:t,QueueBrowser:n}=s,{LOG_DEBUG:r}=this.logger,i={};return i.queueDescriptor=e.queueDescriptor,i.acknowledgeMode=t.CLIENT,i.browser=!0,Object.prototype.hasOwnProperty.call(e,"connectTimeoutInMsecs")&&(i.connectTimeoutInMsecs=e.connectTimeoutInMsecs),Object.prototype.hasOwnProperty.call(e,"connectAttempts")&&(i.connectAttempts=e.connectAttempts),Object.prototype.hasOwnProperty.call(e,"windowSize")&&(i.windowSize=e.windowSize),Object.prototype.hasOwnProperty.call(e,"transportAcknowledgeTimeoutInMsecs")&&(i.transportAcknowledgeTimeoutInMsecs=e.transportAcknowledgeTimeoutInMsecs),Object.prototype.hasOwnProperty.call(e,"transportAcknowledgeThresholdPercentage")&&(i.transportAcknowledgeThresholdPercentage=e.transportAcknowledgeThresholdPercentage),new n(this.createMessageConsumer(i))}destroyTransportSession(e,t){if(l.nothing(this._transport))return void this.processEvent(new P({name:y.TRANSPORT_DESTROYED}));const{LOG_INFO:n,LOG_ERROR:s}=this.logger;n("Destroy transport session");const r=this._transport.destroy(e,t);this._smfClient=null,r!==a.TransportReturnCode.OK&&s(`Failed to destroy transport session, return code: ${a.TransportReturnCode.describe(r)}`)}disposeInternal(){if(this._disposed)return;const e={transport:()=>{this.destroyTransportSession("Disposing",0),this._transport=null,this._smfClient=null},session:()=>{this.cleanupSession(),this._session=null,this._sessionProperties=null,this._correlatedReqs=null,this._flowInterfaceFactory=null},statistics:()=>{this._sessionStatistics&&(this._sessionStatistics.resetStats(),this._sessionStatistics=null),this._kaStats=null},"subscription cache":()=>{this._subscriptionCache&&(Object.keys(this._subscriptionCache).forEach((e=>this.removeFromSubscriptionCache(e))),this._subscriptionCache=null),this.clearSubscriptionCacheKeys(),this._subscriptionCacheCount=0},MessagePublishers:()=>{this._defaultPublisher&&(this._defaultPublisher.dispose(),this._defaultPublisher=null)},MessageConsumers:()=>{this._consumers.disposeAll(),this._consumers=null},"host list":()=>{this._currentHost=null,this._hosts=null}};Object.keys(e).forEach((t=>{const{LOG_TRACE:n,LOG_INFO:s}=this.logger,r=e[t];try{r()}catch(e){s(`Dispose: ${t} failed:`,e,"...continuing")}})),this._disposed=!0}emitSessionEvent(e){this._session.sendEvent(e)}enqueueOutstandingCorrelatedReq(e,t,n,s,r){if(l.nothing(e))return;const{LOG_INFO:i}=this.logger;i(`Enqueue outstanding ctrl request correlationTag=${e}`);let o=null;t&&(o=setTimeout(t,n||this._sessionProperties.readTimeoutInMsecs));const a=new h(e,o,s,r);this._correlatedReqs[e]=a}errorInFsm(e,t,n=null){const{LOG_INFO:s}=this.logger,r=new P({name:y.EXCEPTION});return s(`Handling error in FSM: ${e} ${n&&n.stack}`),this.setCurrentError({eventText:e,errorSubcode:t,eventReason:n}),this.processEvent(r)}flushTransportSession(e){this._transport?this._transport.flush(e):e()}getCorrelationTag(){return this._smfClient.nextCorrelationTag()}getCurrentStateName(){const e=this.getCurrentState();return e?e===this.getFinalState()?b.DISPOSED:this.getCurrentState().getName():null}getStat(e){if(void 0!==this._sessionStatistics)return e===v.TX_TOTAL_DATA_MSGS?this._sessionStatistics.getStat(v.TX_DIRECT_MSGS)+this._sessionStatistics.getStat(v.TX_PERSISTENT_MSGS)+this._sessionStatistics.getStat(v.TX_NONPERSISTENT_MSGS):e===v.TX_TOTAL_DATA_BYTES?this._sessionStatistics.getStat(v.TX_DIRECT_BYTES)+this._sessionStatistics.getStat(v.TX_PERSISTENT_BYTES)+this._sessionStatistics.getStat(v.TX_NONPERSISTENT_BYTES):this._sessionStatistics.getStat(e)}getTransportInfo(){return l.nothing(this._transport)?"Not connected.":this._transport.getInfoStr()}handleADCtrlMessage(e,t){const n=e.getFlowId(),s=t.pm_respstr,r=t.pm_corrtag,{LOG_INFO:o,LOG_DEBUG:a,LOG_WARN:c}=this.logger;if(r){this.updateRxStats(e),o(`Handle SMF response for correlationTag ${r}`);const t=this.cancelOutstandingCorrelatedReq(r);return l.nothing(t)?this.errorInFsm(`Cannot find matching request for response: ${s}`,_.INTERNAL_ERROR):t.respRecvdCallback?(t.respRecvdCallback(e,t),this):(this.incStat(v.RX_REPLY_MSG_DISCARD),this)}let u;const h=e.msgType,{SMFAdProtocolMessageType:p}=i;switch(h){case p.CLIENTACK:case p.CLIENTNACK:case p.CLOSEPUBFLOW:this._defaultPublisher.flowId===n&&(u=this._defaultPublisher);break;default:u=this._consumers.getFlowById(n)}if(u&&!u.disposed)return this.updateRxStats(e,u),u.handleUncorrelatedControlMessage(e),this;const d=e.getResponse(),E=d?`"${d.responseCode} ${d.responseString}" `:"";return c(`Dropping ADCTRL.${i.SMFAdProtocolMessageType.describe(e.msgType)} ${E}for unknown flow ${n}`),this.incStat(v.RX_DISCARD_NO_MATCHING_CONSUMER),this}handleADTrMessage(e,t){const{LOG_DEBUG:n}=this.logger,s=t.pm_ad_flowid,r=this._consumers.getFlowById(s);return!r||r.disposed?(this.updateRxStats(e,this._sessionStatistics),this.incStat(v.RX_DISCARD_NO_MATCHING_CONSUMER),null):(this.updateRxStats(e,r),r.handleDataMessage(e),r)}handleApiSubscriptionTimeout(e,t){if(void 0===this._correlatedReqs[e]||null===this._correlatedReqs[e])return;const{LOG_INFO:n,LOG_ERROR:s}=this.logger;n(`${t||"Subscription timeout"} for correlationTag=${e}`);try{delete this._correlatedReqs[e]||s(`Cannot delete ctrl request ${e}`)}catch(t){s(`Cannot delete ctrl request ${e}, exception: ${t.message}`)}}handleClientCtrlMessage(e,t){let n;const{LOG_INFO:s}=this.logger;this.updateRxStats(e),e.msgType===i.SMFClientCtrlMessageType.LOGIN?(n=a.SMFClient.SMF_CLIENTCTRL_LOGIN_FAKE_CORRELATIONTAG,s("Handle SMF response for ClientCTRL Login")):(n=t.pm_corrtag,s(`Handle SMF response for correlationTag ${n}`));const r=this.cancelOutstandingCorrelatedReq(n);if(l.nothing(r)){const e=t.pm_respstr;return this.errorInFsm(`Cannot find matching request for response: ${e}`,_.INTERNAL_ERROR)}return r.respRecvdCallback?r.respRecvdCallback(e):(s(`Dropping ClientCtrl message due to mismatched correlation tag ${n}`),this.incStat(v.RX_REPLY_MSG_DISCARD))}handleClientCtrlResponse(e){const t=new P({name:y.TRANSPORT_PROTOCOL_CLIENTCTRL});t.smfMsg=e,this.processEvent(t)}handleClientCtrlTimeout(){const{LOG_INFO:e}=this.logger;e("ClientCtrl timeout for session");const t=new P({name:y.DOWNGRADE_TIMEOUT});this.processEvent(t)}handleConnectTimeout(){const{LOG_INFO:e}=this.logger;e("Connection timeout. Disconnecting");const t=new P({name:y.CONNECT_TIMEOUT});this.processEvent(t)}handleUpdatePropertyTimeout(e,t){const{LOG_ERROR:n}=this.logger;try{delete this._correlatedReqs[e]||n(`Cannot delete ctrl request ${e}`)}catch(t){n(`Cannot delete ctrl request ${e}, exception: ${t.message}`)}const s=O.build(N.PROPERTY_UPDATE_ERROR,t,null,_.TIMEOUT,null,null);this.sendEvent(s)}handleRejectedTrMessage(e){const t=e.pm_respcode,n=e.pm_tr_topicname_bytes,s=n?n.replace(/\0/g,""):"",r=e.pm_respstr,i=d.getErrorSubcode(t,r);this.emitSessionEvent(O.build(N.REJECTED_MESSAGE_ERROR,r,t,i,null,`Topic: ${s}`))}handleSMFMessage(e){try{const t=e.smfHeader;if(t.discardMessage)return this._sessionStatistics&&this._sessionStatistics.incStat(v.RX_DISCARD_SMF_UNKNOWN_ELEMENT),null;switch(t.smf_protocol){case i.SMFProtocol.TRMSG:return t.smf_adf?this.handleADTrMessage(e,t):(this.updateRxStats(e,this._sessionStatistics),0===t.pm_respcode?this._session.handleDataMessage(e):this.handleRejectedTrMessage(t));case i.SMFProtocol.ADCTRL:return this.handleADCtrlMessage(e,t);case i.SMFProtocol.CLIENTCTRL:return this.handleClientCtrlMessage(e,t);case i.SMFProtocol.SMP:return this.handleSMPMessage(e,t);case i.SMFProtocol.KEEPALIVE:case i.SMFProtocol.KEEPALIVEV2:return null;default:return this.handleUnknownProtocolMessage(e,t)}}catch(e){const{LOG_ERROR:t}=this.logger;return t(`Exception in handleSMFMessage, exception: ${e.stack}`),this.errorInFsm(`Exception in handleSMFMessage: ${e.message}`,e.subcode||_.INTERNAL_ERROR,e)}}handleSMFParseError(e){return this.errorInFsm(e,_.PROTOCOL_ERROR)}handleSMPMessage(e,t){this.updateRxStats(e);const n=this.cancelOutstandingCorrelatedReq(t.pm_corrtag||"");if(l.nothing(n)||l.nothing(n.respRecvdCallback)){const t=new P({name:y.TRANSPORT_PROTOCOL_SMP});return t.smfMsg=e,this.processEvent(t)}return n.respRecvdCallback(e,n)}handleSubscriptionUpdateError(e,t,n,s,r){const i=d.getErrorSubcode(e,t);i!==_.SUBSCRIPTION_ALREADY_PRESENT&&i!==_.SUBSCRIPTION_NOT_FOUND&&this.removeFromSubscriptionCache(n),this._session.handleSubscriptionUpdateError(e,t,n,s,r)}handleSubscriptionTimeout(e){const{LOG_ERROR:t}=this.logger;try{let n=!1;this._correlatedReqs&&(n=delete this._correlatedReqs[e]),n||t(`Cannot delete ctrl request ${e}`)}catch(n){t(`Cannot delete ctrl request ${e}`,n)}const n=new P({name:y.SUBSCRIBE_TIMEOUT});this.processEvent(n)}handleTransportEvent(e){const{LOG_INFO:t,LOG_WARN:n}=this.logger,s=e.getInfoStr()||"";let r;switch(t(`Receive transport event: ${e}`),e.getTransportEventCode()){case a.TransportSessionEventCode.UP_NOTICE:r=new P({name:y.TRANSPORT_UP}),r.sessionId=e.getSessionId(),this.processEvent(r);break;case a.TransportSessionEventCode.DESTROYED_NOTICE:r=new P({name:y.TRANSPORT_DESTROYED}),r.sessionId=e.getSessionId(),r.eventText=s,r.errorSubcode=e.getSubcode(),r.eventReason=e,this._smfClient=null,this._transport=null,this.processEvent(r);break;case a.TransportSessionEventCode.CAN_ACCEPT_DATA:this.GuaranteedFlowControlledRelief(),r=new P({name:y.TRANSPORT_CAN_ACCEPT_DATA}),r.sessionId=e.getSessionId(),this.processEvent(r);break;case a.TransportSessionEventCode.SEND_ERROR:r=new P({name:y.SEND_ERROR}),r.sessionId=e.getSessionId(),r.eventText=e.getInfoStr(),r.errorSubcode=e.getSubcode(),r.eventReason=e,this.processEvent(r);break;case a.TransportSessionEventCode.DATA_DECODE_ERROR:case a.TransportSessionEventCode.PARSE_FAILURE:return this.errorInFsm(e.getInfoStr(),e.getSubcode());default:n("Received unknown transport session event",e)}return!0}handleUnknownProtocolMessage(e,t){const{LOG_INFO:n,LOG_ERROR:s}=this.logger;return this.updateRxStats(e),t&&t.smf_protocol===i.SMFProtocol.TSESSION?(s(`Received transport session message instead of SMF message, protocol 0x${G(t.smf_protocol)}`),s(`Transport MessageType=${e.messageType}, target sessionId=${G(e.sessionId)}`),this.errorInFsm("Received message with unknown protocol",_.PARSE_FAILURE)):(this._sessionStatistics&&this._sessionStatistics.incStat(v.RX_DISCARD_SMF_UNKNOWN_ELEMENT),n(`Drop message with unknown protocol 0x${G(t.smf_protocol)}`),null)}incStat(e,t){return this._sessionStatistics?this._sessionStatistics.incStat(e,t):void 0}initTransport(){const{LOG_INFO:e}=this.logger,t=this._currentHost;e(`Creating transport session ${t}`),this._kaStats={lastMsgWritten:0,lastBytesWritten:0},this._smfClient=new a.SMFClient((e=>this.handleSMFMessage(e)),(e=>this.handleSMFParseError(e)),this),this._transport=a.TransportFactory.createTransport(t,(e=>this.handleTransportEvent(e)),this._smfClient,this._sessionProperties.clone(),(()=>this.sessionIdHex)),this.injectTransportInterceptor(this._transportInterceptor)}injectTransportInterceptor(e){this._transportInterceptor=e,this._transport&&this._transport.setInterceptor(e)}keepAliveTimeout(){const{LOG_TRACE:e,LOG_DEBUG:t,LOG_INFO:n}=this.logger;if(this._keepAliveCounter>=this._sessionProperties.keepAliveIntervalsLimit)return n(`Exceed maximum keep alive intervals limit ${this._sessionProperties.keepAliveIntervalsLimit}`),this._keepAliveTimer&&clearInterval(this._keepAliveTimer),this.errorInFsm("Exceed maximum keep alive intervals limit",_.KEEP_ALIVE_FAILURE);const s=this._transport.getClientStats(),r=s.msgWritten,o=s.bytesWritten,c=new i.KeepAliveMessage;return this.send(c,null,!0)!==a.TransportReturnCode.OK?this.errorInFsm("Cannot send keep alive message",_.KEEP_ALIVE_FAILURE):(this._kaStats.lastMsgWritten===r&&this._kaStats.lastBytesWritten<o||(this._keepAliveCounter++,this._kaStats.lastMsgWritten,this._kaStats.lastBytesWritten),this._keepAliveCounter,this._kaStats.lastBytesWritten=s.bytesWritten,this._kaStats.lastMsgWritten=s.msgWritten,!0)}prepareAndSendMessage(e){const{LOG_WARN:t,LOG_DEBUG:n}=this.logger;if(e instanceof I){let n;if(this._sessionProperties.payloadCompressionLevel>0&&(l.anything(e._binaryAttachment)&&e._binaryAttachment.length>0||l.anything(e._structuredContainer))){let n,s=0;try{{const t={level:this._sessionProperties.payloadCompressionLevel,flush:Q.Z_FINISH};if(l.anything(e.getSdtContainer())){const r=F.encodeSingleElement(e.getSdtContainer()),i=B(r);s=i.length,n=Q.zlibSync(i,t)}else s=e._binaryAttachment.length,n=Q.zlibSync(e._binaryAttachment,t);n=B(n)}n.length+9<s?(e._setCompressedBinaryAttachment(n),l.nothing(e.getHttpContentEncoding())?e._compressedCE="deflate":e._compressedCE=e.getHttpContentEncoding()+", deflate"):n.length}catch(e){t("Error occurred while compressing, message attachment was not compressed. Original attachment will be sent. ",e)}}const s=e.getDeliveryMode();switch(s){case C.DIRECT:if(!this._transport)return;e._payload_is_memoized=!1,e._memoized_csumm=null,e._memoized_payload=null,n=this.sendToTransport(e);break;case C.PERSISTENT:case C.NON_PERSISTENT:if(!this._defaultPublisher){const e=this._session.adLocallyDisabled?"locally disabled":"remotely unsupported";throw new E("Session does not provide Guaranteed Message Publish capability",_.GM_UNAVAILABLE,e)}this._gmSendDisallowed&&this._gmSendDisallowed(),n=this._defaultPublisher.prepareAdMessageAndSend(e);break;default:{const{LOG_ERROR:e}=this.logger;e("Unhandled message delivery mode",C.describe(s))}}if(l.anything(e._getCompressedBinaryAttachment())&&e._setCompressedBinaryAttachment(),delete e._compressedCE,n!==a.TransportReturnCode.OK){if(n===a.TransportReturnCode.NO_SPACE)throw this._userBackpressured=!0,new E("Cannot send message - no space in transport",_.INSUFFICIENT_SPACE,a.TransportReturnCode.describe(n));this.setCurrentError(new E("Cannot send message",_.INVALID_OPERATION,a.TransportReturnCode.describe(n))),this.processEvent(new P({name:y.EXCEPTION}))}}}GuaranteedFlowControlledRelief(){this._defaultPublisher&&this._defaultPublisher.processFSMEvent(new r.PublisherFSMEvent({name:r.PublisherFSMEventNames.CAN_SEND}))}reapplySubscriptions(){const{LOG_INFO:e,LOG_DEBUG:t}=this.logger,{SolclientFactory:{createTopicDestination:n}}=o;if(e(`Reapplying subscriptions, count=${this._subscriptionCacheKeys.length}`),!this._subscriptionCacheKeys)return!0;try{for(;this._subscriptionCacheKeys.length;){const e=this._subscriptionCacheKeys.shift(),t=0===this._subscriptionCacheKeys.length,s=n(e),r=this.sendSubscribe(s,t,null,this._sessionProperties.readTimeoutInMsecs,null);r!==a.TransportReturnCode.OK&&this.errorInFsm(`Error occurred sending subscription: ${a.TransportReturnCode.describe(r)}`,_.INTERNAL_ERROR)}}catch(e){if(e instanceof E&&e.subcode===_.INSUFFICIENT_SPACE)return!1;this.errorInFsm(`Unexpected expection occurred while reapplying subscriptions: ${e}`,e.subcode||_.INTERNAL_ERROR,e)}return!0}removeFromSubscriptionCache(e){if(l.nothing(e)||!this._subscriptionCache)return null;const{LOG_DEBUG:t,LOG_ERROR:n}=this.logger,s=e instanceof p?e.name:e,r=this._subscriptionCache[s];if(null==r)return null;try{delete this._subscriptionCache[s]?this._subscriptionCacheCount--:n(`Cannot remove subscription ${s}`)}catch(e){n(`Cannot remove subscription ${s}`,e)}return r}reset(){this.resetStats(),this.sessionId=null,this._keepAliveTimer=null,this.resetKeepAliveCounter(),this._correlatedReqs={},this._disposed=!1,this._smfClient=null,this._kaStats={lastMsgWritten:0,lastBytesWritten:0},this._subscriptionCache=null,this._subscriptionCacheKeys=null,this._subscriptionCacheCount=0,this._sessionProperties.reapplySubscriptions&&(this._subscriptionCache={}),this._eventCode=null,this._responseCode=null,this.eventText=null,this.errorSubcode=null,this.eventReason=null}resetKeepAliveCounter(){this._keepAliveCounter=0}resetStats(){return this._sessionStatistics?this._sessionStatistics.resetStats():void 0}scheduleKeepAlive(){const{LOG_DEBUG:e,LOG_ERROR:t}=this.logger,{keepAliveIntervalInMsecs:n}=this._sessionProperties;0!==n&&(this._keepAliveTimer&&clearInterval(this._keepAliveTimer),this._keepAliveTimer=setInterval((()=>{try{this.keepAliveTimeout()}catch(e){t("Error occurred in keepAliveTimeout",e)}}),n))}send(e,t=this._sessionStatistics,n=!1){try{return this.sendToTransport(e,t,n)}catch(e){const{LOG_TRACE:t}=this.logger;e.message,e.stack,this.errorInFsm(`Send operation failed: ${e.message}`,e.subcode||_.CONNECTION_ERROR)}return a.TransportReturnCode.CONNECTION_ERROR}sendToTransport(e,t=this._sessionStatistics,n=!1){let s=a.TransportReturnCode.CONNECTION_ERROR;if(!this._transport)throw new E("Transport has been destroyed",_.INTERNAL_ERROR);const r=i.Codec.Encode.encodeCompoundMessage(e);switch(s=this._transport.send(r,n),s){case a.TransportReturnCode.OK:this.updateTxStats(e,t);break;case a.TransportReturnCode.NO_SPACE:if(!n)break;default:throw new E(`Transport returned ${a.TransportReturnCode.describe(s)}`,_.INTERNAL_ERROR)}return s}sendClientCtrlLogin(){const{LOG_INFO:e,LOG_DEBUG:t,LOG_TRACE:n}=this.logger;this._compressedTLS=this._sessionProperties.compressionLevel>0&&null!==this._currentHost.match(/tcps:/i),this._plaintextTLS=null!==this._currentHost.match(/tcps:/i)&&this._sessionProperties.sslConnectionDowngradeTo===M.PLAINTEXT,this._plaintextTLS,this._compressedTLS;const s=i.ClientCtrlMessage.getLogin(this._sessionProperties,this._compressedTLS,this._plaintextTLS),r=this.send(s);if(r!==a.TransportReturnCode.OK)this._responseCode=null,this.eventReason=null,r===a.TransportReturnCode.NO_SPACE?(this.eventText="Cannot send client control - no space in transport",this.errorSubcode=_.INSUFFICIENT_SPACE):(e(`Cannot send client ctrl, return code\n          ${a.TransportReturnCode.describe(r)}`),this.eventText="Cannot send client ctrl",this.errorSubcode=_.INVALID_OPERATION);else{const t=a.SMFClient.SMF_CLIENTCTRL_LOGIN_FAKE_CORRELATIONTAG;e(`Using internally correlationTag=${t} for tracking ClientCTRL Login`),this.enqueueOutstandingCorrelatedReq(t,null,null,null,(e=>this.handleClientCtrlResponse(e)))}return r}sendSubscribe(e,t,n,s,r){const{LOG_INFO:o,LOG_DEBUG:u}=this.logger;c(e instanceof p,"sendSubscribe requires a Destination, not a string");const l=this.getCorrelationTag(),h=i.SMPMessage.getSubscriptionMessage(l,e,!0,t);c(h.encodedUtf8Subscription,"Encoded SMP message was invalid");const d=this.send(h);return d!==a.TransportReturnCode.OK?(o("Subscribe failed",a.TransportReturnCode.describe(d)),d):(t&&this.enqueueOutstandingCorrelatedReq(l,(()=>this.handleSubscriptionTimeout(l)),s||this._sessionProperties.readTimeoutInMsecs,n,r),d)}sendUpdateProperty(e,t,n,s,r){const o=this._smfClient.nextCorrelationTag(),c=i.ClientCtrlMessage.getUpdate(e,t,o),u=this.send(c);return u!==a.TransportReturnCode.OK||this.enqueueOutstandingCorrelatedReq(o,(()=>this.handleUpdatePropertyTimeout(o)),s||this._sessionProperties.readTimeoutInMsecs,n,r),u}provisionEndpoint(e,t,n,s){const{LOG_DEBUG:r}=this.logger,o=this._smfClient.nextCorrelationTag(),c=i.AdProtocolMessage.getCreate(e,t,o),u=this.send(c);if(u!==a.TransportReturnCode.OK)return LOG_INFO("Provision failed",a.TransportReturnCode.describe(u)),u;this.enqueueOutstandingCorrelatedReq(o,null,0,n,s)}deprovisionEndpoint(e,t,n){const{LOG_DEBUG:s}=this.logger,r=this._smfClient.nextCorrelationTag(),o=i.AdProtocolMessage.getDelete(e,r),c=this.send(o);if(c!==a.TransportReturnCode.OK)return LOG_INFO("Deprovision failed",a.TransportReturnCode.describe(c)),c;this.enqueueOutstandingCorrelatedReq(r,null,0,t,n)}setClientCtrlTimer(){this.clearClientCtrlTimer(),this._clientCtrlTimer=setTimeout((()=>this.handleClientCtrlTimeout()),this._sessionProperties.transportDowngradeTimeoutInMsecs)}setConnectTimer(){this.clearConnectTimer(),this._connectTimer=setTimeout((()=>this.handleConnectTimeout()),this._sessionProperties.connectTimeoutInMsecs)}setCurrentError(e){const t=this._currentError||{},{LOG_TRACE:n}=this.logger;Object.keys(e).forEach((n=>null!==e[n]&&void 0!==e[n]&&(null!==t[n]&&void 0!==t[n]?(t[n],e[n],!1):(t[n]=e[n],!0)))),this._currentError=t}subscriptionUpdate(e,t,n,s,r,o,c){const u=this.checkSessionDestinationCapability(e);if(u)throw u;const l=U[r]||U.default,h=r!==D.REMOVE_DTE_SUBSCRIPTION,p=r===D.ADD_SUBSCRIPTION||r===D.ADD_P2PINBOX,d=this.getCorrelationTag(),_=(h?i.SMPMessage.getSubscriptionMessage:i.AdProtocolMessage.getDTEUnsubscribeMessage)(d,e,p,t),E=this.send(_,void 0,o);return E!==a.TransportReturnCode.OK||(t&&this.enqueueOutstandingCorrelatedReq(d,(()=>this.handleApiSubscriptionTimeout(d,l)),s||this._sessionProperties.readTimeoutInMsecs,n,c),r===D.ADD_SUBSCRIPTION&&this._sessionProperties.reapplySubscriptions?this.addToSubscriptionCache(e):r===D.REMOVE_SUBSCRIPTION&&this._sessionProperties.reapplySubscriptions&&this.removeFromSubscriptionCache(e)),E}queueSubscriptionUpdate(e,t,n,s,r,o){const c=s?U[D.ADD_SUBSCRIPTION]:U[D.REMOVE_SUBSCRIPTION],u=this.getCorrelationTag(),l=i.SMPMessage.getQueueSubscriptionMessage(u,e,t,s),h=this.send(l,void 0,r);return h!==a.TransportReturnCode.OK||this.enqueueOutstandingCorrelatedReq(u,(()=>{const e=this._correlatedReqs[u];this.handleApiSubscriptionTimeout(u,c),o(null,e)}),n||this._sessionProperties.readTimeoutInMsecs,null,o),h}updateRxStats(e,t=this._sessionStatistics){if(!t)return;const n=e.smfHeader;if(!n)return;const s=n.pm_deliverymode||0,r=V[s],o=q[s],a=n.messageLength;switch(n.smf_protocol){case i.SMFProtocol.TRMSG:0===n.pm_respcode&&(t.incStat(v.RX_TOTAL_DATA_MSGS),t.incStat(r),t.incStat(v.RX_TOTAL_DATA_BYTES,a),t.incStat(o,a),n.smf_di&&t.incStat(v.RX_DISCARD_MSG_INDICATION));break;case i.SMFProtocol.CLIENTCTRL:case i.SMFProtocol.SMP:case i.SMFProtocol.KEEPALIVE:case i.SMFProtocol.KEEPALIVEV2:case i.SMFProtocol.ADCTRL:t.incStat(v.RX_CONTROL_MSGS),t.incStat(v.RX_CONTROL_BYTES,a)}}updateTxStats(e,t=this._sessionStatistics){if(!t)return;void 0!==e.getReplyTo&&e.getReplyTo()&&t.incStat(v.TX_REQUEST_SENT);const n=e.smfHeader;if(!n)return;const s=n.pm_deliverymode||0;let r=$[s],o=W[s];s!==C.DIRECT&&e.isRedelivered()&&(r=H[s],o=Y[s]);const a=n.messageLength;switch(n.smf_protocol){case i.SMFProtocol.TRMSG:t.incStat(r),t.incStat(o,a);break;case i.SMFProtocol.CLIENTCTRL:case i.SMFProtocol.SMP:case i.SMFProtocol.KEEPALIVE:case i.SMFProtocol.KEEPALIVEV2:case i.SMFProtocol.ADCTRL:t.incStat(v.TX_CONTROL_MSGS),t.incStat(v.TX_CONTROL_BYTES,a)}}updateReadonlySessionProps(e){const t=this._sessionProperties;t._setVpnNameInUse(e.getVpnNameInUseValue()||"");const n=t.virtualRouterName,s=e.getVridInUseValue()||"";t._setVirtualRouterName(s),""!==n&&n!==s&&this.handleVirtualRouterNameChange(n,s),t._setP2pInboxBase(e.getP2PTopicValue()||""),t._setP2pInboxInUse(A.getP2PInboxTopic(t.p2pInboxBase)),this._session.updateCapabilities(e.getRouterCapabilities());const r=this._session._getCapability(u.GUARANTEED_MESSAGE_PUBLISH);this._gmSendDisallowed="boolean"!=typeof r||r?null:()=>{throw new E("Sending guaranteed message is not allowed by router for this client",_.INVALID_OPERATION,null)}}handleVirtualRouterNameChange(e,t){this._consumers&&(this._consumers.flows.forEach((e=>e.onVRNChanged())),this._consumers.reconnectingFlows.forEach((e=>e.onVRNChanged()))),this.emitSessionEvent(O.build(N.VIRTUALROUTER_NAME_CHANGED,`Virtual router name is changed from ${e} to ${t}`,null,0,null,null))}get sessionIdHex(){return this.sessionId&&G(this.sessionId)||"N/A"}updateTransportCompression(e){const{LOG_TRACE:t}=this.logger;return this._plaintextTLS,this._compressedTLS,this._plaintextTLS?(a.TransportFactory.severTls(this._transport,this._compressedTLS,e),null):this._compressedTLS?a.TransportFactory.startCompression(this._transport):this._transport}}},56:(e,t,n)=>{var s=n(2195);const r=n(3450),{Flow:i,PrivateFlowEventName:o}=n(8860),{LogFormatter:a}=n(2694),{MessagePublisherEventName:c}=n(3840),{MessagePublisherProperties:u}=n(996),{PublisherFSM:l}=n(2577),{PublisherFSMEvent:h}=n(9728),{PublisherFSMEventNames:p}=n(3865),{LOG_WARN:d}=new a;e.exports.MessagePublisher=class extends i{constructor({properties:e,sessionInterfaceFactory:t}={}){super(new u(e),t,{direct:c.ACKNOWLEDGED_MESSAGE,emits:c.values}),this._fsm=this._makeFSM();const n=new a;n.formatter=function(...e){return["[message-publisher]",...e]},this.log=n.wrap(this.log,this),this._bindWaiting=!0,this.on(o.BIND_WAITING,this._onBindWaiting.bind(this)),this.on(c.CONNECT_FAILED_ERROR,this._onBindFailed.bind(this)),this.on(c.DOWN,this._onDown.bind(this)),this.on(c.UP,this._onUp.bind(this))}_onBindFailed(){this._bindWaiting=!1}_onBindWaiting(){this._bindWaiting=!0}_onDown(){this._bindWaiting=!1}_onUp(){this._bindWaiting=!1}_makeFSM(){return new l({publisher:this,name:"PublisherFSM",sessionInterface:this._sessionInterface,properties:this._properties})}get flowId(){return this._flowId}set flowId(e){this._flowId=e}get name(){return this._flowName}set name(e){this._flowName=e}get publisherId(){return this._publisherId}set publisherId(e){this._publisherId=e}get properties(){return this._properties.clone()}connect(){super.connect(),this._fsm.getCurrentState()||this._fsm.start()}_disconnectSession(){super._disconnectSession(),this.processFSMEvent(new h({name:p.FLOW_CLOSE}))}getDisposedEvent(){return c.DISPOSED}handleAck(e){this.processFSMEvent(new h({name:p.ACK},{ack:e}))}handleNack(e,t){this.processFSMEvent(new h({name:p.ACK},{nack:e,ctrlMessage:t}))}handleUncorrelatedControlMessage(e){const t=e.msgType,{SMFAdProtocolMessageType:n}=r;switch(t){case n.CLIENTACK:{const t=e.getLastMsgIdAcked();e.smfHeader.pm_respcode>299?this.handleNack(t,e):this.handleAck(t);break}case n.CLIENTNACK:{const t=e.getLastMsgIdAcked();this.handleNack(t,e);break}case n.CLOSEPUBFLOW:this.processFSMEvent(new h({name:p.FLOW_UNBOUND}));break;default:d(`Dropping unhandled AD control message for ${this}`,n.describe(t))}}prepareAdMessageAndSend(e){return this._fsm.prepareAdMessageAndSend(e)}isBindWaiting(){return this._bindWaiting}inspect(){return Object.assign(super.inspect(),{name:this.name,publisherId:this.publisherId})}toString(){return s(this)}_disposeFSM(){this.processFSMEvent(new h({name:p.DISPOSE}))}_isDisconnected(){return this._fsm.isDisconnected()}}},73:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.WebTransportState=s.new({DOWN:"WebTransportDown",CONNECTING:"WebTransportConnecting",DOWNGRADING:"WebTransportDowngrading",DESTROYING:"WebTransportDestroying",UP:"WebTransportUp"})},77:e=>{e.exports.UUID={generateUUID:function(){let e=(new Date).getTime();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(t=>{const n=(e+16*Math.random())%16|0;return e=Math.floor(e/16),("x"===t?n:3&n|8).toString(16)}))}}},85:(e,t,n)=>{const s={6.4:"10.0",6.3:"8.1",6.2:"8",6.1:"7","6.0":"Vista",5.2:"Server 2003",5.1:"XP",5.01:"2000 SP1","5.0":"2000","4.0":"4.0"},r="undefined"!=typeof window?window:n.g,i=(r.process,{product:"solclientjs",platform:"unknown",agent:"Gecko"});try{if(r.navigator){const e=e=>e.replace(/[^a-zA-Z0-9_/.]/g,"-");Object.assign(i,{platform:r.navigator.platform,agent:r.navigator.product,description:e(r.navigator.userAgent),navigator:r.navigator});const t=function(){if("undefined"==typeof navigator||!navigator)return null;const e=navigator.userAgent,t={browser:{name:"unknown",version:"0.0.0"},platform:{os:"unknown",arch:"unknown",version:"unknown"}},n=(...t)=>t.some((t=>e.indexOf(t)>=0)),r=(...e)=>e.filter(Boolean).shift();Object.assign(t,[["edge",/Edge\/([0-9._]+)/],["chrome",/(?!Chrom.*OPR)Chrom(?:e|ium)\/([0-9.]+)(:?\s|$)/],["firefox",/Firefox\/([0-9.]+)(?:\s|$)/],["opera",/Opera\/([0-9.]+)(?:\s|$)/],["opera",/OPR\/([0-9.]+)(:?\s|$)$/],["ie",/Trident\/7\.0.*rv:([0-9.]+).*\).*Gecko$/],["ie",/MSIE\s([0-9.]+);.*Trident\/[4-8].0/],["ie",/MSIE\s(7\.0)/],["bb10",/BB10;\sTouch.*Version\/([0-9.]+)/],["android",/Android\s([0-9.]+)/],["ios",/Version\/([0-9._]+).*Mobile.*Safari.*/],["safari",/Version\/([0-9._]+).*Safari/]].map((([t,n])=>{if(!n.test(e))return!1;const s=n.exec(e),r=(s&&s[1].split(/[._]/).slice(0,3)).map((e=>parseInt(e,10)));for(;r.length<3;)r.push(0);return{browser:{name:t,version:r.join(".")}}})).filter(Boolean).shift());const i=r(n("Windows Phone")&&"WindowsPhone",n("Windows")&&"Windows",n("Linux")&&"Linux",n("like Mac OS X")&&"iOS",n("OS X")&&"OSX",n("Android","Adr")&&"Android",n("BB10","RIM Tablet OS","BlackBerry")&&"BlackBerry"),o={Windows:()=>r(n("Win16")&&"3.1.1",n("Windows CE")&&"CE",n("Windows 95")&&"4.00.950",n("Windows 98; Win 9x 4.90")&&"4.90",n("Windows 98")&&"4.10",(()=>{const t=e.match(/\(.+?\)/)[0];return!!t&&r(...Object.keys(s).map((e=>{return n=e,t.indexOf(n)>=0&&s[e];var n})))})()),OSX:()=>e.match(/OS X ((\d+[._])+\d+)\b/)[1],Linux:()=>"",iOS:()=>e.match(/OS ((\d+[._])+\d+) like Mac OS X/)[1],Android:()=>e.match(/(?:Android|Adr) ((\d+[._])+\d_)/)[1],BlackBerry:()=>e.match(/(?:Version\/|RIM Tablet OS )((\d+\.)+\d+)/)[1]}[i];return t.platform.os=i||"Unknown",t.platform.version=(o&&o()||"0.0.0").replace(/_/g,"."),t}();t&&(i.agent=e(`${t.browser.name}-${t.browser.version}`),i.platform=`${i.agent}-${e(`${t.platform.os}-${t.platform.version}`)}`)}}catch(e){}const o=Object.assign({},{},i,{});e.exports.Process=o},97:()=>{},112:e=>{class t{constructor(e,t=0,n=e.length){this._arr=e,this._index=t,this._end=n}deref(){return this._arr[this._index]}incr(){return++this._index}end(){return this._index>=this._end}static makeIterator(e,n=0,s=e.length){return new t(e,n,s)}}e.exports.Iterator=t},168:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.SessionRequestType=s.new({ADD_SUBSCRIPTION:0,REMOVE_SUBSCRIPTION:1,ADD_P2PINBOX:2,REMOVE_P2PINBOX:3,REMOVE_DTE_SUBSCRIPTION:100})},177:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.MessageDeliveryModeType=s.new({DIRECT:0,PERSISTENT:1,NON_PERSISTENT:2})},178:(e,t,n)=>{const{ConsumerFlows:s}=n(8960),{ConsumerFSMEvent:r}=n(946),{ConsumerFSMEventNames:i}=n(1699),{MessageConsumer:o}=n(6598),{MessageConsumerAcknowledgeMode:a}=n(4590),{MessageConsumerEvent:c}=n(3247),{MessageConsumerEventName:u}=n(6934),{MessageConsumerProperties:l}=n(2558),{QueueBrowser:h}=n(2584),{QueueBrowserEventName:p}=n(8496),{QueueBrowserProperties:d}=n(4548);e.exports.ConsumerFlows=s,e.exports.ConsumerFSMEvent=r,e.exports.ConsumerFSMEventNames=i,e.exports.MessageConsumer=o,e.exports.MessageConsumerEvent=c,e.exports.MessageConsumerAcknowledgeMode=a,e.exports.MessageConsumerEventName=u,e.exports.MessageConsumerProperties=l,e.exports.QueueBrowser=h,e.exports.QueueBrowserEventName=p,e.exports.QueueBrowserProperties=d},185:e=>{e.exports.CacheRequestResult=class{constructor(e,t,n,s){this._returnCode=e,this._subcode=t,this._topic=n,this._error=s}getReturnCode(){return this._returnCode}getReturnSubcode(){return this._subcode}getTopic(){return this._topic}getError(){return this._error}}},199:(e,t,n)=>{const{Debug:s}=n(8348);e.exports.Debug=s},251:(e,t)=>{t.read=function(e,t,n,s,r){var i,o,a=8*r-s-1,c=(1<<a)-1,u=c>>1,l=-7,h=n?r-1:0,p=n?-1:1,d=e[t+h];for(h+=p,i=d&(1<<-l)-1,d>>=-l,l+=a;l>0;i=256*i+e[t+h],h+=p,l-=8);for(o=i&(1<<-l)-1,i>>=-l,l+=s;l>0;o=256*o+e[t+h],h+=p,l-=8);if(0===i)i=1-u;else{if(i===c)return o?NaN:1/0*(d?-1:1);o+=Math.pow(2,s),i-=u}return(d?-1:1)*o*Math.pow(2,i-s)},t.write=function(e,t,n,s,r,i){var o,a,c,u=8*i-r-1,l=(1<<u)-1,h=l>>1,p=23===r?Math.pow(2,-24)-Math.pow(2,-77):0,d=s?0:i-1,_=s?1:-1,E=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,o=l):(o=Math.floor(Math.log(t)/Math.LN2),t*(c=Math.pow(2,-o))<1&&(o--,c*=2),(t+=o+h>=1?p/c:p*Math.pow(2,1-h))*c>=2&&(o++,c/=2),o+h>=l?(a=0,o=l):o+h>=1?(a=(t*c-1)*Math.pow(2,r),o+=h):(a=t*Math.pow(2,h-1)*Math.pow(2,r),o=0));r>=8;e[n+d]=255&a,d+=_,a/=256,r-=8);for(o=o<<r|a,u+=r;u>0;e[n+d]=255&o,d+=_,o/=256,u-=8);e[n+d-_]|=128*E}},261:(e,t,n)=>{const s=n(9005),{WebSocketTransportSession:r}=n(9990),{HTTPConnection:i}=s,o={webSocket:()=>r.browserSupportsBinaryWebSockets(),xhrBinary:()=>i.browserSupportsXhrBinary(),streaming:()=>i.browserSupportsStreamingResponse()};e.exports.WebTransportCapabilities=o},299:(e,t,n)=>{const s=n(3884),r=n(968),{EncodeHeader:i}=n(2224),{SDTDataTypes:o}=n(8605),{SDTMapContainer:a}=n(7449),{encodeHeader:c}=i,u={encodeMap(e){const t=[];if(!(e instanceof a))return null;const n=e.getKeys();let i,u=null,l=null,h=null;for(i=0;i<n.length;i++)u=e.getField(n[i]),u&&(h=r.StringUtils.nullTerminate(n[i]),l=c(o.String,h.length),l+=h,t.push(l),s.EncodeSingleElement.encodeSingleElementToBuf(u,t));return t.join("")}};e.exports.EncodeMap=u},319:(e,t,n)=>{const{StringBuffer:s}=n(9710),r=(()=>{const e=[];for(let t=0;t<256;++t)e[t]=t<33||t>126?".":String.fromCharCode(t);return e})();function i(e,t,n,r=" "){if("string"!=typeof e)return e;if(e.length>=t)return e;const i=new s;for(let n=0;n<t-e.length;n++)i.append(r.charAt(0));switch(n){case 0:return`${i}${e}`;case 1:return`${e}${i}`;default:return e}}function o(e,t){if(!t.length)return e;const n=t.match(/^\s*/)[0].length;return n<e?n:e}function a(e){return`${e.charAt(0).toUpperCase()}${e.substr(1)}`}function c(e){return null==e||0===e.length}const u={capitalize:function(e){return e&&e.length?e.split(" ").map(a).join(" "):e}};u.isEmpty=c,u.notEmpty=function(e){return!c(e)},u.toSafeChars=function(e){return e.replace(/[^a-zA-Z0-9_/.]/g,"")},u.padLeft=function(e,t,n){return i(e,t,0,n)},u.padRight=function(e,t,n){return i(e,t,1,n)},u.nullTerminate=function(e){if(null==e)throw new Error("non str in nullTerminate");return 0===e.charCodeAt(e.length-1)?e:e+String.fromCharCode(0)},u.stripNullTerminate=function(e){if(null==e)throw new Error("null str in stripNullTerminate");return 0===e.charCodeAt(e.length-1)?e.substr(0,e.length-1):e},u.hexdump=function(e){const t=new s,n=new s,o=e=>8===e||16===e?"  ":" ";let a=0;for(let s=0,c=e.length;s<c;s++){const c=e.charCodeAt(s);if(t.append(i(c.toString(16),2,0)),n.append(r[c]||"."),t.append(o(++a)),s===e.length-1)for(;a<16;)t.append(`  ${o(++a)}`);16===a&&(t.append(n.join("")),t.append("\n"),a=0,n.clear())}return t.toString()},u.heredoc=function(e,...t){const n=[...t,""],s=e.map((e=>e+n.shift())).join("").split(/\r?\n/),r=1===s.length?0:s.reduce(o,1/0);for(;""===s[0];)s.shift();return s.map((e=>e.substring(r))).join("\n")},e.exports.StringUtils=u},343:(e,t,n)=>{const{Process:s}=n(968),r=Math.pow(2,32);function i(e,t){return t>e.length?"0".repeat(t-e.length)+e:e}const o={sessionCounter:0,idCounter:0,RandId:i((Math.random()*r).toFixed(0).toString(),10),NextSessionCounter(){return i((++this.sessionCounter).toString(),4)},NextId(){return++this.idCounter},GenerateClientName(){const{product:e,platform:t}=s;return`${e}/${t}/${this.RandId}/${this.NextSessionCounter()}`},GenerateUserIdentification(){const{product:e,platform:t}=s;return`${e}/${t}/${this.RandId}`},GenerateClientDescription:()=>`solclientjs/${s.description}`.substring(0,254)};e.exports.GlobalContext=o},383:(e,t,n)=>{const{ErrorSubcode:s,OperationError:r}=n(6706),{TransportReturnCode:i}=n(9944);class o{constructor(e,t,n,s,r=null){this._url=e,this._ssl=o.useSsl(e),this._client=n,this._eventCB=t,this._props=s,this.setInterceptor(r)}connect(){return i.OK}destroy(e,t,n){return i.OK}forceFailure(e){return i.OK}flush(e){return e(),i.OK}send(e,t=!1){return i.OK}getTransportProtocol(){return this._props.transportProtocol}getInfoStr(){return null}getClientStats(){return null}beginDowngrade(e,t){return!1}setInterceptor(e){this._interceptor&&this._interceptor.removed&&this._interceptor.removed(this),this._interceptor=e,e&&e.installed&&e.installed(this)}toString(){return`${this.getTransportProtocol()}${this._ssl?" (SSL)":""}`}static useSsl(e){const t=(e||"").split("://");if(0===t.length||o.validSchemes.indexOf(t[0])<0)throw new r(`Invalid url "${e}": Only [${o.validSchemes.join(", ")}] URL schemes are supported`,s.PARAMETER_OUT_OF_RANGE);return"https"===t[0]||"wss"===t[0]||"tcps"===t[0]}}o.validSchemes=["http","https","ws","wss","tcp","tcps"],e.exports.TransportBase=o},394:(e,t,n)=>{const s=n(6247),{BidiMap:r}=n(7444);e.exports.PriorityUserCosMap=class extends r{constructor(){super([s.MessageUserCosType.COS1,0],[s.MessageUserCosType.COS2,1],[s.MessageUserCosType.COS3,2])}}},427:e=>{class t{constructor(e,t,n){this.reset(),this._rxSmfCB=e,this._rxMessageErrorCB=t,this._session=n}reset(){this._correlationCounter=0}nextCorrelationTag(){return++this._correlationCounter>=t.SMF_MAX_CORRELATION&&(this._correlationCounter=1),this._correlationCounter?this._correlationCounter:0}}t.SMF_MAX_CORRELATION=16777215,e.exports.BaseSMFClient=t},429:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.CacheReturnCode=s.new({OK:1,FAIL:2,INCOMPLETE:3})},475:(e,t,n)=>{const{APIPropertiesValidators:s}=n(968),{MessagePublisherAcknowledgeMode:r}=n(6e3),{validateInstance:i,valBoolean:o,valIsMember:a,valNumber:c,valRange:u}=s,l={validate(e){const t=i.bind(null,"MessagePublisherProperties",e);t("enabled",[o]),t("windowSize",[c],[u,1,255]),t("acknowledgeTimeoutInMsecs",[c],[u,20,6e4]),t("acknowledgeMode",[a,r,"MessagePublisherAcknowledgeMode"]),t("connectRetryCount",[c],[u,0,Number.MAX_VALUE]),t("connectTimeoutInMsecs",[c],[u,50,Number.MAX_VALUE])}};e.exports.MessagePublisherPropertiesValidator=l},530:(e,t,n)=>{const s=n(9620),r=n(7449),i=n(5711),o=n(802),{ErrorSubcode:a,OperationError:c}=n(6706),{SDTFieldType:u}=n(7849),l=(()=>{const e=[];return e[u.BOOL]="boolean",e[u.UINT8]="number",e[u.INT8]="number",e[u.UINT16]="number",e[u.INT16]="number",e[u.UINT32]="number",e[u.INT32]="number",e[u.UINT64]="number",e[u.INT64]="number",e[u.WCHAR]="string",e[u.STRING]="string",e[u.BYTEARRAY]="object",e[u.FLOATTYPE]="number",e[u.DOUBLETYPE]="number",e})();function h(e){return new c(`Invalid SDT type:value combination, expected value type ${e}`,a.PARAMETER_INVALID_TYPE)}e.exports.validateSdtField=function(e,t){return l[e]&&("boolean"===l[e]&&"boolean"!=typeof t||"number"===l[e]&&"number"!=typeof t||"string"===l[e]&&"string"!=typeof t)?h(l[e]):e!==u.MAP||o.Check.instanceOf(t,r.SDTMapContainer)?e!==u.STREAM||o.Check.instanceOf(t,i.SDTStreamContainer)?e!==u.DESTINATION||o.Check.instanceOf(t,s.Destination)?null:h("Destination"):h("SDTStreamContainer"):h("SDTMapContainer")}},568:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.MessageOutcome=s.new({ACCEPTED:0,FAILED:1,REJECTED:3})},595:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n={},s=function(e,t,s,r,i){var o=new Worker(n[t]||(n[t]=URL.createObjectURL(new Blob([e+';addEventListener("error",function(e){e=e.error;postMessage({$e$:[e.message,e.code,e.stack]})})'],{type:"text/javascript"}))));return o.onmessage=function(e){var t=e.data,n=t.$e$;if(n){var s=new Error(n[0]);s.code=n[1],s.stack=n[2],i(s,null)}else i(null,t)},o.postMessage(s,r),o},r=Uint8Array,i=Uint16Array,o=Int32Array,a=new r([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),c=new r([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),u=new r([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),l=function(e,t){for(var n=new i(31),s=0;s<31;++s)n[s]=t+=1<<e[s-1];var r=new o(n[30]);for(s=1;s<30;++s)for(var a=n[s];a<n[s+1];++a)r[a]=a-n[s]<<5|s;return{b:n,r}},h=l(a,2),p=h.b,d=h.r;p[28]=258,d[258]=28;for(var _=l(c,0),E=_.b,g=_.r,T=new i(32768),S=0;S<32768;++S){var m=(43690&S)>>1|(21845&S)<<1;m=(61680&(m=(52428&m)>>2|(13107&m)<<2))>>4|(3855&m)<<4,T[S]=((65280&m)>>8|(255&m)<<8)>>1}var f=function(e,t,n){for(var s=e.length,r=0,o=new i(t);r<s;++r)e[r]&&++o[e[r]-1];var a,c=new i(t);for(r=1;r<t;++r)c[r]=c[r-1]+o[r-1]<<1;if(n){a=new i(1<<t);var u=15-t;for(r=0;r<s;++r)if(e[r])for(var l=r<<4|e[r],h=t-e[r],p=c[e[r]-1]++<<h,d=p|(1<<h)-1;p<=d;++p)a[T[p]>>u]=l}else for(a=new i(s),r=0;r<s;++r)e[r]&&(a[r]=T[c[e[r]-1]++]>>15-e[r]);return a},I=new r(288);for(S=0;S<144;++S)I[S]=8;for(S=144;S<256;++S)I[S]=9;for(S=256;S<280;++S)I[S]=7;for(S=280;S<288;++S)I[S]=8;var R=new r(32);for(S=0;S<32;++S)R[S]=5;var C=f(I,9,0),A=f(I,9,1),O=f(R,5,0),N=f(R,5,1),y=function(e){for(var t=e[0],n=1;n<e.length;++n)e[n]>t&&(t=e[n]);return t},P=function(e,t,n){var s=t/8|0;return(e[s]|e[s+1]<<8)>>(7&t)&n},D=function(e,t){var n=t/8|0;return(e[n]|e[n+1]<<8|e[n+2]<<16)>>(7&t)},b=function(e){return(e+7)/8|0},M=function(e,t,n){return(null==t||t<0)&&(t=0),(null==n||n>e.length)&&(n=e.length),new r(e.subarray(t,n))};t.FlateErrorCode={UnexpectedEOF:0,InvalidBlockType:1,InvalidLengthLiteral:2,InvalidDistance:3,StreamFinished:4,NoStreamHandler:5,InvalidHeader:6,NoCallback:7,InvalidUTF8:8,ExtraFieldTooLong:9,InvalidDate:10,FilenameTooLong:11,StreamFinishing:12,InvalidZipData:13,UnknownCompressionMethod:14};var v=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],w=function(e,t,n){var s=new Error(t||v[e]);if(s.code=e,Error.captureStackTrace&&Error.captureStackTrace(s,w),!n)throw s;return s},L=function(e,t,n,s){var i=e.length,o=s?s.length:0;if(!i||t.f&&!t.l)return n||new r(0);var l=!n,h=l||2!=t.i,d=t.i;l&&(n=new r(3*i));var _=function(e){var t=n.length;if(e>t){var s=new r(Math.max(2*t,e));s.set(n),n=s}},g=t.f||0,T=t.p||0,S=t.b||0,m=t.l,I=t.d,R=t.m,C=t.n,O=8*i;do{if(!m){g=P(e,T,1);var v=P(e,T+1,3);if(T+=3,!v){var L=e[(H=b(T)+4)-4]|e[H-3]<<8,U=H+L;if(U>i){d&&w(0);break}h&&_(S+L),n.set(e.subarray(H,U),S),t.b=S+=L,t.p=T=8*U,t.f=g;continue}if(1==v)m=A,I=N,R=9,C=5;else if(2==v){var F=P(e,T,31)+257,x=P(e,T+10,15)+4,B=F+P(e,T+5,31)+1;T+=14;for(var G=new r(B),k=new r(19),W=0;W<x;++W)k[u[W]]=P(e,T+3*W,7);T+=3*x;var $=y(k),q=(1<<$)-1,V=f(k,$,1);for(W=0;W<B;){var H,Y=V[P(e,T,q)];if(T+=15&Y,(H=Y>>4)<16)G[W++]=H;else{var Q=0,X=0;for(16==H?(X=3+P(e,T,3),T+=2,Q=G[W-1]):17==H?(X=3+P(e,T,7),T+=3):18==H&&(X=11+P(e,T,127),T+=7);X--;)G[W++]=Q}}var K=G.subarray(0,F),j=G.subarray(F);R=y(K),C=y(j),m=f(K,R,1),I=f(j,C,1)}else w(1);if(T>O){d&&w(0);break}}h&&_(S+131072);for(var z=(1<<R)-1,Z=(1<<C)-1,J=T;;J=T){var ee=(Q=m[D(e,T)&z])>>4;if((T+=15&Q)>O){d&&w(0);break}if(Q||w(2),ee<256)n[S++]=ee;else{if(256==ee){J=T,m=null;break}var te=ee-254;if(ee>264){var ne=a[W=ee-257];te=P(e,T,(1<<ne)-1)+p[W],T+=ne}var se=I[D(e,T)&Z],re=se>>4;if(se||w(3),T+=15&se,j=E[re],re>3&&(ne=c[re],j+=D(e,T)&(1<<ne)-1,T+=ne),T>O){d&&w(0);break}h&&_(S+131072);var ie=S+te;if(S<j){var oe=o-j,ae=Math.min(j,ie);for(oe+S<0&&w(3);S<ae;++S)n[S]=s[oe+S]}for(;S<ie;++S)n[S]=n[S-j]}}t.l=m,t.p=J,t.b=S,t.f=g,m&&(g=1,t.m=R,t.d=I,t.n=C)}while(!g);return S!=n.length&&l?M(n,0,S):n.subarray(0,S)},U=function(e,t,n){n<<=7&t;var s=t/8|0;e[s]|=n,e[s+1]|=n>>8},F=function(e,t,n){n<<=7&t;var s=t/8|0;e[s]|=n,e[s+1]|=n>>8,e[s+2]|=n>>16},x=function(e,t){for(var n=[],s=0;s<e.length;++s)e[s]&&n.push({s,f:e[s]});var o=n.length,a=n.slice();if(!o)return{t:V,l:0};if(1==o){var c=new r(n[0].s+1);return c[n[0].s]=1,{t:c,l:1}}n.sort((function(e,t){return e.f-t.f})),n.push({s:-1,f:25001});var u=n[0],l=n[1],h=0,p=1,d=2;for(n[0]={s:-1,f:u.f+l.f,l:u,r:l};p!=o-1;)u=n[n[h].f<n[d].f?h++:d++],l=n[h!=p&&n[h].f<n[d].f?h++:d++],n[p++]={s:-1,f:u.f+l.f,l:u,r:l};var _=a[0].s;for(s=1;s<o;++s)a[s].s>_&&(_=a[s].s);var E=new i(_+1),g=B(n[p-1],E,0);if(g>t){s=0;var T=0,S=g-t,m=1<<S;for(a.sort((function(e,t){return E[t.s]-E[e.s]||e.f-t.f}));s<o;++s){var f=a[s].s;if(!(E[f]>t))break;T+=m-(1<<g-E[f]),E[f]=t}for(T>>=S;T>0;){var I=a[s].s;E[I]<t?T-=1<<t-E[I]++-1:++s}for(;s>=0&&T;--s){var R=a[s].s;E[R]==t&&(--E[R],++T)}g=t}return{t:new r(E),l:g}},B=function(e,t,n){return-1==e.s?Math.max(B(e.l,t,n+1),B(e.r,t,n+1)):t[e.s]=n},G=function(e){for(var t=e.length;t&&!e[--t];);for(var n=new i(++t),s=0,r=e[0],o=1,a=function(e){n[s++]=e},c=1;c<=t;++c)if(e[c]==r&&c!=t)++o;else{if(!r&&o>2){for(;o>138;o-=138)a(32754);o>2&&(a(o>10?o-11<<5|28690:o-3<<5|12305),o=0)}else if(o>3){for(a(r),--o;o>6;o-=6)a(8304);o>2&&(a(o-3<<5|8208),o=0)}for(;o--;)a(r);o=1,r=e[c]}return{c:n.subarray(0,s),n:t}},k=function(e,t){for(var n=0,s=0;s<t.length;++s)n+=e[s]*t[s];return n},W=function(e,t,n){var s=n.length,r=b(t+2);e[r]=255&s,e[r+1]=s>>8,e[r+2]=255^e[r],e[r+3]=255^e[r+1];for(var i=0;i<s;++i)e[r+i+4]=n[i];return 8*(r+4+s)},$=function(e,t,n,s,r,o,l,h,p,d,_){U(t,_++,n),++r[256];for(var E=x(r,15),g=E.t,T=E.l,S=x(o,15),m=S.t,A=S.l,N=G(g),y=N.c,P=N.n,D=G(m),b=D.c,M=D.n,v=new i(19),w=0;w<y.length;++w)++v[31&y[w]];for(w=0;w<b.length;++w)++v[31&b[w]];for(var L=x(v,7),B=L.t,$=L.l,q=19;q>4&&!B[u[q-1]];--q);var V,H,Y,Q,X=d+5<<3,K=k(r,I)+k(o,R)+l,j=k(r,g)+k(o,m)+l+14+3*q+k(v,B)+2*v[16]+3*v[17]+7*v[18];if(p>=0&&X<=K&&X<=j)return W(t,_,e.subarray(p,p+d));if(U(t,_,1+(j<K)),_+=2,j<K){V=f(g,T,0),H=g,Y=f(m,A,0),Q=m;var z=f(B,$,0);for(U(t,_,P-257),U(t,_+5,M-1),U(t,_+10,q-4),_+=14,w=0;w<q;++w)U(t,_+3*w,B[u[w]]);_+=3*q;for(var Z=[y,b],J=0;J<2;++J){var ee=Z[J];for(w=0;w<ee.length;++w){var te=31&ee[w];U(t,_,z[te]),_+=B[te],te>15&&(U(t,_,ee[w]>>5&127),_+=ee[w]>>12)}}}else V=C,H=I,Y=O,Q=R;for(w=0;w<h;++w){var ne=s[w];if(ne>255){F(t,_,V[257+(te=ne>>18&31)]),_+=H[te+257],te>7&&(U(t,_,ne>>23&31),_+=a[te]);var se=31&ne;F(t,_,Y[se]),_+=Q[se],se>3&&(F(t,_,ne>>5&8191),_+=c[se])}else F(t,_,V[ne]),_+=H[ne]}return F(t,_,V[256]),_+H[256]},q=new o([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),V=new r(0),H=function(e,t,n,s,u,l){var h=l.z||e.length,p=new r(s+h+5*(1+Math.ceil(h/7e3))+u),_=p.subarray(s,p.length-u),E=l.l,T=7&(l.r||0);if(t){T&&(_[0]=l.r>>3);for(var S=q[t-1],m=S>>13,f=8191&S,I=(1<<n)-1,R=l.p||new i(32768),C=l.h||new i(I+1),A=Math.ceil(n/3),O=2*A,N=function(t){return(e[t]^e[t+1]<<A^e[t+2]<<O)&I},y=new o(25e3),P=new i(288),D=new i(32),v=0,w=0,L=l.i||0,U=0,F=l.w||0,x=0;L+2<h;++L){var B=N(L),G=32767&L,k=C[B];if(R[G]=k,C[B]=G,F<=L){var V=h-L;if((v>7e3||U>24576)&&(V>423||!E)){T=$(e,_,0,y,P,D,w,U,x,L-x,T),U=v=w=0,x=L;for(var H=0;H<286;++H)P[H]=0;for(H=0;H<30;++H)D[H]=0}var Y=2,Q=0,X=f,K=G-k&32767;if(V>2&&B==N(L-K))for(var j=Math.min(m,V)-1,z=Math.min(32767,L),Z=Math.min(258,V);K<=z&&--X&&G!=k;){if(e[L+Y]==e[L+Y-K]){for(var J=0;J<Z&&e[L+J]==e[L+J-K];++J);if(J>Y){if(Y=J,Q=K,J>j)break;var ee=Math.min(K,J-2),te=0;for(H=0;H<ee;++H){var ne=L-K+H&32767,se=ne-R[ne]&32767;se>te&&(te=se,k=ne)}}}K+=(G=k)-(k=R[G])&32767}if(Q){y[U++]=268435456|d[Y]<<18|g[Q];var re=31&d[Y],ie=31&g[Q];w+=a[re]+c[ie],++P[257+re],++D[ie],F=L+Y,++v}else y[U++]=e[L],++P[e[L]]}}for(L=Math.max(L,F);L<h;++L)y[U++]=e[L],++P[e[L]];T=$(e,_,E,y,P,D,w,U,x,L-x,T),E||(l.r=7&T|_[T/8|0]<<3,T-=7,l.h=C,l.p=R,l.i=L,l.w=F)}else{for(L=l.w||0;L<h+E;L+=65535){var oe=L+65535;oe>=h&&(_[T/8|0]=E,oe=h),T=W(_,T+1,e.subarray(L,oe))}l.i=h}return M(p,0,s+b(T)+u)},Y=function(){for(var e=new Int32Array(256),t=0;t<256;++t){for(var n=t,s=9;--s;)n=(1&n&&-306674912)^n>>>1;e[t]=n}return e}(),Q=function(){var e=-1;return{p:function(t){for(var n=e,s=0;s<t.length;++s)n=Y[255&n^t[s]]^n>>>8;e=n},d:function(){return~e}}},X=function(){var e=1,t=0;return{p:function(n){for(var s=e,r=t,i=0|n.length,o=0;o!=i;){for(var a=Math.min(o+2655,i);o<a;++o)r+=s+=n[o];s=(65535&s)+15*(s>>16),r=(65535&r)+15*(r>>16)}e=s,t=r},d:function(){return(255&(e%=65521))<<24|(65280&e)<<8|(255&(t%=65521))<<8|t>>8}}},K=function(e,t,n,s,i){if(!i&&(i={l:1},t.dictionary)){var o=t.dictionary.subarray(-32768),a=new r(o.length+e.length);a.set(o),a.set(e,o.length),e=a,i.w=o.length}return H(e,null==t.level?6:t.level,null==t.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(e.length)))):12+t.mem,n,s,i)},j=function(e,t){var n={};for(var s in e)n[s]=e[s];for(var s in t)n[s]=t[s];return n},z=function(e,t,n){for(var s=e(),r=e.toString(),i=r.slice(r.indexOf("[")+1,r.lastIndexOf("]")).replace(/\s+/g,"").split(","),o=0;o<s.length;++o){var a=s[o],c=i[o];if("function"==typeof a){t+=";"+c+"=";var u=a.toString();if(a.prototype)if(-1!=u.indexOf("[native code]")){var l=u.indexOf(" ",8)+1;t+=u.slice(l,u.indexOf("(",l))}else for(var h in t+=u,a.prototype)t+=";"+c+".prototype."+h+"="+a.prototype[h].toString();else t+=u}else n[c]=a}return t},Z=[],J=function(e,t,n,r){if(!Z[n]){for(var i="",o={},a=e.length-1,c=0;c<a;++c)i=z(e[c],i,o);Z[n]={c:z(e[a],i,o),e:o}}var u=j({},Z[n].e);return s(Z[n].c+";onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage="+t.toString()+"}",n,u,function(e){var t=[];for(var n in e)e[n].buffer&&t.push((e[n]=new e[n].constructor(e[n])).buffer);return t}(u),r)},ee=function(){return[r,i,o,a,c,u,p,E,A,N,T,v,f,y,P,D,b,M,w,L,De,oe,ae]},te=function(){return[r,i,o,a,c,u,d,g,C,I,O,R,T,q,V,f,U,F,x,B,G,k,W,$,b,M,H,K,Oe,oe]},ne=function(){return[Ee,Se,_e,Q,Y]},se=function(){return[ge,Te]},re=function(){return[me,_e,X]},ie=function(){return[fe]},oe=function(e){return postMessage(e,[e.buffer])},ae=function(e){return e&&{out:e.size&&new r(e.size),dictionary:e.dictionary}},ce=function(e,t,n,s,r,i){var o=J(n,s,r,(function(e,t){o.terminate(),i(e,t)}));return o.postMessage([e,t],t.consume?[e.buffer]:[]),function(){o.terminate()}},ue=function(e){return e.ondata=function(e,t){return postMessage([e,t],[e.buffer])},function(t){return e.push(t.data[0],t.data[1])}},le=function(e,t,n,s,r,i){var o,a=J(e,s,r,(function(e,n){e?(a.terminate(),t.ondata.call(t,e)):Array.isArray(n)?(n[1]&&a.terminate(),t.ondata.call(t,e,n[0],n[1])):i(n)}));a.postMessage(n),t.push=function(e,n){t.ondata||w(5),o&&t.ondata(w(4,0,1),null,!!n),a.postMessage([e,o=n],[e.buffer])},t.terminate=function(){a.terminate()}},he=function(e,t){return e[t]|e[t+1]<<8},pe=function(e,t){return(e[t]|e[t+1]<<8|e[t+2]<<16|e[t+3]<<24)>>>0},de=function(e,t){return pe(e,t)+4294967296*pe(e,t+4)},_e=function(e,t,n){for(;n;++t)e[t]=n,n>>>=8},Ee=function(e,t){var n=t.filename;if(e[0]=31,e[1]=139,e[2]=8,e[8]=t.level<2?4:9==t.level?2:0,e[9]=3,0!=t.mtime&&_e(e,4,Math.floor(new Date(t.mtime||Date.now())/1e3)),n){e[3]=8;for(var s=0;s<=n.length;++s)e[s+10]=n.charCodeAt(s)}},ge=function(e){31==e[0]&&139==e[1]&&8==e[2]||w(6,"invalid gzip data");var t=e[3],n=10;4&t&&(n+=2+(e[10]|e[11]<<8));for(var s=(t>>3&1)+(t>>4&1);s>0;s-=!e[n++]);return n+(2&t)},Te=function(e){var t=e.length;return(e[t-4]|e[t-3]<<8|e[t-2]<<16|e[t-1]<<24)>>>0},Se=function(e){return 10+(e.filename?e.filename.length+1:0)},me=function(e,t){var n=t.level,s=0==n?0:n<6?1:9==n?3:2;if(e[0]=120,e[1]=s<<6|(t.dictionary&&32),e[1]|=31-(e[0]<<8|e[1])%31,t.dictionary){var r=X();r.p(t.dictionary),_e(e,2,r.d())}},fe=function(e,t){return(8!=(15&e[0])||e[0]>>4>7||(e[0]<<8|e[1])%31)&&w(6,"invalid zlib data"),(e[1]>>5&1)==+!t&&w(6,"invalid zlib data: "+(32&e[1]?"need":"unexpected")+" dictionary"),2+(e[1]>>3&4)};function Ie(e,t){return"function"==typeof e&&(t=e,e={}),this.ondata=t,e}var Re=function(){function e(e,t){if("function"==typeof e&&(t=e,e={}),this.ondata=t,this.o=e||{},this.s={l:0,i:32768,w:32768,z:32768},this.b=new r(98304),this.o.dictionary){var n=this.o.dictionary.subarray(-32768);this.b.set(n,32768-n.length),this.s.i=32768-n.length}}return e.prototype.p=function(e,t){this.ondata(K(e,this.o,0,0,this.s),t)},e.prototype.push=function(e,t){this.ondata||w(5),this.s.l&&w(4);var n=e.length+this.s.z;if(n>this.b.length){if(n>2*this.b.length-32768){var s=new r(-32768&n);s.set(this.b.subarray(0,this.s.z)),this.b=s}var i=this.b.length-this.s.z;i&&(this.b.set(e.subarray(0,i),this.s.z),this.s.z=this.b.length,this.p(this.b,!1)),this.b.set(this.b.subarray(-32768)),this.b.set(e.subarray(i),32768),this.s.z=e.length-i+32768,this.s.i=32766,this.s.w=32768}else this.b.set(e,this.s.z),this.s.z+=e.length;this.s.l=1&t,(this.s.z>this.s.w+8191||t)&&(this.p(this.b,t||!1),this.s.w=this.s.i,this.s.i-=2)},e}();t.Deflate=Re;var Ce=function(){return function(e,t){le([te,function(){return[ue,Re]}],this,Ie.call(this,e,t),(function(e){var t=new Re(e.data);onmessage=ue(t)}),6)}}();function Ae(e,t,n){return n||(n=t,t={}),"function"!=typeof n&&w(7),ce(e,t,[te],(function(e){return oe(Oe(e.data[0],e.data[1]))}),0,n)}function Oe(e,t){return K(e,t||{},0,0)}t.AsyncDeflate=Ce,t.deflate=Ae,t.deflateSync=Oe;var Ne=function(){function e(e,t){"function"==typeof e&&(t=e,e={}),this.ondata=t;var n=e&&e.dictionary&&e.dictionary.subarray(-32768);this.s={i:0,b:n?n.length:0},this.o=new r(32768),this.p=new r(0),n&&this.o.set(n)}return e.prototype.e=function(e){if(this.ondata||w(5),this.d&&w(4),this.p.length){if(e.length){var t=new r(this.p.length+e.length);t.set(this.p),t.set(e,this.p.length),this.p=t}}else this.p=e},e.prototype.c=function(e){this.s.i=+(this.d=e||!1);var t=this.s.b,n=L(this.p,this.s,this.o);this.ondata(M(n,t,this.s.b),this.d),this.o=M(n,this.s.b-32768),this.s.b=this.o.length,this.p=M(this.p,this.s.p/8|0),this.s.p&=7},e.prototype.push=function(e,t){this.e(e),this.c(t)},e}();t.Inflate=Ne;var ye=function(){return function(e,t){le([ee,function(){return[ue,Ne]}],this,Ie.call(this,e,t),(function(e){var t=new Ne(e.data);onmessage=ue(t)}),7)}}();function Pe(e,t,n){return n||(n=t,t={}),"function"!=typeof n&&w(7),ce(e,t,[ee],(function(e){return oe(De(e.data[0],ae(e.data[1])))}),1,n)}function De(e,t){return L(e,{i:2},t&&t.out,t&&t.dictionary)}t.AsyncInflate=ye,t.inflate=Pe,t.inflateSync=De;var be=function(){function e(e,t){this.c=Q(),this.l=0,this.v=1,Re.call(this,e,t)}return e.prototype.push=function(e,t){this.c.p(e),this.l+=e.length,Re.prototype.push.call(this,e,t)},e.prototype.p=function(e,t){var n=K(e,this.o,this.v&&Se(this.o),t&&8,this.s);this.v&&(Ee(n,this.o),this.v=0),t&&(_e(n,n.length-8,this.c.d()),_e(n,n.length-4,this.l)),this.ondata(n,t)},e}();t.Gzip=be,t.Compress=be;var Me=function(){return function(e,t){le([te,ne,function(){return[ue,Re,be]}],this,Ie.call(this,e,t),(function(e){var t=new be(e.data);onmessage=ue(t)}),8)}}();function ve(e,t,n){return n||(n=t,t={}),"function"!=typeof n&&w(7),ce(e,t,[te,ne,function(){return[we]}],(function(e){return oe(we(e.data[0],e.data[1]))}),2,n)}function we(e,t){t||(t={});var n=Q(),s=e.length;n.p(e);var r=K(e,t,Se(t),8),i=r.length;return Ee(r,t),_e(r,i-8,n.d()),_e(r,i-4,s),r}t.AsyncGzip=Me,t.AsyncCompress=Me,t.gzip=ve,t.compress=ve,t.gzipSync=we,t.compressSync=we;var Le=function(){function e(e,t){this.v=1,this.r=0,Ne.call(this,e,t)}return e.prototype.push=function(e,t){if(Ne.prototype.e.call(this,e),this.r+=e.length,this.v){var n=this.p.subarray(this.v-1),s=n.length>3?ge(n):4;if(s>n.length){if(!t)return}else this.v>1&&this.onmember&&this.onmember(this.r-n.length);this.p=n.subarray(s),this.v=0}Ne.prototype.c.call(this,t),this.s.f&&!this.s.l&&(this.v=b(this.s.p)+9,this.s={i:0},this.o=new r(0),this.p.length&&this.push(new r(0),t))},e}();t.Gunzip=Le;var Ue=function(){return function(e,t){var n=this;le([ee,se,function(){return[ue,Ne,Le]}],this,Ie.call(this,e,t),(function(e){var t=new Le(e.data);t.onmember=function(e){return postMessage(e)},onmessage=ue(t)}),9,(function(e){return n.onmember&&n.onmember(e)}))}}();function Fe(e,t,n){return n||(n=t,t={}),"function"!=typeof n&&w(7),ce(e,t,[ee,se,function(){return[xe]}],(function(e){return oe(xe(e.data[0],e.data[1]))}),3,n)}function xe(e,t){var n=ge(e);return n+8>e.length&&w(6,"invalid gzip data"),L(e.subarray(n,-8),{i:2},t&&t.out||new r(Te(e)),t&&t.dictionary)}t.AsyncGunzip=Ue,t.gunzip=Fe,t.gunzipSync=xe;var Be=function(){function e(e,t){this.c=X(),this.v=1,Re.call(this,e,t)}return e.prototype.push=function(e,t){this.c.p(e),Re.prototype.push.call(this,e,t)},e.prototype.p=function(e,t){var n=K(e,this.o,this.v&&(this.o.dictionary?6:2),t&&4,this.s);this.v&&(me(n,this.o),this.v=0),t&&_e(n,n.length-4,this.c.d()),this.ondata(n,t)},e}();t.Zlib=Be;var Ge=function(){return function(e,t){le([te,re,function(){return[ue,Re,Be]}],this,Ie.call(this,e,t),(function(e){var t=new Be(e.data);onmessage=ue(t)}),10)}}();function ke(e,t){t||(t={});var n=X();n.p(e);var s=K(e,t,t.dictionary?6:2,4);return me(s,t),_e(s,s.length-4,n.d()),s}t.AsyncZlib=Ge,t.zlib=function(e,t,n){return n||(n=t,t={}),"function"!=typeof n&&w(7),ce(e,t,[te,re,function(){return[ke]}],(function(e){return oe(ke(e.data[0],e.data[1]))}),4,n)},t.zlibSync=ke;var We=function(){function e(e,t){Ne.call(this,e,t),this.v=e&&e.dictionary?2:1}return e.prototype.push=function(e,t){if(Ne.prototype.e.call(this,e),this.v){if(this.p.length<6&&!t)return;this.p=this.p.subarray(fe(this.p,this.v-1)),this.v=0}t&&(this.p.length<4&&w(6,"invalid zlib data"),this.p=this.p.subarray(0,-4)),Ne.prototype.c.call(this,t)},e}();t.Unzlib=We;var $e=function(){return function(e,t){le([ee,ie,function(){return[ue,Ne,We]}],this,Ie.call(this,e,t),(function(e){var t=new We(e.data);onmessage=ue(t)}),11)}}();function qe(e,t,n){return n||(n=t,t={}),"function"!=typeof n&&w(7),ce(e,t,[ee,ie,function(){return[Ve]}],(function(e){return oe(Ve(e.data[0],ae(e.data[1])))}),5,n)}function Ve(e,t){return L(e.subarray(fe(e,t&&t.dictionary),-4),{i:2},t&&t.out,t&&t.dictionary)}t.AsyncUnzlib=$e,t.unzlib=qe,t.unzlibSync=Ve;var He=function(){function e(e,t){this.G=Le,this.I=Ne,this.Z=We,this.o=Ie.call(this,e,t)||{}}return e.prototype.push=function(e,t){if(this.ondata||w(5),this.s)this.s.push(e,t);else{if(this.p&&this.p.length){var n=new r(this.p.length+e.length);n.set(this.p),n.set(e,this.p.length)}else this.p=e;if(this.p.length>2){var s=this,i=function(){s.ondata.apply(s,arguments)};this.s=31==this.p[0]&&139==this.p[1]&&8==this.p[2]?new this.G(this.o,i):8!=(15&this.p[0])||this.p[0]>>4>7||(this.p[0]<<8|this.p[1])%31?new this.I(this.o,i):new this.Z(this.o,i),this.s.push(this.p,t),this.p=null}}},e}();t.Decompress=He;var Ye=function(){function e(e,t){this.G=Ue,this.I=ye,this.Z=$e,He.call(this,e,t)}return e.prototype.push=function(e,t){He.prototype.push.call(this,e,t)},e}();t.AsyncDecompress=Ye,t.decompress=function(e,t,n){return n||(n=t,t={}),"function"!=typeof n&&w(7),31==e[0]&&139==e[1]&&8==e[2]?Fe(e,t,n):8!=(15&e[0])||e[0]>>4>7||(e[0]<<8|e[1])%31?Pe(e,t,n):qe(e,t,n)},t.decompressSync=function(e,t){return 31==e[0]&&139==e[1]&&8==e[2]?xe(e,t):8!=(15&e[0])||e[0]>>4>7||(e[0]<<8|e[1])%31?De(e,t):Ve(e,t)};var Qe=function(e,t,n,s){for(var i in e){var o=e[i],a=t+i,c=s;Array.isArray(o)&&(c=j(s,o[1]),o=o[0]),o instanceof r?n[a]=[o,c]:(n[a+="/"]=[new r(0),c],Qe(o,a,n,s))}},Xe="undefined"!=typeof TextEncoder&&new TextEncoder,Ke="undefined"!=typeof TextDecoder&&new TextDecoder,je=0;try{Ke.decode(V,{stream:!0}),je=1}catch(e){}var ze=function(e){for(var t="",n=0;;){var s=e[n++],r=(s>127)+(s>223)+(s>239);if(n+r>e.length)return{s:t,r:M(e,n-1)};r?3==r?(s=((15&s)<<18|(63&e[n++])<<12|(63&e[n++])<<6|63&e[n++])-65536,t+=String.fromCharCode(55296|s>>10,56320|1023&s)):t+=1&r?String.fromCharCode((31&s)<<6|63&e[n++]):String.fromCharCode((15&s)<<12|(63&e[n++])<<6|63&e[n++]):t+=String.fromCharCode(s)}},Ze=function(){function e(e){this.ondata=e,je?this.t=new TextDecoder:this.p=V}return e.prototype.push=function(e,t){if(this.ondata||w(5),t=!!t,this.t)return this.ondata(this.t.decode(e,{stream:!0}),t),void(t&&(this.t.decode().length&&w(8),this.t=null));this.p||w(4);var n=new r(this.p.length+e.length);n.set(this.p),n.set(e,this.p.length);var s=ze(n),i=s.s,o=s.r;t?(o.length&&w(8),this.p=null):this.p=o,this.ondata(i,t)},e}();t.DecodeUTF8=Ze;var Je=function(){function e(e){this.ondata=e}return e.prototype.push=function(e,t){this.ondata||w(5),this.d&&w(4),this.ondata(et(e),this.d=t||!1)},e}();function et(e,t){if(t){for(var n=new r(e.length),s=0;s<e.length;++s)n[s]=e.charCodeAt(s);return n}if(Xe)return Xe.encode(e);var i=e.length,o=new r(e.length+(e.length>>1)),a=0,c=function(e){o[a++]=e};for(s=0;s<i;++s){if(a+5>o.length){var u=new r(a+8+(i-s<<1));u.set(o),o=u}var l=e.charCodeAt(s);l<128||t?c(l):l<2048?(c(192|l>>6),c(128|63&l)):l>55295&&l<57344?(c(240|(l=65536+(1047552&l)|1023&e.charCodeAt(++s))>>18),c(128|l>>12&63),c(128|l>>6&63),c(128|63&l)):(c(224|l>>12),c(128|l>>6&63),c(128|63&l))}return M(o,0,a)}function tt(e,t){if(t){for(var n="",s=0;s<e.length;s+=16384)n+=String.fromCharCode.apply(null,e.subarray(s,s+16384));return n}if(Ke)return Ke.decode(e);var r=ze(e),i=r.s;return(n=r.r).length&&w(8),i}t.EncodeUTF8=Je,t.strToU8=et,t.strFromU8=tt;var nt=function(e){return 1==e?3:e<6?2:9==e?1:0},st=function(e,t){return t+30+he(e,t+26)+he(e,t+28)},rt=function(e,t,n){var s=he(e,t+28),r=tt(e.subarray(t+46,t+46+s),!(2048&he(e,t+8))),i=t+46+s,o=pe(e,t+20),a=n&&4294967295==o?it(e,i):[o,pe(e,t+24),pe(e,t+42)],c=a[0],u=a[1],l=a[2];return[he(e,t+10),c,u,r,i+he(e,t+30)+he(e,t+32),l]},it=function(e,t){for(;1!=he(e,t);t+=4+he(e,t+2));return[de(e,t+12),de(e,t+4),de(e,t+20)]},ot=function(e){var t=0;if(e)for(var n in e){var s=e[n].length;s>65535&&w(9),t+=s+4}return t},at=function(e,t,n,s,r,i,o,a){var c=s.length,u=n.extra,l=a&&a.length,h=ot(u);_e(e,t,null!=o?33639248:67324752),t+=4,null!=o&&(e[t++]=20,e[t++]=n.os),e[t]=20,t+=2,e[t++]=n.flag<<1|(i<0&&8),e[t++]=r&&8,e[t++]=255&n.compression,e[t++]=n.compression>>8;var p=new Date(null==n.mtime?Date.now():n.mtime),d=p.getFullYear()-1980;if((d<0||d>119)&&w(10),_e(e,t,d<<25|p.getMonth()+1<<21|p.getDate()<<16|p.getHours()<<11|p.getMinutes()<<5|p.getSeconds()>>1),t+=4,-1!=i&&(_e(e,t,n.crc),_e(e,t+4,i<0?-i-2:i),_e(e,t+8,n.size)),_e(e,t+12,c),_e(e,t+14,h),t+=16,null!=o&&(_e(e,t,l),_e(e,t+6,n.attrs),_e(e,t+10,o),t+=14),e.set(s,t),t+=c,h)for(var _ in u){var E=u[_],g=E.length;_e(e,t,+_),_e(e,t+2,g),e.set(E,t+4),t+=4+g}return l&&(e.set(a,t),t+=l),t},ct=function(e,t,n,s,r){_e(e,t,101010256),_e(e,t+8,n),_e(e,t+10,n),_e(e,t+12,s),_e(e,t+16,r)},ut=function(){function e(e){this.filename=e,this.c=Q(),this.size=0,this.compression=0}return e.prototype.process=function(e,t){this.ondata(null,e,t)},e.prototype.push=function(e,t){this.ondata||w(5),this.c.p(e),this.size+=e.length,t&&(this.crc=this.c.d()),this.process(e,t||!1)},e}();t.ZipPassThrough=ut;var lt=function(){function e(e,t){var n=this;t||(t={}),ut.call(this,e),this.d=new Re(t,(function(e,t){n.ondata(null,e,t)})),this.compression=8,this.flag=nt(t.level)}return e.prototype.process=function(e,t){try{this.d.push(e,t)}catch(e){this.ondata(e,null,t)}},e.prototype.push=function(e,t){ut.prototype.push.call(this,e,t)},e}();t.ZipDeflate=lt;var ht=function(){function e(e,t){var n=this;t||(t={}),ut.call(this,e),this.d=new Ce(t,(function(e,t,s){n.ondata(e,t,s)})),this.compression=8,this.flag=nt(t.level),this.terminate=this.d.terminate}return e.prototype.process=function(e,t){this.d.push(e,t)},e.prototype.push=function(e,t){ut.prototype.push.call(this,e,t)},e}();t.AsyncZipDeflate=ht;var pt=function(){function e(e){this.ondata=e,this.u=[],this.d=1}return e.prototype.add=function(e){var t=this;if(this.ondata||w(5),2&this.d)this.ondata(w(4+8*(1&this.d),0,1),null,!1);else{var n=et(e.filename),s=n.length,i=e.comment,o=i&&et(i),a=s!=e.filename.length||o&&i.length!=o.length,c=s+ot(e.extra)+30;s>65535&&this.ondata(w(11,0,1),null,!1);var u=new r(c);at(u,0,e,n,a,-1);var l=[u],h=function(){for(var e=0,n=l;e<n.length;e++){var s=n[e];t.ondata(null,s,!1)}l=[]},p=this.d;this.d=0;var d=this.u.length,_=j(e,{f:n,u:a,o,t:function(){e.terminate&&e.terminate()},r:function(){if(h(),p){var e=t.u[d+1];e?e.r():t.d=1}p=1}}),E=0;e.ondata=function(n,s,i){if(n)t.ondata(n,s,i),t.terminate();else if(E+=s.length,l.push(s),i){var o=new r(16);_e(o,0,134695760),_e(o,4,e.crc),_e(o,8,E),_e(o,12,e.size),l.push(o),_.c=E,_.b=c+E+16,_.crc=e.crc,_.size=e.size,p&&_.r(),p=1}else p&&h()},this.u.push(_)}},e.prototype.end=function(){var e=this;2&this.d?this.ondata(w(4+8*(1&this.d),0,1),null,!0):(this.d?this.e():this.u.push({r:function(){1&e.d&&(e.u.splice(-1,1),e.e())},t:function(){}}),this.d=3)},e.prototype.e=function(){for(var e=0,t=0,n=0,s=0,i=this.u;s<i.length;s++)n+=46+(u=i[s]).f.length+ot(u.extra)+(u.o?u.o.length:0);for(var o=new r(n+22),a=0,c=this.u;a<c.length;a++){var u=c[a];at(o,e,u,u.f,u.u,-u.c-2,t,u.o),e+=46+u.f.length+ot(u.extra)+(u.o?u.o.length:0),t+=u.b}ct(o,e,this.u.length,n,t),this.ondata(null,o,!0),this.d=2},e.prototype.terminate=function(){for(var e=0,t=this.u;e<t.length;e++)t[e].t();this.d=2},e}();t.Zip=pt,t.zip=function(e,t,n){n||(n=t,t={}),"function"!=typeof n&&w(7);var s={};Qe(e,"",s,t);var i=Object.keys(s),o=i.length,a=0,c=0,u=o,l=new Array(o),h=[],p=function(){for(var e=0;e<h.length;++e)h[e]()},d=function(e,t){Tt((function(){n(e,t)}))};Tt((function(){d=n}));var _=function(){var e=new r(c+22),t=a,n=c-a;c=0;for(var s=0;s<u;++s){var i=l[s];try{var o=i.c.length;at(e,c,i,i.f,i.u,o);var h=30+i.f.length+ot(i.extra),p=c+h;e.set(i.c,p),at(e,a,i,i.f,i.u,o,c,i.m),a+=16+h+(i.m?i.m.length:0),c=p+o}catch(e){return d(e,null)}}ct(e,a,l.length,n,t),d(null,e)};o||_();for(var E=function(e){var t=i[e],n=s[t],r=n[0],u=n[1],E=Q(),g=r.length;E.p(r);var T=et(t),S=T.length,m=u.comment,f=m&&et(m),I=f&&f.length,R=ot(u.extra),C=0==u.level?0:8,A=function(n,s){if(n)p(),d(n,null);else{var r=s.length;l[e]=j(u,{size:g,crc:E.d(),c:s,f:T,m:f,u:S!=t.length||f&&m.length!=I,compression:C}),a+=30+S+R+r,c+=76+2*(S+R)+(I||0)+r,--o||_()}};if(S>65535&&A(w(11,0,1),null),C)if(g<16e4)try{A(null,Oe(r,u))}catch(e){A(e,null)}else h.push(Ae(r,u,A));else A(null,r)},g=0;g<u;++g)E(g);return p},t.zipSync=function(e,t){t||(t={});var n={},s=[];Qe(e,"",n,t);var i=0,o=0;for(var a in n){var c=n[a],u=c[0],l=c[1],h=0==l.level?0:8,p=(A=et(a)).length,d=l.comment,_=d&&et(d),E=_&&_.length,g=ot(l.extra);p>65535&&w(11);var T=h?Oe(u,l):u,S=T.length,m=Q();m.p(u),s.push(j(l,{size:u.length,crc:m.d(),c:T,f:A,m:_,u:p!=a.length||_&&d.length!=E,o:i,compression:h})),i+=30+p+g+S,o+=76+2*(p+g)+(E||0)+S}for(var f=new r(o+22),I=i,R=o-i,C=0;C<s.length;++C){var A=s[C];at(f,A.o,A,A.f,A.u,A.c.length);var O=30+A.f.length+ot(A.extra);f.set(A.c,A.o+O),at(f,i,A,A.f,A.u,A.c.length,A.o,A.m),i+=16+O+(A.m?A.m.length:0)}return ct(f,i,s.length,R,I),f};var dt=function(){function e(){}return e.prototype.push=function(e,t){this.ondata(null,e,t)},e.compression=0,e}();t.UnzipPassThrough=dt;var _t=function(){function e(){var e=this;this.i=new Ne((function(t,n){e.ondata(null,t,n)}))}return e.prototype.push=function(e,t){try{this.i.push(e,t)}catch(e){this.ondata(e,null,t)}},e.compression=8,e}();t.UnzipInflate=_t;var Et=function(){function e(e,t){var n=this;t<32e4?this.i=new Ne((function(e,t){n.ondata(null,e,t)})):(this.i=new ye((function(e,t,s){n.ondata(e,t,s)})),this.terminate=this.i.terminate)}return e.prototype.push=function(e,t){this.i.terminate&&(e=M(e,0)),this.i.push(e,t)},e.compression=8,e}();t.AsyncUnzipInflate=Et;var gt=function(){function e(e){this.onfile=e,this.k=[],this.o={0:dt},this.p=V}return e.prototype.push=function(e,t){var n=this;if(this.onfile||w(5),this.p||w(4),this.c>0){var s=Math.min(this.c,e.length),i=e.subarray(0,s);if(this.c-=s,this.d?this.d.push(i,!this.c):this.k[0].push(i),(e=e.subarray(s)).length)return this.push(e,t)}else{var o=0,a=0,c=void 0,u=void 0;this.p.length?e.length?((u=new r(this.p.length+e.length)).set(this.p),u.set(e,this.p.length)):u=this.p:u=e;for(var l=u.length,h=this.c,p=h&&this.d,d=function(){var e,t=pe(u,a);if(67324752==t){o=1,c=a,_.d=null,_.c=0;var s=he(u,a+6),r=he(u,a+8),i=2048&s,p=8&s,d=he(u,a+26),E=he(u,a+28);if(l>a+30+d+E){var g=[];_.k.unshift(g),o=2;var T,S=pe(u,a+18),m=pe(u,a+22),f=tt(u.subarray(a+30,a+=30+d),!i);4294967295==S?(e=p?[-2]:it(u,a),S=e[0],m=e[1]):p&&(S=-1),a+=E,_.c=S;var I={name:f,compression:r,start:function(){if(I.ondata||w(5),S){var e=n.o[r];e||I.ondata(w(14,"unknown compression type "+r,1),null,!1),(T=S<0?new e(f):new e(f,S,m)).ondata=function(e,t,n){I.ondata(e,t,n)};for(var t=0,s=g;t<s.length;t++){var i=s[t];T.push(i,!1)}n.k[0]==g&&n.c?n.d=T:T.push(V,!0)}else I.ondata(null,V,!0)},terminate:function(){T&&T.terminate&&T.terminate()}};S>=0&&(I.size=S,I.originalSize=m),_.onfile(I)}return"break"}if(h){if(134695760==t)return c=a+=12+(-2==h&&8),o=3,_.c=0,"break";if(33639248==t)return c=a-=4,o=3,_.c=0,"break"}},_=this;a<l-4&&"break"!==d();++a);if(this.p=V,h<0){var E=o?u.subarray(0,c-12-(-2==h&&8)-(134695760==pe(u,c-16)&&4)):u.subarray(0,a);p?p.push(E,!!o):this.k[+(2==o)].push(E)}if(2&o)return this.push(u.subarray(a),t);this.p=u.subarray(a)}t&&(this.c&&w(13),this.p=null)},e.prototype.register=function(e){this.o[e.compression]=e},e}();t.Unzip=gt;var Tt="function"==typeof queueMicrotask?queueMicrotask:"function"==typeof setTimeout?setTimeout:function(e){e()};t.unzip=function(e,t,n){n||(n=t,t={}),"function"!=typeof n&&w(7);var s=[],i=function(){for(var e=0;e<s.length;++e)s[e]()},o={},a=function(e,t){Tt((function(){n(e,t)}))};Tt((function(){a=n}));for(var c=e.length-22;101010256!=pe(e,c);--c)if(!c||e.length-c>65558)return a(w(13,0,1),null),i;var u=he(e,c+8);if(u){var l=u,h=pe(e,c+16),p=4294967295==h||65535==l;if(p){var d=pe(e,c-12);(p=101075792==pe(e,d))&&(l=u=pe(e,d+32),h=pe(e,d+48))}for(var _=t&&t.filter,E=function(t){var n=rt(e,h,p),c=n[0],l=n[1],d=n[2],E=n[3],g=n[4],T=n[5],S=st(e,T);h=g;var m=function(e,t){e?(i(),a(e,null)):(t&&(o[E]=t),--u||a(null,o))};if(!_||_({name:E,size:l,originalSize:d,compression:c}))if(c)if(8==c){var f=e.subarray(S,S+l);if(l<32e4)try{m(null,De(f,{out:new r(d)}))}catch(e){m(e,null)}else s.push(Pe(f,{size:d},m))}else m(w(14,"unknown compression type "+c,1),null);else m(null,M(e,S,S+l));else m(null,null)},g=0;g<l;++g)E()}else a(null,{});return i},t.unzipSync=function(e,t){for(var n={},s=e.length-22;101010256!=pe(e,s);--s)(!s||e.length-s>65558)&&w(13);var i=he(e,s+8);if(!i)return{};var o=pe(e,s+16),a=4294967295==o||65535==i;if(a){var c=pe(e,s-12);(a=101075792==pe(e,c))&&(i=pe(e,c+32),o=pe(e,c+48))}for(var u=t&&t.filter,l=0;l<i;++l){var h=rt(e,o,a),p=h[0],d=h[1],_=h[2],E=h[3],g=h[4],T=h[5],S=st(e,T);o=g,u&&!u({name:E,size:d,originalSize:_,compression:p})||(p?8==p?n[E]=De(e.subarray(S,S+d),{out:new r(_)}):w(14,"unknown compression type "+p):n[E]=M(e,S,S+d))}return n}},599:(e,t,n)=>{const{LogLevel:s}=n(1074),r={impl:null,level:s.INFO},i={getImpl:function(){return r.impl},setImpl:function(e){r.impl=e},getLogLevel:function(){return r.level},setLogLevel:function(e){r.level=e}};e.exports.GlobalBinding=i},608:(e,t,n)=>{const{ReplayStartLocation:s,ReplayStartType:r}=n(4115);e.exports.ReplayStartLocationBeginning=class extends s{constructor(){super({_type:r.BEGINNING})}static inspect(){return"BEGINNING"}}},617:(e,t,n)=>{const s=n(4386),{Convert:r}=n(9783),{DestinationType:i}=n(8805),{LOG_ERROR:o}=n(2694),{SubscriptionInfo:a}=n(5456),{UUID:c,StringUtils:u}=n(968),{ucs2ToUtf8:l,utf8ToUcs2:h}=r,{ProfileBinding:p}=s,{toSafeChars:d,stripNullTerminate:_}=u,{ErrorSubcode:E,OperationError:g}=n(6706),T={[i.QUEUE]:"#P2P/QUE/",[i.TEMPORARY_QUEUE]:"#P2P/QTMP/"};function S(e){return T[e]||""}function m(e,t){return new g(`Invalid ${e}: ${t}`,E.INVALID_TOPIC_SYNTAX)}function f(e,t,n,s=m.bind(null,e)){let r;const i=n.length;if(i<1)return r=s("Too short (must be >= 1 character)."),{error:r};const o=t.length;if(o>251)return r=s(`Too long (encoding must be <= 250 bytes); name is ${o-1} bytes: '${n}'`),{error:r};let a=!1;">"===n.charAt(i-1)&&(a=!0);for(let e=0;e<i;++e)switch(n.charAt(e)){case"/":if(0===e||e===i-1||"/"===n.charAt(e-1))return r=s(`Empty level(s) in '${n}'@${e}.`),{error:r};break;case"*":if(e<i-1&&"/"!==n.charAt(e+1))return r=s(`Illegal wildcard(s) in '${n}'@${e}.`),{error:r};a=!0}return{isWildcarded:a}}function I(e){return p.value.topicUtf8Encode?`${l(e)}\0`:`${e}\0`}function R(e,t){const n=S(e),s=n.length,r=n+t;return{bytes:I(r),offset:s,networkName:r}}const C={createPrefix:S,createTemporaryName:function(e,t,n){const s=n||c.generateUUID();switch(e){case i.TOPIC:return`#P2P/TTMP/${t}/${s}`;case i.TEMPORARY_QUEUE:return`#P2P/QTMP/${t}/${s}`;default:o("Unknown/invalid destination type",i.describe(e))}},decodeBytes:function(e){return _(p.value.topicUtf8Encode?h(e):e)},encode:R,encodeBytes:I,legacyValidate:f,toSafeChars:d,validateAndEncode:function(e,t,n=m.bind(null,e)){const{bytes:s,offset:r}=R(e,t),{error:i,isWildcarded:o}=f(e,s,t,n);let c=i,u={};if(u.isWildcarded=o,c||Object.keys(T).some((e=>{const s=T[e];return!!t.startsWith(s)&&(c=n(`Reserved prefix '${s}' found at start of '${t}'`),!0)})),!c){const{error:n,subInfo:s}=a.parseFromName(t,e);c=n,u=s}return{bytes:s,offset:r,error:c,isWildcarded:o,subscriptionInfo:u}}};e.exports.DestinationUtil=C},719:(e,t,n)=>{const{APIPropertiesValidators:s}=n(968),{QueueAccessType:r}=n(1851),{QueueDiscardBehavior:i}=n(9449),{QueuePermissions:o}=n(9489),{validateInstance:a,valBoolean:c,valIsMember:u,valNumber:l,valRange:h}=s,p={validate(e){const t=a.bind(null,"QueueProperties",e);void 0!==e.permissions&&null!==e.permissions&&t("permissions",[u,o,"QueuePermissions"]),void 0!==e.accessType&&null!==e.accessType&&t("accessType",[u,r,"QueueAccessType"]),void 0!==e.quotaMB&&null!==e.quotaMB&&t("quotaMB",[l],[h,0,2**32-1]),void 0!==e.maxMessageSize&&null!==e.maxMessageSize&&t("maxMessageSize",[l],[h,0,2**32-1]),void 0!==e.respectsTTL&&null!==e.respectsTTL&&t("respectsTTL",[c]),void 0!==e.discardBehavior&&null!==e.discardBehavior&&t("discardBehavior",[u,i,"QueueDiscardBehavior"]),void 0!==e.maxMessageRedelivery&&null!==e.maxMessageRedelivery&&t("maxMessageRedelivery",[l],[h,0,255])}};e.exports.QueuePropertiesValidator=p},755:(e,t,n)=>{var s=n(2195);class r{get guaranteedMessagingEnabled(){}get cometEnabled(){}get topicUtf8Encode(){}get byteArrayAsString(){}inspect(){return{guaranteedMessagingEnabled:this.guaranteedMessagingEnabled,cometEnabled:this.cometEnabled,topicUtf8Encode:this.topicUtf8Encode,byteArrayAsString:this.byteArrayAsString}}toString(){return s(this)}}const i=new class extends r{get guaranteedMessagingEnabled(){return!1}get cometEnabled(){return!0}get topicUtf8Encode(){return!1}get byteArrayAsString(){return!0}},o=new class extends r{get guaranteedMessagingEnabled(){return!0}get cometEnabled(){return!1}get topicUtf8Encode(){return!0}get byteArrayAsString(){return!0}},a=new class extends r{get guaranteedMessagingEnabled(){return!0}get cometEnabled(){return!1}get topicUtf8Encode(){return!0}get byteArrayAsString(){return!1}},c={_legacy:i,_v10:o,_forward:a,_default:i,version7:i,version10:o,version10_5:a,inspect(){return{version7:this.version7,version10:this.version10,version10_5:this.version10_5}},toString(){return s(this)}};e.exports.FactoryProfile=r,e.exports.SolclientFactoryProfiles=c},760:(e,t,n)=>{const s=n(6247),{BidiMap:r,Lazy:i}=n(7444),{Bits:o,Convert:a}=n(9783),{ContentSummaryElement:c}=n(4970),{ContentSummaryType:u}=n(8283),{ErrorSubcode:l,OperationError:h}=n(6706),{LOG_ERROR:p}=n(2694),{SMFUH:d}=n(8247),{lazyValue:_}=i,{int8ToStr:E,int16ToStr:g,int24ToStr:T,int32ToStr:S}=a,m=_((()=>{const e=[[0,s.MessageDeliveryModeType.NON_PERSISTENT],[1,s.MessageDeliveryModeType.PERSISTENT],[2,s.MessageDeliveryModeType.DIRECT]].map((e=>[e[0],e[1]]));return new r(...e)})),f=_((()=>m.value.forward)),I=_((()=>m.value.reverse)),R=(()=>{const e=[],t=Math.pow(2,5);return d.values.forEach((n=>{e[n]=[];for(let s=0;s<t;++s){let t=0;t=o.set(t,n,6,2),t=o.set(t,s,0,5),e[n][s]=E(t)}})),e})(),C=new Array(256).fill(null).map(((e,t)=>E(t))),A=(()=>{const e=[],t=Math.pow(2,3),n=Math.pow(2,2);return d.values.forEach((s=>{e[s]=[];for(let r=0;r<t;++r){e[s][r]=[];for(let t=0;t<n;++t){let n=0;n=o.set(n,s,6,2),n=o.set(n,1,5,1),n=o.set(n,r,2,3),n=o.set(n,t,0,2),e[s][r][t]=E(n)}}})),e})(),O=[u.XML_META,u.XML_PAYLOAD,u.BINARY_ATTACHMENT,u.CID_LIST,u.BINARY_METADATA],N={FORCED_LENGTH_MODE:{FIVE:5,SIX:6},parseTopicQueueOffsets:function(e,t){const n=[];return n[0]=e.readUInt8(t),n[1]=e.readUInt8(t+1),n},parseResponseParam:function(e,t,n){const s=[];return s[0]=e.readInt32BE(t),s[1]=n>4?e.toString("latin1",t+4,t+n):"",s},parseDeliveryMode:function(e,t){const n=e.readUInt8(t),r=f.value.get(n);return void 0!==r?r:s.MessageDeliveryModeType.DIRECT},encDeliveryMode:function(e){const t=I.value.get(e);return E(void 0!==t?t:s.MessageDeliveryModeType.DIRECT)},parseContentSummary:function(e,t,n){const s=[];let r=0,i=t;for(;i<t+n;){const t=e.readUInt8(i),n=o.get(t,4,4),a=o.get(t,0,4);let u=0;switch(a){case 2:u=e.readUInt8(i+1);break;case 3:u=e.readUInt16BE(i+1);break;case 4:u=e.readUIntBE(i+1,3);break;case 5:u=e.readInt32BE(i+1)}if(0===a)return p("Invalid content summary parameter - pos not advancing"),null;i+=a;const l=O[n];void 0===l&&p(`Unhandled element type ${n}`);const h=new c(l,r,u);s.push(h),r+=u}return s},encContentSummary:function(e){const t=[];for(let n=0,s=e.length;n<s;++n){const s=e[n];let r="",i=o.set(0,s.type,4,4);s.length<=255?(i=o.set(i,2,0,4),r=E(s.length)):s.length<=65535?(i=o.set(i,3,0,4),r=g(s.length)):s.length<=16777215?(i=o.set(i,4,0,4),r=T(s.length)):(i=o.set(i,5,0,4),r=S(s.length)),t.push(E(i)),t.push(r)}return t.join("")},encodeSMFParam:function(e,t,n){if(void 0===n)return R[e][t]+C[2];const s=n.length;return s<=253?R[e][t]+C[s+2]+n:R[e][t]+C[0]+S(s+6)+n},encodeSMFExtendedParam:function(e,t,n,s=-1){let r=0;r=o.set(r,e?1:0,7,1);const i=null==n?0:n.length,a={0:0,1:1,2:2,4:3,8:4};let c=0,u="";if(s!==N.FORCED_LENGTH_MODE.FIVE&&s!==N.FORCED_LENGTH_MODE.SIX&&(s=-1),Object.prototype.hasOwnProperty.call(a,i))c=a[i];else if(i<253&&s!==N.FORCED_LENGTH_MODE.SIX||s===N.FORCED_LENGTH_MODE.FIVE)c=5,u=E(i+3);else{if(!(i<65532&&s!==N.FORCED_LENGTH_MODE.FIVE||s===N.FORCED_LENGTH_MODE.SIX))throw p(`Extended parameter type ${t} is too long (${i} bytes) `),new h(`Extended parameter (${t}) over the 2^16 byte limit`,l.PARAMETER_OUT_OF_RANGE);c=6,u=g(i+4)}r=o.set(r,c,4,3),r=o.set(r,t>>8,0,4);const d=255&t;return E(r)+E(d)+u+n},encLightSMFParam:function(e,t,n){return A[e][t][n.length]+n}};e.exports.ParamParse=N},769:(e,t,n)=>{const{Codec:s}=n(6360),{SDTDestType:r}=n(3153),{SDTField:i}=n(7385),{SDTFieldType:o}=n(7849),{SDTMapContainer:a}=n(7449),{SDTStreamContainer:c}=n(5711),{SDTUnsupportedValueError:u}=n(2157),{SDTValueErrorSubcode:l}=n(3268);e.exports.Codec=s,e.exports.SDTDestType=r,e.exports.SDTField=i,e.exports.SDTFieldType=o,e.exports.SDTMapContainer=a,e.exports.SDTStreamContainer=c,e.exports.SDTUnsupportedValueError=u,e.exports.SDTValueErrorSubcode=l},802:(e,t,n)=>{const{Check:s}=n(7434),{Parameter:r}=n(1537);e.exports.Check=s,e.exports.Parameter=r},818:(e,t,n)=>{const{Bits:s,Convert:r}=n(9783),{get:i}=s,{int8ToStr:o,int24ToStr:a}=r;class c{constructor(e,t){this.type=e,this.payload=t}asEncodedSmf(){const e=[];return e.push(o(1)),e.push(o(this.type)),e.push(a(this.payload.length)),e.push(this.payload.toString("latin1")),e.join("")}static fromEncodedSmf(e,t=0){if(e.length-t<6)return null;const n=e.readUInt8(t),s=e.readInt32BE(t+1),r=i(s,24,8),o=i(s,0,24),a=4*n+1,u=e.slice(t+a,t+a+o);return new c(r,u)}}e.exports.BinaryMetaBlock=c},825:e=>{const t={mixin:function(e,t){const n=e.prototype,s=t.prototype;return Object.getOwnPropertyNames(s).forEach((e=>{"constructor"!==e&&Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(s,e))})),e}};e.exports.Mixin=t},852:(e,t,n)=>{var s=n(2195);const{APIProperties:r}=n(968),{Check:i}=n(802);function o(){const{LogLevel:e}=n(2694);return e.INFO}e.exports.SolclientFactoryProperties=class extends r{constructor(e){super({logLevel:o(),logger:null},e)}get profile(){return this._profile}set profile(e){this._profile=e}get logLevel(){return i.number(this._logLevel)?this._logLevel:o()}set logLevel(e){this._logLevel=e}get logger(){return this._logger||null}set logger(e){this._logger=e}inspect(){const{LogLevel:e}=n(2694);return{logLevel:e.describe(this._logLevel),profile:this._profile}}toString(){return s(this)}}},886:()=>{},918:(e,t,n)=>{const{SessionEvent:s}=n(8229);e.exports.ProvisionEvent=class extends s{constructor(e,t,n=void 0,s=0,r=void 0,i=void 0,o=null,a=null){super([],e,t,n,s,r,i),this._queueDescriptor=o,this._queueProperties=a}get queueDescriptor(){return this._queueDescriptor}set queueDescriptor(e){this._queueDescriptor=e}get queueProperties(){return this._queueProperties}set queueProperties(e){this._queueProperties=e}inspect(){const e=super.inspect();return e.queueDescriptor=this._queueDescriptor,e.queueProperties=this._queueProperties,e}}},946:(e,t,n)=>{const{FsmEvent:s}=n(7414);e.exports.ConsumerFSMEvent=class extends s{constructor(e,t){super(e),this.details=t}}},968:(e,t,n)=>{const{APIProperties:s}=n(9544),{APIPropertiesValidators:r}=n(5050),{ArrayUtils:i}=n(8821),{parseURL:o}=n(6321),{Process:a}=n(85),{StringBuffer:c}=n(9710),{StringUtils:u}=n(319),{TimingBucket:l}=n(4255),{UUID:h}=n(77),{Version:p}=n(9436),d=n(4205);e.exports={clone:d,parseURL:o,APIProperties:s,APIPropertiesValidators:r,ArrayUtils:i,Process:a,StringBuffer:c,StringUtils:u,TimingBucket:l,UUID:h,Version:p}},996:(e,t,n)=>{const s=n(4386),{APIProperties:r}=n(968),{Check:i}=n(802),{MessagePublisherAcknowledgeMode:o}=n(6e3),{TransportCapabilities:a}=n(8205);function c(){const{ProfileBinding:e}=s,t=e.value.guaranteedMessagingEnabled,n=a.web.webSocket();return t&&n}function u(){return{enabled:c(),windowSize:50,acknowledgeTimeoutInMsecs:2e3,acknowledgeMode:o.PER_MESSAGE,connectRetryCount:3,connectTimeoutInMsecs:5e3}}e.exports.MessagePublisherProperties=class extends r{constructor(e){super(u(),e||{})}get enabled(){return this._enabled}set enabled(e){this._enabled=e}get windowSize(){return i.defined(this._windowSize)?this._windowSize:u().windowSize}set windowSize(e){this._windowSize=e}get acknowledgeTimeoutInMsecs(){return i.defined(this._acknowledgeTimeoutInMsecs)?this._acknowledgeTimeoutInMsecs:u().acknowledgeTimeoutInMsecs}set acknowledgeTimeoutInMsecs(e){this._acknowledgeTimeoutInMsecs=e}get acknowledgeMode(){return this._acknowledgeMode||o.PER_MESSAGE}set acknowledgeMode(e){this._acknowledgeMode=e}get connectRetryCount(){return i.defined(this._connectRetryCount)?this._connectRetryCount:u().connectRetryCount}set connectRetryCount(e){this._connectRetryCount=e}get connectTimeoutInMsecs(){return i.defined(this._connectTimeoutInMsecs)?this._connectTimeoutInMsecs:u().connectTimeoutInMsecs}set connectTimeoutInMsecs(e){this._connectTimeoutInMsecs=e}inspect(){return{enabled:this.enabled,windowSize:this.windowSize,acknowledgeTimeoutInMsecs:this.acknowledgeTimeoutInMsecs,acknowledgeMode:o.describe(this.acknowledgeMode),connectRetryCount:this.connectRetryCount,connectTimeoutInMsecs:this.connectTimeoutInMsecs}}}},1074:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.LogLevel=s.new({FATAL:0,ERROR:1,WARN:2,INFO:3,DEBUG:4,TRACE:5})},1081:(e,t,n)=>{const s=n(9783),r=n(199),i=n(9620),o=n(6706),a=n(7444),c=n(4386),u=n(7414),l=n(2694),h=n(6247),p=n(2288),d=n(5898),_=n(769),E=n(5024),g=n(3450),T=n(2689),S=n(6663),m=n(8205),f=n(968),I=n(802);e.exports={Convert:s,Debug:r,Destination:i,Error:o,ESKit:a,Factory:c,FSM:u,Log:l,Message:h,MessageTracing:p,Publisher:d,SDT:_,Session:E,SMF:g,SolcacheSession:T,TestEnv:S,Transport:m,Util:f,Validate:I}},1122:(e,t,n)=>{var s=n(2195);const{ErrorSubcode:r}=n(6706),{FsmEvent:i}=n(7414),{Hex:o}=n(9783),{formatHexString:a}=o;e.exports.SessionFSMEvent=class extends i{constructor(e,t){super(e),this.eventText=null,this.errorSubcode=null,this.eventReason=null,this.smfMsg=null,this.sessionId=null,this.guaranteedFlowObject=null,Object.assign(this,t)}inspect(){return{eventText:this.eventText,eventReason:this.eventReason,errorSubcode:r.describe(this.errorSubcode),sessionId:this.sessionId&&a(this.sessionId)||"N/A"}}getExtraStringInfo(){return s(this)}}},1123:e=>{e.exports.SMFParameter=class{constructor(e,t,n,s,r,i){this._type=t,this._value=n,this._uh=e,this._buffer=s,this._begin=r,this._end=i}getType(){return this._type}getValue(){return this._buffer&&!this._value?this._buffer.toString("latin1",this._begin,this._end):this._value}getUh(){return this._uh}getBuffer(){return this._buffer}getBegin(){return this._begin}getEnd(){return this._end}toString(){return`${this._uh}:0x${this._type.toString(16)} = ${this.getValue()}`}}},1246:(e,t,n)=>{var s=n(2195);const{Long:r}=n(9783),i={_lastAcked:r.fromNumber(0,!0),_lastSent:r.fromNumber(0,!0),_next:r.fromNumber(1,!0)},o=e=>e.toString(10);e.exports.MessageIds=class{constructor(e){Object.assign(this,i,e)}get lastAcked(){return this._lastAcked}set lastAcked(e){this._lastAcked=r.fromValue(e)}get lastSent(){return this._lastSent}setLastSent(e){this._lastSent=r.fromValue(e),this._next=this._lastSent.add(1)}get next(){return this._next}inspect(){return{lastAcked:o(this.lastAcked),lastSent:o(this.lastSent),next:o(this.next)}}toString(){return s(this)}}},1250:(e,t,n)=>{const{ErrorSubcode:s,OperationError:r}=n(6706),{HTTPTransportSession:i}=n(9005),{LOG_TRACE:o,LOG_INFO:a,LOG_ERROR:c}=n(2694),{FsmEvent:u}=n(7414),{TransportBase:l}=n(383),{TransportProtocol:h}=n(9072),{TransportProtocolHandler:p}=n(5208),{TransportReturnCode:d}=n(9944),{TransportSessionEventCode:_}=n(3427),{WebSocketTransportSession:E}=n(9990),{WebTransportEvent:g}=n(3246),{WebTransportFSM:T}=n(6145);e.exports.WebTransport=class extends l{constructor(e,t,n,s,r){super(e,t,n,s),s.webTransportProtocolList,this._transportHandler=new p(e,s.webTransportProtocolList),this._webTransportFsm=new T(this,r),this._webTransportFsm.start()}notifyEvent(e){this._eventCB(e)}handleDestroyed(){this._transportSession=null}handleTransportEvent(e){let t;switch(a(`Web transport receive transport event: ${e}`),e.getTransportEventCode()){case _.UP_NOTICE:t=new u({name:g.UP_NOTICE}),t._transportEvent=e,this._webTransportFsm.processEvent(t);break;case _.DESTROYED_NOTICE:this.handleDestroyed(),t=new u({name:g.DESTROYED_NOTICE}),t._transportEvent=e,this._webTransportFsm.processEvent(t);break;case _.SEND_ERROR:t=new u({name:g.SEND_ERROR}),t._transportEvent=e,this._webTransportFsm.processEvent(t);break;case _.CONNECT_TIMEOUT:t=new u({name:g.CONNECT_TIMEOUT}),t._transportEvent=e,this._webTransportFsm.processEvent(t);break;case _.DOWNGRADE_FAILED:this._lastDowngradeSucceeded=!1;break;case _.DOWNGRADE_SUCCEEDED:this._lastDowngradeSucceeded=!0;break;default:this._eventCB(e)}}connect(){const e=new u({name:g.CONNECT});return this._webTransportFsm.processEvent(e),d.OK}connectInternal(){this._transportSession=null;const e=this._transportHandler.getTransportProtocol();switch(this._props.transportProtocol=e,e){case h.HTTP_BASE64:case h.HTTP_BINARY:case h.HTTP_BINARY_STREAMING:this._transportSession=new i(this._url,(e=>this.handleTransportEvent(e)),this._client,this._props);break;case h.WS_BINARY:this._transportSession=new E(this._url,(e=>this.handleTransportEvent(e)),this._client,this._props);break;default:throw c(`Web transport unrecognized TransportProtocol: ${e}`),new r(`No transport session provider for scheme: ${e}`,s.CONNECTION_ERROR,e)}return a(`Connect Transport ${e}`),this._transportSession.connect()}destroy(e,t){const n=new u({name:g.DESTROY});return n._destroyMsg=e,n._subcode=t,this._webTransportFsm.processEvent(n),d.OK}forceFailure(e){const t=null!=e?e:"";return this._transportSession&&this._transportSession._socket._sender._socket.destroy(new Error(t)),d.OK}beginDowngrade(e,t){return!!this._transportHandler.canCompleteDowngrade()&&(this.destroyInternal(e,t),!0)}completeDowngrade(){return!!this._transportHandler.canCompleteDowngrade()&&this._transportHandler.completeDowngrade()}destroyInternal(e,t){this._transportSession&&this._transportSession.destroy(e,t)}flush(e){return this._transportSession.flush(e)}getConnError(){return this._transportSession?this._transportSession._connError:null}getInfoStr(){return this._transportSession?this._transportSession.getInfoStr():"Not connected."}getTransportProtocol(){return this._transportHandler.getTransportProtocol()}getClientStats(){return this._transportSession?this._transportSession.getClientStats():null}requestDowngrade(e,t){this._lastDowngradeSucceeded=void 0;const n=new u({name:g.DOWNGRADE});return n._downgradeMsg=e,n._subcode=t,this._webTransportFsm.processEvent(n),this._lastDowngradeSucceeded}send(e,t){return this._transportSession.send(e,t)}}},1261:(e,t,n)=>{const s=n(199),r=n(9620),i=n(6247),{Baggage:o,TraceContextSetter:a}=n(2288),c=n(769),{BinaryMetaBlock:u,KeepAliveMessage:l}=n(8247),{ContentSummaryType:h}=n(8283),{Hex:p,Long:d,Convert:{stringToUint8Array:_,anythingToBuffer:E}}=n(9783),{Lazy:g}=n(7444),{LogFormatter:T}=n(2694),{parseAdpAt:S}=n(1318),{parseCCAt:m}=n(1868),{ParseSMF:f}=n(8103),{PriorityUserCosMap:I}=n(394),{SMFProtocol:R}=n(5052),{SMP:C}=n(9963),{Transport:A}=n(2318),O=(n(5594),n(595)),{Check:N}=n(802),{formatHexString:y}=p,{lazyValue:P}=g,{parseSMFAt:D}=f,{parseSMPAt:b}=C,{parseTsSmfMsgAt:M}=A,v=new T("[smf-decode]"),{LOG_DEBUG:w,LOG_ERROR:L,LOG_WARN:U}=v,F=P((()=>(new I).reverse)),x={10:i.MessageType.MAP,11:i.MessageType.STREAM,7:i.MessageType.TEXT};function B(e,t){const n=t,s=c.Codec.parseSingleElement(e.payload,0);if(!s||s.getType()!==c.SDTFieldType.STREAM)return;const r=s.getValue();let u=r.getNext();if(u&&u.getType()===c.SDTFieldType.BYTEARRAY&&u._value&&u._value.length>0){let e=u._value.readUInt8(0);if(64&e&&n._setPayloadCompressed(e),128&e||(n._messageType=x[15&e]||i.MessageType.BINARY),u._value.length>1){const e=u._value.readUInt8(1);n.setAsReplyMessage(!!(128&e))}}if(u=r.getNext(),u&&u.getType()===c.SDTFieldType.MAP){const e=u.getValue(),t=e.getField("p"),s=e.getField("h");if(t&&n.setUserPropertyMap(t.getValue()),s){const e=s.getValue(),t=e.getField("ci"),r=e.getField("mi"),i=e.getField("mt"),c=e.getField("rt"),u=e.getField("si"),l=e.getField("sn"),h=e.getField("ts"),p=e.getField("ex"),d=e.getField("ce"),_=e.getField("ct"),E=e.getField("bag"),g=e.getField("ctx");if(t&&n.setCorrelationId(t.getValue()),r&&n.setApplicationMessageId(r.getValue()),i&&n.setApplicationMessageType(i.getValue()),c&&n.setReplyTo(c.getValue()),u&&n.setSenderId(u.getValue()),l&&n.setSequenceNumber(l.getValueNoThrow()),h&&n.setSenderTimestamp(h.getValue()),p&&n.setGMExpiration(p.getValue()),d&&n.setHttpContentEncoding(d.getValue()),_&&n.setHttpContentType(_.getValue()),E){const e=new o;e.setBaggage(E.getValue()),n._setBaggage(e)}if(g){const e=a.fromTraceContext(g.getValue());n._setCreationContext(e)}}}}const G={decodeCompoundMessage:function(e,t){const n=D(e,t);if(!n)return null;const o=t+n.headerLength,c=n.payloadLength;let p;switch(n.smf_protocol){case R.TSESSION:if(p=M(e,o,n),!p)break;return p.smfHeader=n,p;case R.TRMSG:return p=new i.Message,p._smfHeader=n,function(e,t,n,s){const o=t;if(o._setDeliverToOne(!!e.smf_dto),o._setDeliveryMode(e.pm_deliverymode||i.MessageDeliveryModeType.DIRECT),null!==e.pm_tr_topicname_bytes&&o._setDestination(r.DestinationFromNetwork.createDestinationFromBytes(e.pm_tr_topicname_bytes)),o._setDiscardIndication(!!e.smf_di),o._setElidingEligible(!!e.smf_elidingEligible),o._setDMQEligible(!!e.smf_deadMessageQueueEligible),o._setUserCos(F.value.get(e.smf_priority)),o._setPriority(e.pm_msg_priority),e.pm_userdata&&o._setUserData(e.pm_userdata),o.setRedelivered(!!e.pm_ad_redelflag||!!e.pm_ad_flowredelflag),o.setFlowId(e.pm_ad_flowid),o.setGuaranteedMessageId(e.pm_ad_msgid),o.setGuaranteedPreviousMessageId(e.pm_ad_prevmsgid),o.setPublisherId(e.pm_ad_publisherid),o.setPublisherMessageId(e.pm_ad_publishermsgid),o.setTopicSequenceNumber(e.pm_ad_topicSequenceNumber),o.getDeliveryMode()===i.MessageDeliveryModeType.DIRECT?o.setDeliveryCount(-1):e.pm_ad_redeliveryCount?o.setDeliveryCount(e.pm_ad_redeliveryCount+1):o.setDeliveryCount(1),e.pm_ad_spooler_unique_id&&o._setSpoolerUniqueId(e.pm_ad_spooler_unique_id),e.pm_ad_replication_mate_ack_message_id?o._setSpoolerMessageId(e.pm_ad_replication_mate_ack_message_id):e.pm_ad_local_spooler_message_id&&o._setSpoolerMessageId(e.pm_ad_local_spooler_message_id),d.isLong(e.pm_ad_ttl)?o.setTimeToLive(e.pm_ad_ttl.toNumber()):o.setTimeToLive(e.pm_ad_ttl),e.pm_ts_transport_context){const t=a.fromTraceContext(e.pm_ts_transport_context);o._setTransportContext(t)}const c=s+e.headerLength,l=e.pm_content_summary;if(l&&l.length){for(let e=0,t=l.length;e<t;++e){const t=l[e],s=c+t.position,r=c+t.position+t.length;switch(t.type){case h.BINARY_ATTACHMENT:o._setBinaryAttachment(n.slice(s,r));break;case h.BINARY_METADATA:{const e=u.fromEncodedSmf(n,s);o.binaryMetadataChunk=e,0===e.type&&B(e,o);break}case h.XML_META:o._setXmlMetadata(n.toString("latin1",s,r));break;case h.XML_PAYLOAD:o._setXmlContentInternal(n.toString("latin1",s,r));break;default:L(`Unhandled ContentSummaryType: ${h.describe(t.type)}`)}}if(o._getPayloadCompressed()&&N.anything(o.getHttpContentEncoding())&&o.getHttpContentEncoding().endsWith("deflate"))try{let e;if(e=O.unzlibSync(o._binaryAttachment),e=E(e),o._setBinaryAttachment(e),o._setPayloadCompressed(null),"deflate"===o.getHttpContentEncoding().trim())delete o._httpContentEncoding;else{let e=o.getHttpContentEncoding().slice(0,-7).trimRight();e.endsWith(",")?(e=e.slice(0,-1),o.setHttpContentEncoding(e)):delete o._httpContentEncoding}}catch(e){U("Message payload was not decompressed, there was an error. Leaving the message body compressed ",e),o._messageType=i.MessageType.BINARY}}else o._setBinaryAttachment(e.payloadLength>0?n.slice(c,c+e.payloadLength):void 0)}(n,p,e,t),p;case R.ADCTRL:return p=S(e,o,c),p.smfHeader=n,p;case R.CLIENTCTRL:if(p=m(e,o,c),!p)break;return p.smfHeader=n,p;case R.SMP:if(p=b(e,o),!p)break;return p.smfHeader=n,p;case R.KEEPALIVE:case R.KEEPALIVEV2:return p=new l,p.smfHeader=n,p;default:L(`Unknown protocol: 0x${y(n.smf_protocol)}, dump message content: \n${s.Debug.formatDumpBytes(e.slice(t,t+n.messageLength).toString("latin1"),!0,0)}`)}return null}};e.exports.Decode=G},1278:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.SMFSMPMessageTypeFlags=s.new({FLAG_FILTER:1,FLAG_PERSIST:2,SMF_SMP_FLAG_TOPIC:4,SMF_SMP_FLAG_RESPREQUIRED:8,SMF_SMP_FLAG_DELIVERALWAYS:16})},1318:(e,t,n)=>{const s=n(3450),{AdProtocolMessage:r,SMFParameter:i}=n(8247),{Bits:o,Convert:a}=n(9783),{LOG_DEBUG:c,LOG_INFO:u,LOG_ERROR:l}=n(2694),{ReplayStartType:h}=n(9309),{get:p,set:d}=o,{int8ToStr:_,int16ToStr:E,int24ToStr:g,int32ToStr:T}=a;function S(e,t){const n=[];let s=0;return s=o.set(s,e,6,2),s=o.set(s,t,0,6),n.push(a.int8ToStr(s)),n.push(a.int8ToStr(2)),n.join("")}function m(e,t,n){const s=[];let r=0;return r=o.set(r,e,6,2),r=o.set(r,t,0,6),s.push(a.int8ToStr(r)),s.push(a.int8ToStr(3)),s.push(a.int8ToStr(n)),s.join("")}function f(e,t,n){const s=[];let r=0;return r=o.set(r,e,6,2),r=o.set(r,t,0,6),s.push(a.int8ToStr(r)),s.push(a.int8ToStr(4)),s.push(a.int16ToStr(n)),s.join("")}function I(e,t,n){const s=[];let r=0;return r=o.set(r,e,6,2),r=o.set(r,t,0,6),s.push(a.int8ToStr(r)),s.push(a.int8ToStr(6)),s.push(a.int32ToStr(n)),s.join("")}function R(e,t,n){const s=[];let r=0;return r=o.set(r,e,6,2),r=o.set(r,t,0,6),s.push(a.int8ToStr(r)),s.push(a.int8ToStr(10)),s.push(a.int64ToStr(n)),s.join("")}const C={};function A(e,t,n){const s=[],r=n.type,i=n.value;let c=0;switch(c=o.set(c,e,6,2),c=o.set(c,t,0,6),s.push(a.int8ToStr(c)),s.push(a.int8ToStr(C[r])),s.push(a.int8ToStr(r)),r){case h.DATE:s.push(a.int64ToStr(i));break;case h.RGMID:s.push(a.int64ToStr(i.suid)),s.push(a.int64ToStr(i.messageId));case h.BEGINNING:}return s.join("")}function O(e,t,n,s,r=void 0){const i=[];let c=0;c=o.set(c,e,6,2),c=o.set(c,t,0,6);const u=null==r||0===r?18:19;return i.push(a.int8ToStr(c)),i.push(a.int8ToStr(u)),i.push(a.int64ToStr(n)),i.push(a.int64ToStr(s)),null!=r&&r>0&&i.push(a.int8ToStr(r)),i.join("")}function N(e,t,n){const s=[];let r=0;r=o.set(r,e,6,2),r=o.set(r,t,0,6),s.push(a.int8ToStr(r));let i=0;return n.length<=253?(i=n.length+2,s.push(a.int8ToStr(i))):(i=0,s.push(a.int8ToStr(i)),s.push(a.int32ToStr(n.length+5))),s.push(n),s.join("")}C[h.BEGINNING]=3,C[h.DATE]=11,C[h.RGMID]=19,e.exports.parseAdpAt=function(e,t){if(t+3>e.length)return!1;let n=t,s=e.readUInt8(n);n++;const o=p(s,0,6);let a,c;if(o<3){const t=e.readUInt16BE(n);n+=2,c=p(t,12,4),a=p(t,0,12),a<<=2}else{if(3!==o)return l("Found unsupported ADP Version",o),!1;s=e.readUInt8(n),n++,c=p(s,0,8),a=e.readUInt32BE(n),n+=4}if(t+a>e.length)return l(`Invalid Asssured Control Protocol length=${a} exceeds remaining message buffer = ${e.length-t}`),!1;const u=new r(c,o);for(;n<t+a;){s=e.readUInt8(n),n++;const r=p(s,6,2),o=p(s,0,6);if(0===o)continue;if(n>=t+a)return l(`Invalid Asssured Control Protocol parameter=${o} at position =${n}`),!1;let c,h=e.readUInt8(n);if(n++,0===h){if(n+5>t+a)return l(`Invalid Asssured Control Protocol parameter=${o} at position =${n}`),!1;h=e.readUInt32BE(n),n+=4,c=h-5}else c=h-2;if(h<=0)return!1;if(n+c>t+a)return l(`Invalid Asssured Control Protocol parameter=${o} length =${c} invalid at position =${n}`),!1;const d=new i(r,o,null,e,n,n+c);u.addParameter(d),n+=c}return u},e.exports.encAdp=function(e){const t=[],n=e.getParameterArray();let r;for(r=0;r<n.length;r++){const e=n[r];if(void 0!==e)switch(e.getType()){case s.SMFAdProtocolParam.WINDOW:case s.SMFAdProtocolParam.EP_DURABLE:case s.SMFAdProtocolParam.ACCESSTYPE:case s.SMFAdProtocolParam.FLOWTYPE:case s.SMFAdProtocolParam.EP_RESPECTS_TTL:case s.SMFAdProtocolParam.TRANSACTION_CTRL_MESSAGE_TYPE:case s.SMFAdProtocolParam.TRANSACTED_SESSION_STATE:case s.SMFAdProtocolParam.ACTIVE_FLOW_INDICATION:case s.SMFAdProtocolParam.WANT_FLOW_CHANGE_NOTIFY:case s.SMFAdProtocolParam.MAX_REDELIVERY:t.push(m(e.getUh(),e.getType(),e.getValue()));break;case s.SMFAdProtocolParam.EP_BEHAVIOUR:case s.SMFAdProtocolParam.PARTITION_GROUP_ID:t.push(f(e.getUh(),e.getType(),e.getValue()));break;case s.SMFAdProtocolParam.FLOWID:case s.SMFAdProtocolParam.TRANSPORT_WINDOW:case s.SMFAdProtocolParam.EP_ALLOTHER_PERMISSION:case s.SMFAdProtocolParam.EP_QUOTA:case s.SMFAdProtocolParam.EP_MAX_MSGSIZE:case s.SMFAdProtocolParam.GRANTED_PERMISSION:case s.SMFAdProtocolParam.TRANSACTED_SESSION_ID:case s.SMFAdProtocolParam.PUBLISHER_ID:t.push(I(e.getUh(),e.getType(),e.getValue()));break;case s.SMFAdProtocolParam.LASTMSGIDSENT:case s.SMFAdProtocolParam.LASTMSGIDACKED:case s.SMFAdProtocolParam.LASTMSGIDRECEIVED:case s.SMFAdProtocolParam.TRANSACTION_ID:case s.SMFAdProtocolParam.ENDPOINT_ERROR_ID:t.push(R(e.getUh(),e.getType(),e.getValue()));break;case s.SMFAdProtocolParam.REPLAY_START_LOCATION:{const n=e.getValue();void 0===n.value?t.push(m(e.getUh(),e.getType(),n.type)):t.push(A(e.getUh(),e.getType(),n));break}case s.SMFAdProtocolParam.APPLICATION_ACK:{const n=e.getValue(),s=e.getUh(),r=e.getType();n.forEach(((e,n)=>{for(let i=0;i<e.length;++i){const o=e[i];t.push(O(s,r,o[0],o[1],n))}}));break}case s.SMFAdProtocolParam.QUEUENAME:case s.SMFAdProtocolParam.DTENAME:case s.SMFAdProtocolParam.TOPICNAME:case s.SMFAdProtocolParam.FLOWNAME:case s.SMFAdProtocolParam.SELECTOR:case s.SMFAdProtocolParam.TRANSACTED_SESSION_NAME:t.push(N(e.getUh(),e.getType(),e.getValue()));break;case s.SMFAdProtocolParam.TRANSACTION_FLOW_DESCRIPTOR_PUB_NOTIFY:case s.SMFAdProtocolParam.TRANSACTION_FLOW_DESCRIPTOR_PUB_ACK:case s.SMFAdProtocolParam.TRANSACTION_FLOW_DESCRIPTOR_SUB_ACK:break;case s.SMFAdProtocolParam.NOLOCAL:case s.SMFAdProtocolParam.CUT_THROUGH:t.push(S(e.getUh(),e.getType()));break;case s.SMFAdProtocolParam.APPLICATION_PUB_ACK:break;default:u("Unrecognized ADProtocol Parameter in Message")}}const i=t.join(""),o=[];if(2===e.version){let t=0;t=d(t,0,22,2),t=d(t,e.version,16,6),t=d(t,e.msgType,12,4);let n=4-(3+i.length&3);const s=3+i.length+n>>2;for(t=d(t,s,0,12),o.push(g(t)),o.push(i),4===n&&(n=0);n>0;)o.push(_(0)),n--}else if(3===e.version){let t=0;t=d(t,0,14,2),t=d(t,e.version,8,6),t=d(t,e.msgType,0,8),o.push(E(t)),o.push(T(6+i.length)),o.push(i)}else l(`Invalid Version ${e.version} found while encoding`);return o.join("")}},1382:(e,t,n)=>{var s=n(2195);const{Enum:r}=n(7444),{LOG_TRACE:i,LOG_DEBUG:o}=n(2694),{Long:a}=n(9783),c=r.new({OK:0,DUPLICATE:1,OUT_OF_ORDER:2});e.exports={TransportAcks:class{constructor(e=0){const t="number"==typeof e?a.fromNumber(e,!0):a.fromValue(e);this.lastAcked=t,this._acksPending=0}reset(){this._acksPending=0,this.lastAcked=a.ZERO}tryReceive(e,t){return this._lastReceived.lt(t)?(this._lastReceived,c.OUT_OF_ORDER):this._lastReceived.gte(e)?(this._lastReceived,this._acksPending++,c.DUPLICATE):(this._lastReceived=e,this._acksPending++,c.OK)}setAcked(){this._lastAcked=a.fromValue(this._lastReceived),this._acksPending=0}get acksPending(){return this._acksPending}get lastAcked(){return this._lastAcked}set lastAcked(e){e.toString(),Object.assign(this,{_lastAcked:a.fromValue(e),_lastReceived:a.fromValue(e)})}get lastReceived(){return this._lastReceived}toString(){return s(this)}},TransportAckResult:c}},1435:(e,t,n)=>{const s=n(617),{assert:r}=n(7444),{Destination:i}=n(5136),{DestinationType:o}=n(8805);class a extends i{constructor(e){r(e.name,"Queue name not supplied"),r(e.type===o.QUEUE||e.type===o.TEMPORARY_QUEUE,"Queue spec.type is invalid"),r(e.bytes,"Queue spec missing bytes"),r(void 0!==e.offset,"Queue spec missing offset"),super(e)}getOffset(){return this._offset}get offset(){return this.getOffset()}inspect(){return`[Queue ${this.getName()}]`}static createFromLocalName(e){const t=s.DestinationUtil.validateAndEncode(o.QUEUE,e);if(t.error)throw t.error;return new a({name:e,type:o.QUEUE,isValidated:!0,bytes:t.bytes,offset:t.offset,isWildcarded:t.isWildcarded,subscriptionInfo:t.subscriptionInfo})}}e.exports.Queue=a},1493:(e,t,n)=>{const s=n(7339),{FsmObject:r}=n(6031),{Iterator:i}=n(7444),{LOG_TRACE:o}=n(2694),{makeIterator:a}=i;class c extends r{constructor(e){super(e),this.impl=this.impl||{},this.impl.logPadding=""}getStateMachine(){return this.impl.ancestorList[0]}initial(e){return this.impl.initialReaction&&this.log(`Replacing ${this} initialReaction ${this.impl.initialReaction} with ${e}`),this.impl.initialReaction=e.bind(this),this}transitionTo(e,t){return new c.ReactionResult({caller:this,destState:e,action:t})}terminate(e){return new c.ReactionResult({caller:this,destState:this.getStateMachine().getFinalState(),action:e})}getAncestorList(){return this.impl.ancestorList}log(...e){this.impl.logPadding}onInitial(e){let t;if(this.impl.initialReaction){if(this.log(`Initial: for ${this}`),t=this.impl.initialReaction(e),t.external)throw new Error(`Initial reaction for ${this} returned external transitions`);return t}if(!(this instanceof s.State))throw new Error(`Missing initial reaction for ${this}`);return this.transitionTo(this)}processReactionResult(e,t){let n=this;if(!e.destState)return this;const s=this.lowestCommonAncestor(e);for(;n!==s.deref();)n.onExit(),n=n.getParent();for(e.action&&e.action(n,t),n.log(`Action: transition to ${e.destState} in context ${n}`),s.incr();!s.end();s.incr())n=s.deref(),n.onEntry();const r=n.onInitial(t);return r.destState!==n?n.processReactionResult(r,t):n}lowestCommonAncestor(e){const t=this.impl.ancestorList,n=e.destState.getAncestorList();let s;if(t[0]!==n[0])throw new Error(`No common ancestor between (${this} in ${t[0]}) and (${e.destState} in ${n[0]})`);if(this===e.destState)s=t.length,e.external&&--s;else{for(s=1;s<t.length&&t[s]===n[s];++s);s!==t.length&&s!==n.length||e.external&&--s}return a(n,s-1)}setLogPadding(e){this.impl.logPadding=e}}c.ReactionResult=class{constructor(e){if(!(e&&e.caller&&e.caller instanceof c))throw new Error("spec.caller is required to be a StateContext");if(!e.caller.getStateMachine().isRunning())throw new Error("ReactionResult objects can only be created while processing events");if(e.destState){if(!(e.destState instanceof s.State))throw new Error("destState must be a State object");if(e.action&&"function"!=typeof e.action)throw new Error("action must be a function");this.destState=e.destState,this.action=e.action,this.external=e.external}}},e.exports.StateContext=c},1517:(e,t,n)=>{const{LOG_DEBUG:s,LOG_TRACE:r}=n(2694),{TransportBase:i}=n(383),{TransportClientStats:o}=n(8188),{TransportReturnCode:a}=n(9944),{TransportSessionState:c}=n(3304);e.exports.WebTransportSessionBase=class extends i{constructor(e,t,n,s){super(e,t,n,s),this._connectTimeout=s.transportDowngradeTimeoutInMsecs,this._connectTimer=null,this._clientstats=new o,this._sendBufferMaxSize=s.sendBufferMaxSize,this._maxPayloadBytes=s.maxWebPayload,this._queuedData=[],this._queuedDataSize=0,this._canSendNeeded=!1,this._state=c.DOWN,this._connError=null}getClientStats(){return this._clientstats}createConnectTimeout(){this._connectTimeout>0&&(this._connectTimer=setTimeout((()=>{this.connectTimerExpiry()}),this._connectTimeout))}cancelConnectTimeout(){this._connectTimer&&(clearTimeout(this._connectTimer),this._connectTimer=null)}connectTimerExpiry(){}allowEnqueue(e){return 0===this._queuedDataSize||e+this._queuedDataSize<=this._sendBufferMaxSize}enqueueFailNoSpace(){return this._canSendNeeded=!0,a.NO_SPACE}flush(e){e()}getQueuedDataToSend(){let e="",t=this._maxPayloadBytes;if(this.getBufferedAmount&&this.getBufferedAmount(),this.getBufferedAmount){if(t=this._maxPayloadBytes-this.getBufferedAmount(),t<=0)return this._maxPayloadBytes,this.getBufferedAmount(),this._bufferedAmountQueryIntervalInMsecs*this._bufferedAmountQueryIntervalDelayMultiplier<=4e3&&(this._bufferedAmountQueryIntervalDelayMultiplier*=2),e;this._bufferedAmountQueryIntervalDelayMultiplier=1}if(this._queuedDataSize>t){let n=t;for(;n&&this._queuedDataSize;){const t=this._queuedData[0],s=t.length;s>n?(e+=t.substr(0,n),this._queuedData[0]=t.substr(n),this._queuedDataSize-=n,n=0):(e+=this._queuedData.shift(),n-=s,this._queuedDataSize-=s,this._clientstats.msgWritten++)}}else e=this._queuedData.join(""),this._clientstats.msgWritten+=this._queuedData.length,this._queuedData=[],this._queuedDataSize=0;return e.length,e}}},1529:(e,t,n)=>{const{TransportProtocol:s}=n(9072),{TSHState:r}=n(3718),{WebTransportCapabilities:i}=n(261);e.exports.StateWebSocketBinary=class extends r{constructor(e,t,n){super(e,s.WS_BINARY,t,n)}validateLegal(){return i.webSocket()}}},1537:(e,t,n)=>{const{Check:s}=n(7434),{ErrorSubcode:r,OperationError:i}=n(6706),o={};function a(e,t=null,n=o){const s=t?`; expected: ${t}`:"",i=n!==o?`; got: ${n}`:"";return e===r.PARAMETER_INVALID_TYPE?`Parameter type was invalid${s}${i}`:r.nameOf(e).toLowerCase().replace(/_/," ")+s}function c(e,t,n,s,r,...o){return s(r,...o)?r:function(e,t,n){throw new i(`Parameter ${e} failed validation`,t,n)}(e,t,n)}const u=e=>e&&e.constructor&&e.constructor.name||typeof e,l={isArray:function(e,t,n=r.PARAMETER_INVALID_TYPE,i=a(n,"array",t)){return c(e,n,i,s.isArray,t)},isBoolean:function(e,t,n=r.PARAMETER_INVALID_TYPE,i=a(n,"boolean",t)){return c(e,n,i,s.boolean,t)},isBooleanOrNothing:function(e,t,n=r.PARAMETER_INVALID_TYPE,i=a(n,"boolean or nothing",t)){return c(e,n,i,s.boolean.orNothing,t)},isEnumMember:function(e,t,n,i=r.PARAMETER_OUT_OF_RANGE,o=a(i,`one of [${n.names.join(", ")}]`,t)){return c(e,i,o,s.member,t,n)},isEnumMemberOrNothing:function(e,t,n,i=r.PARAMETER_OUT_OF_RANGE,o=a(i,`one of [${n.names.join(", ")}]`,t)){return c(e,i,o,s.member.orNothing,t,n)},isFunction:function(e,t,n=r.PARAMETER_INVALID_TYPE,i=a(n,"function",t)){return c(e,n,i,s.function,t)},isFunctionOrNothing:function(e,t,n=r.PARAMETER_INVALID_TYPE,i=a(n,"function or nothing",t)){return c(e,n,i,s.function.orNothing,t)},isInstanceOf:function(e,t,n,i=r.PARAMETER_INVALID_TYPE,o=a(i,n.name,u(t))){return c(e,i,o,s.instanceOf,t,n)},isInstanceOfOrNothing:function(e,t,n,i=r.PARAMETER_INVALID_TYPE,o=a(i,`${n.name} or nothing`,u(t))){return c(e,i,o,s.instanceOf.orNothing,t,n)},isInstanceOfOrNull:function(e,t,n,i=r.PARAMETER_INVALID_TYPE,o=a(i,`${n.name} or null`,u(t))){return c(e,i,o,s.instanceOf.orNull,t,n)},isInstanceOfOrUndefined:function(e,t,n,i=r.PARAMETER_INVALID_TYPE,o=a(i,`${n.name} or undefined`,u(t))){return c(e,i,o,s.instanceOf.orUndefined,t,n)},isMember:function(e,t,n,i=r.PARAMETER_OUT_OF_RANGE,o=a(i,`one of ${n.name}.[${function(e){return Object.keys(e).map((t=>e[t]))}(n).join(", ")}]`,t)){return c(e,i,o,s.member,t,n)},isNumber:function(e,t,n=r.PARAMETER_INVALID_TYPE,i=a(n,"number",t)){return c(e,n,i,s.number,t)},isNumberOrNothing:function(e,t,n=r.PARAMETER_INVALID_TYPE,i=a(n,"number or nothing",t)){return c(e,n,i,s.number.orNothing,t)},isNumberOrNull:function(e,t,n=r.PARAMETER_INVALID_TYPE,i=a(n,"number or null",t)){return c(e,n,i,s.number.orNull,t)},isRangeCompare:function(e,t,n,i,o=r.PARAMETER_OUT_OF_RANGE,u=a(o,`${n} ${i}`,t)){return c(e,o,u,s.rangeCompare,t,n,i)},isRangeCompareOrNothing:function(e,t,n,i,o=r.PARAMETER_OUT_OF_RANGE,u=a(o,`${n} ${i} or nothing`,t)){return c(e,o,u,s.rangeCompare.orNothing,t,n,i)},isStringOrNull:function(e,t,n=r.PARAMETER_INVALID_TYPE,i=a(n,"string or null",t)){return c(e,n,i,s.string.orNull,t)},isString:function(e,t,n=r.PARAMETER_INVALID_TYPE,i=a(n,"string",t)){return c(e,n,i,s.string,t)},isStringOrNothing:function(e,t,n=r.PARAMETER_INVALID_TYPE,i=a(n,"string or nothing",t)){return c(e,n,i,s.string.orNothing,t)},isValue:function(e,t,n,i=r.PARAMETER_OUT_OF_RANGE,o=a(i,`must be ${t}`)){return c(e,i,o,s.equal,t,n)}};e.exports.Parameter=l},1618:(e,t,n)=>{const{DestinationType:s}=n(8805),{DestinationUtil:r}=n(617),{Queue:i}=n(1435),{Topic:o}=n(8335);function a(e,t=void 0){if(null===e||0===e.length)return null;const n={name:e,bytes:t||r.encodeBytes(e)};if("#"===e[0]){if(e.startsWith("#P2P/QUE/")){const t=9;return n.name=e.substr(t),n.type=s.QUEUE,n.offset=t,new i(n)}if(e.startsWith("#P2P/QTMP/"))return n.name=e,n.type=s.TEMPORARY_QUEUE,n.offset=0,new i(n)}return new o(n)}const c={createDestinationFromBytes:function(e){return null===e||0===e.length?null:a(r.decodeBytes(e),e)},createDestinationFromName:a};e.exports.DestinationFromNetwork=c},1643:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.SMFTransportSessionMessageType=s.new({CREATE:0,CREATE_RESP:1,DESTROY:2,DESTROY_RESP:3,DATA:4,DATA_TOKEN:5,DATA_STREAM_TOKEN:6})},1663:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.SessionOperation=s.new({CONNECT:"CONNECT",DISCONNECT:"DISCONNECT",CTRL:"CTRL",SEND:"SEND",QUERY_OPERATION:"QUERY_OPERATION"})},1699:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.ConsumerFSMEventNames=s.new({SESSION_UP:"SESSION_UP",SESSION_UP_NO_AD:"SESSION_UP_NO_AD",SESSION_DOWN:"SESSION_DOWN",SESSION_DISCONNECT:"SESSION_DISCONNECT",FLOW_FAILED:"FLOW_FAILED",FLOW_UP:"FLOW_UP",FLOW_ACTIVE_IND:"FLOW_ACTIVE_IND",FLOW_CLOSE:"FLOW_CLOSE",FLOW_OPEN:"FLOW_OPEN",FLOW_UNBOUND:"FLOW_UNBOUND",ACK:"ACK",ACK_TIMEOUT:"ACK_TIMEOUT",BIND_TIMEOUT:"BIND_TIMEOUT",CREATE_TIMEOUT:"CREATE_TIMEOUT",UNBIND_TIMEOUT:"UNBIND_TIMEOUT",CAN_SEND:"CAN_SEND",TRANSPORT_ERROR:"TRANSPORT_ERROR",DISPOSE:"DISPOSE",VIRTUALROUTER_NAME_CHANGED:"VIRTUALROUTER_NAME_CHANGED",RECONNECT_INTERVAL_TIMEOUT:"RECONNECT_INTERVAL_TIMEOUT",BIND_RESPONSE:"BIND_RESPONSE",CREATE_FAILED:"CREATE_FAILED",CREATE_SUCCESS:"CREATE_SUCCESS"})},1719:e=>{e.exports.LogImpl=class{constructor(e,t,n,s,r,i){Object.assign(this,{trace:e,debug:t,info:n,warn:s,error:r,fatal:i})}trace(){}debug(){}info(){}warn(){}error(){}fatal(){}}},1737:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.StatType=s.new({TX_TOTAL_DATA_BYTES:0,TX_TOTAL_DATA_MSGS:1,TX_DIRECT_BYTES:2,TX_DIRECT_MSGS:3,TX_CONTROL_BYTES:4,TX_CONTROL_MSGS:5,TX_REQUEST_SENT:6,TX_REQUEST_TIMEOUT:7,RX_TOTAL_DATA_BYTES:8,RX_TOTAL_DATA_MSGS:9,RX_DIRECT_BYTES:10,RX_DIRECT_MSGS:11,RX_CONTROL_BYTES:12,RX_CONTROL_MSGS:13,RX_DISCARD_MSG_INDICATION:14,RX_REPLY_MSG_RECVED:15,RX_REPLY_MSG_DISCARD:16,RX_DISCARD_SMF_UNKNOWN_ELEMENT:17,CACHE_REQUEST_SENT:18,CACHE_REQUEST_OK_RESPONSE:19,CACHE_REQUEST_FAIL_RESPONSE:20,CACHE_REQUEST_FULFILL_DISCARD_RESPONSE:21,RX_CACHE_MSG:22,CACHE_REQUEST_INCOMPLETE_RESPONSE:23,CACHE_REQUEST_LIVE_DATA_FULFILL:24,TX_PERSISTENT_BYTES:25,TX_PERSISTENT_MSGS:26,TX_NONPERSISTENT_BYTES:27,TX_NONPERSISTENT_MSGS:28,TX_PERSISTENT_BYTES_REDELIVERED:29,TX_PERSISTENT_REDELIVERED:30,TX_NONPERSISTENT_BYTES_REDELIVERED:31,TX_NONPERSISTENT_REDELIVERED:32,TX_ACKS_RXED:33,TX_WINDOW_CLOSE:34,TX_ACK_TIMEOUT:35,RX_PERSISTENT_BYTES:36,RX_PERSISTENT_MSGS:37,RX_NONPERSISTENT_BYTES:38,RX_NONPERSISTENT_MSGS:39,RX_ACKED:40,RX_DISCARD_DUPLICATE:41,RX_DISCARD_NO_MATCHING_CONSUMER:42,RX_DISCARD_OUT_OF_ORDER:43,RX_SETTLE_REJECTED:44,RX_SETTLE_FAILED:45,RX_SETTLE_ACCEPTED:46})},1851:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.QueueAccessType=s.new({EXCLUSIVE:"EXCLUSIVE",NONEXCLUSIVE:"NONEXCLUSIVE"})},1868:(e,t,n)=>{const{Bits:s,Convert:r}=n(9783),{ClientCtrlMessage:i,SMFParameter:o}=n(8247),{LOG_ERROR:a}=n(2694),{get:c,set:u}=s,{int8ToStr:l,int16ToStr:h,int32ToStr:p}=r;e.exports.parseCCAt=function(e,t,n){const s=new i;if(n<6||t+6>e.length)return s;let r=t;const u=e.readUInt16BE(r);r+=2;const l=c(u,8,3),h=c(u,0,8),p=e.readUInt32BE(r);if(r+=4,1!==l)return a(`Unsupported ClientCtrl version ${l}`),!1;if(p<=0||t+p>e.length)return!1;for(s.msgType=h,s.version=l;r<t+p;){const t=e.readUInt8(r);r++;const n=c(t,7,1),i=c(t,0,7),a=e.readUInt32BE(r);if(a<=0)return!1;r+=4;const u=a-5,l=new o(n,i,null,e,r,r+u);s.addParameter(l),r+=u}return s},e.exports.encCC=function(e){const t=[],n=e.getParameterArray();for(let e=0,s=n.length;e<s;++e){const s=n[e];if(void 0===s)continue;let r=0;r=u(r,s.getUh(),7,1),r=u(r,s.getType(),0,7),t.push(l(r)),t.push(p(s.getValue().length+5)),t.push(s.getValue())}const s=t.join("");let r=0;r=u(r,0,15,1),r=u(r,0,11,4),r=u(r,1,8,3),r=u(r,e.msgType,0,8);const i=[];return i.push(h(r)),i.push(p(6+s.length)),i.push(s),i.join("")}},1884:(e,t,n)=>{const{clone:s}=n(968),r=n(4386),{Codec:i}=n(769),{Convert:o}=n(9783),{Destination:a}=n(9620),{ErrorSubcode:c,OperationError:u}=n(6706),{LOG_DEBUG:l,LOG_WARN:h}=n(2694),{MessageCacheStatus:p}=n(7366),{MessageDeliveryModeType:d}=n(177),{MessageDumpFlag:_}=n(3901),{MessageDumpUtil:E}=n(8892),{MessageType:g}=n(2868),{MessageOutcome:T}=n(568),{MessageUserCosType:S}=n(6676),{Parameter:m}=n(802),{RgmidFactory:f}=n(6475),{SDTField:I,SDTFieldType:R,SDTMapContainer:C,SDTUnsupportedValueError:A}=n(769),{Baggage:O,MessageTracingSupport:N,TraceContext:y,TraceContextSetter:P}=n(2288),{ProfileBinding:D}=r,{utf8ToUcs2:b,anythingToBuffer:M}=o,{isBoolean:v,isEnumMember:w,isInstanceOf:L,isInstanceOfOrNothing:U,isNumberOrNothing:F,isStringOrNothing:x}=m,B={circular:!1,includeNonEnumerable:!1};function G(e){e._deliveryMode=d.DIRECT,e._userCos=S.COS1,e._cacheStatus=p.LIVE,e._spoolerUniqueId=void 0,e._priority=void 0,e._deliveryCount=-1,e._traceContextSetter=null,e._creationContext=null,e._transportContext=null,e._baggage=new O}class k extends N{constructor(){super(),G(this)}getType(){return this._messageType||g.BINARY}setApplicationMessageId(e){this._applicationMessageId=x("applicationMessageId",e)}getApplicationMessageId(){return this._applicationMessageId}setApplicationMessageType(e){this._applicationMessageType=x("applicationMessageType",e)}getApplicationMessageType(){return this._applicationMessageType}getBinaryAttachment(){return this._binaryAttachment&&D.value.byteArrayAsString?this._binaryAttachment.toString("latin1"):this._binaryAttachment}setBinaryAttachment(e){e&&(this._messageType=g.BINARY),this._setBinaryAttachment(M(e))}_setBinaryAttachment(e){this._binaryAttachment=e}getCacheRequestId(){return this._cacheRequestId}_setCacheRequestID(e){this._cacheRequestId=e}getCorrelationId(){return this._correlationId}setCorrelationId(e){this._correlationId=x("correlationId",e)}getCorrelationKey(){return this._correlationKey||null}setCorrelationKey(e){this._correlationKey=e}isDeliverToOne(){return this._deliverToOne||!1}setDeliverToOne(e){this._setDeliverToOne(this._deliverToOne=v("deliverToOne",e))}_setDeliverToOne(e){this._deliverToOne=e}getDeliveryMode(){return this._deliveryMode}setDeliveryMode(e){this._setDeliveryMode(w("deliveryMode",e,d))}_setDeliveryMode(e){this._deliveryMode=e}getDestination(){return this._destination}setDestination(e){this._setDestination(L("destination",e,a))}_setDestination(e){this._destination=e}isDiscardIndication(){return this._discardIndication||!1}setDiscardIndication(e){this._setDiscardIndication(v("discardIndication",e))}_setDiscardIndication(e){this._discardIndication=e}isElidingEligible(){return this._elidingEligible||!1}setElidingEligible(e){this._setElidingEligible(v("setElidingEligible",e))}_setElidingEligible(e){this._elidingEligible=e}getHttpContentEncoding(){return this._httpContentEncoding}setHttpContentEncoding(e){this._httpContentEncoding=x("httpContentEncoding",e),""===this._httpContentEncoding&&delete this._httpContentEncoding}getHttpContentType(){return this._httpContentType}setHttpContentType(e){this._httpContentType=x("httpContentType",e),""===this._httpContentType&&delete this._httpContentType}getPublisherId(){return this._publisherId}setPublisherId(e){this._publisherId=e}getPublisherMessageId(){return this._publisherMsgId}setPublisherMessageId(e){this._publisherMsgId=e}getTimeToLive(){return this._timeToLive}setTimeToLive(e){if(null!=e){if("number"!=typeof e||isNaN(e))throw new u("Invalid type for time to live",c.PARAMETER_INVALID_TYPE);if(e<0||e>31536e7)throw new u("Invalid time to live value",c.PARAMETER_OUT_OF_RANGE);this._timeToLive=e}else this._timeToLive=e}getGMExpiration(){return this._expiration}setGMExpiration(e){this._expiration=F("GMExpiration",e)}isDMQEligible(){return this._dmqEligible||!1}setDMQEligible(e){this._setDMQEligible(v("DMQEligible",e))}_setDMQEligible(e){this._dmqEligible=e}getFlowId(){return this._flowId}setFlowId(e){this._flowId=e}getGuaranteedPreviousMessageId(){return this._guaranteedPrevMsgId}setGuaranteedPreviousMessageId(e){this._guaranteedPrevMsgId=e}_setSpoolerUniqueId(e){this._spoolerUniqueId=e}_getSpoolerUniqueId(){return void 0===this._spoolerUniqueId?f.INVALID_SUID:this._spoolerUniqueId}getMessageConsumer(){return this._consumer}setMessageConsumer(e){this._consumer=e}getGuaranteedMessageId(){return this._guaranteedMsgId}setGuaranteedMessageId(e){this._guaranteedMsgId=e}_setSpoolerMessageId(e){this._spoolerMessageId=e}getReplicationGroupMessageId(){if(void 0===this._spoolerUniqueId||f.INVALID_SUID.eq(this._spoolerUniqueId))return;const e=this._spoolerMessageId||this._guaranteedMsgId;return f.from({suid:this._spoolerUniqueId,msgid:e})}getTopicSequenceNumber(){return this._topicSequenceNumber}setTopicSequenceNumber(e){this._topicSequenceNumber=e}getDeliveryCount(){if(-1===this._deliveryCount)throw new u("Endpoint does not report delivery count.",c.INVALID_OPERATION);return this._deliveryCount}setDeliveryCount(e){this._deliveryCount=e}settle(e){if(this._acked)throw new u("Message can only be settled once",c.MESSAGE_ALREADY_ACKNOWLEDGED);if(this._deliveryMode===d.DIRECT)throw new u("Cannot settle a DIRECT message",c.MESSAGE_DELIVERY_MODE_MISMATCH);if(!this._consumer)throw new u("Cannot settle a locally-created message",c.MESSAGE_DELIVERY_MODE_MISMATCH);if(!this._consumer._sessionInterface.canAck)throw new u("Cannot settle using associated session",c.SESSION_NOT_CONNECTED);if(!this._consumer.canAck)throw new u("Cannot settle using associated Message Consumer",c.INVALID_OPERATION);if(this._consumer.getProperties().browser)throw new u("Messages delivered to a Queue Browser can only be deleted by calling QueueBrowser.removeMessageFromQueue()",c.INVALID_OPERATION);if(this._consumer._fsm.hasAutoAckSupport)return void h(`Consumer configured to auto-acknowledge messages, so message ${this._guaranteedMsgId} cannot be application settled`);const t=e;if(-1===T.values.indexOf(t))throw new u("Settlement outcome for message must be valid",c.INVALID_OPERATION);const n=this._consumer.getProperties().requiredSettlementOutcomes.some((e=>e===t));if(t!=T.ACCEPTED&&!n)throw new u(`solace.MessageOutcome.${T.nameOf(t)} not supported for this Message Consumer`,c.INVALID_OPERATION);this._consumer.applicationSettle(this._guaranteedMsgId,t),this._acked=!0}get isSettled(){return this._acked||!1}_validateBeforeAcknowledge(){if(this._acked)throw new u("Message can only be acknowledged once",c.MESSAGE_ALREADY_ACKNOWLEDGED);if(this._deliveryMode===d.DIRECT)throw new u("Cannot acknowledge a DIRECT message",c.MESSAGE_DELIVERY_MODE_MISMATCH);if(!this._consumer)throw new u("Cannot acknowledge a locally-created message",c.MESSAGE_DELIVERY_MODE_MISMATCH);if(!this._consumer._sessionInterface.canAck)throw new u("Cannot acknowledge using associated session",c.SESSION_NOT_CONNECTED);if(!this._consumer.canAck)throw new u("Cannot acknowledge using associated Message Consumer",c.INVALID_OPERATION);if(this._consumer.getProperties().browser)throw new u("Messages delivered to a Queue Browser can only be deleted by calling QueueBrowser.removeMessageFromQueue()",c.INVALID_OPERATION)}acknowledge(){this._validateBeforeAcknowledge(),this._consumer._fsm.hasAutoAckSupport?h(`Consumer configured to auto-acknowledge messages, so message ${this._guaranteedMsgId} cannot be application acknowledge`):(this._consumer.applicationAck(this._guaranteedMsgId,!1),this._acked=!0)}_autoAcknowledge(){this._validateBeforeAcknowledge(),this._consumer.applicationAck(this._guaranteedMsgId,!0),this._acked=!0}get isAcknowledged(){return this._acked||!1}isAcknowledgeImmediately(){return this._ackImmediately||!1}setAcknowledgeImmediately(e){this._setAcknowledgeImmediately(v("acknowledgeImmediately",e))}_setAcknowledgeImmediately(e){this._ackImmediately=e}getCacheStatus(){return this._cacheStatus}_setCacheStatus(e){this._cacheStatus=e}isReplyMessage(){return this._replyMessage||!1}isRedelivered(){return this._redelivered||!1}setRedelivered(e){this._redelivered=e}setAsReplyMessage(e){this._replyMessage=v("asReplyMessage",e)}getReceiverTimestamp(){return this._receiverTimestamp}getReplyTo(){return this._replyTo}setReplyTo(e){this._replyTo=U("replyTo",e,a)}getSenderId(){return this._senderId}setSenderId(e){this._senderId=x("senderId",e)}getSenderTimestamp(){return this._senderTimestamp}setSenderTimestamp(e){this._senderTimestamp=F("senderTimestamp",e)}getSequenceNumber(){if(this._sequenceNumberError)throw this._sequenceNumberError;return this._sequenceNumber}setSequenceNumber(e){e instanceof A?this._sequenceNumberError=e:(this._sequenceNumber=F("sequenceNumber",e),this._sequenceNumberError=void 0,this._autoSequenceNumber=!1)}getUserCos(){return this._userCos}getPriority(){return this._priority}setUserCos(e){this._setUserCos(w("userCos",e,S))}_setUserCos(e){this._userCos=e}setPriority(e){if(null!=e){if("number"!=typeof e||isNaN(e))throw new u("Invalid type for message priority",c.PARAMETER_INVALID_TYPE);if(e<0||e>255)throw new u("Invalid priority value",c.PARAMETER_OUT_OF_RANGE);this._setPriority(e)}else this._setPriority(void 0)}_setPriority(e){this._priority=e}getUserData(){return this._userData}setUserData(e){this._setUserData(x("userData",e))}_setUserData(e){this._userData=e}getXmlContent(){return this._xmlContent}getXmlContentDecoded(){return this._xmlContent?b(this._xmlContent):this._xmlContent}setXmlContent(e){const t=x("xmlContent",e);this._xmlContent=t?unescape(encodeURIComponent(t)):t}_setXmlContentInternal(e){this._xmlContent=x("xmlContentInternal",e)}setXmlMetadata(e){this._setXmlMetadata(x("xmlMetadata",e))}_setXmlMetadata(e){this._xmlMetadata=e}getXmlMetadata(){return this._xmlMetadata}get binaryMetadataChunk(){return this._binaryMetaChunk||null}set binaryMetadataChunk(e){this._binaryMetaChunk=e}get smfHeader(){return this._smfHeader}set smfHeader(e){this._smfHeader=e}get hasAutoSequenceNumber(){return this._autoSequenceNumber||!1}set hasAutoSequenceNumber(e){this._autoSequenceNumber=e}get hasAutoSenderTimestamp(){return this._autoSenderTimestamp||!1}set hasAutoSenderTimestamp(e){this._autoSenderTimestamp=e}getUserPropertyMap(){return this._userPropertyMap}setUserPropertyMap(e){this._userPropertyMap=U("userPropertyMap",e,C)}setSdtContainer(e){const t=U("sdtContainer",e,I);if(null==t)return this._structuredContainer=null,void this.setBinaryAttachment(null);switch(this._setBinaryAttachment(null),t.getType()){case R.MAP:this._messageType=g.MAP;break;case R.STREAM:this._messageType=g.STREAM;break;case R.STRING:this._messageType=g.TEXT;break;default:throw new u("Invalid parameter: expected SDTField Type of MAP, STREAM, or STRING.",c.PARAMETER_INVALID_TYPE)}this._structuredContainer=t}getSdtContainer(){const e=this.getType(),t=this._binaryAttachment,n=t?t.length:0;return e===g.BINARY?null:(void 0!==this._structuredContainer||(this._structuredContainer=0===n?null:i.parseSingleElement(t,0)),this._structuredContainer)}_getCompressedBinaryAttachment(){return this._compressedBinaryAttachment}_setCompressedBinaryAttachment(e){this._compressedBinaryAttachment=e}_getPayloadCompressed(){return this._payloadCompressed}_setPayloadCompressed(e){this._payloadCompressed=e}getSequenceNumber(){if(this._sequenceNumberError)throw this._sequenceNumberError;return this._sequenceNumber}getTraceContextSetter(){return super.getTraceContextSetter()}getCreationContext(){return super.getCreationContext()}getTransportContext(){return super.getTransportContext()}setTransportContext(e){super._setTransportContext(e)}getBaggage(){return super.getBaggage()}dump(e=_.MSGDUMP_FULL){const t=w("flags",e,_);return E.dump(this,t)}clone(){return s(this,B)}reset(){var e;e=this,Object.keys(e).forEach((t=>delete e[t])),G(this)}clearExtendedVarLenParams(){this._transportContext=null}}k.SOLCLIENT_USER_PROP_QUEUE_PARTITION_KEY="JMSXGroupID",e.exports.Message=k},2076:(e,t,n)=>{const s=n(2497),{LOG_ERROR:r}=n(2694),{ParseFieldHeader:i}=n(8906),{SDTDataTypes:o}=n(8605),{SDTField:a}=n(7385),{SDTFieldType:c}=n(7849),{SDTMapContainer:u}=n(7449),l={parseMapAt:function(e,t,n){const l=new u;let h=t;for(;h<t+n;){const t=i.parseFieldHeader(e,h);if(h+=t[3],t[0]!==o.String)return r("Error parsing SDTMAP, expected to find a string field as map key, and didn't"),r(`Type of key: ${t[0]}`),a.create(c.MAP,null);const n=e.toString("latin1",h,h+t[2]-1);h+=t[2];const u=i.parseFieldHeader(e,h),p=s.ParseSingleElement.parseSingleElement(e,h);h+=u[1],p&&l.addField(n,p)}return a.create(c.MAP,l)}};e.exports.ParseMap=l},2128:e=>{const t={enumerable:!0};function n(e,n,s,r=null){Object.defineProperty(e,n,Object.assign({value:s},t,r))}function s(e,s,r){return Object.defineProperty(e,s,Object.assign({configurable:!0,get:()=>{const t=r(e,s);return n(e,s,t),t},set:t=>{n(e,s,t)}},t)),e}const r={lazyProperties:function(e,t){return Object.keys(t).forEach((n=>{s(e,n,t[n])})),t},lazyProperty:s,lazyValue:function(e){return s({},"value",e)}};e.exports.Lazy=r},2157:(e,t,n)=>{const s=n(199),{SolaceError:r}=n(6706);e.exports.SDTUnsupportedValueError=class extends r{constructor(e,t,n){super("SDTUnsupportedValue",e),this.subcode=t,this.sourceData=n||""}inspect(){return super.inspect({subcode:null,sourceData:e=>s.Debug.formatDumpBytes(e,!1,0)})}getSubcode(){return this.subcode}getSourceData(){return this.sourceData}}},2195:e=>{function t(e,t){var o={seen:[],stylize:n};return arguments.length>=3&&(o.depth=arguments[2]),arguments.length>=4&&(o.colors=arguments[3]),s(t)?o.showHidden=t:t&&function(e,t){if(!t||!h(t))return e;for(var n=Object.keys(t),s=n.length;s--;)e[n[s]]=t[n[s]]}(o,t),r(o.showHidden)&&(o.showHidden=!1),r(o.depth)&&(o.depth=2),r(o.colors)&&(o.colors=!1),r(o.customInspect)&&(o.customInspect=!0),o.colors&&(o.stylize=i),g(o,e,o.depth)}function n(e,t){return e}function s(e){return"boolean"==typeof e}function r(e){return void 0===e}function i(e,n){var s=t.styles[n];return s?"["+t.colors[s][0]+"m"+e+"["+t.colors[s][1]+"m":e}function o(e){return"function"==typeof e}function a(e){return"string"==typeof e}function c(e){return null===e}function u(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function l(e){return h(e)&&"[object RegExp]"===_(e)}function h(e){return"object"==typeof e&&null!==e}function p(e){return h(e)&&("[object Error]"===_(e)||e instanceof Error)}function d(e){return h(e)&&"[object Date]"===_(e)}function _(e){return Object.prototype.toString.call(e)}function E(e){return"["+Error.prototype.toString.call(e)+"]"}function g(e,n,i){if(e.customInspect&&n&&o(n.inspect)&&n.inspect!==t&&(!n.constructor||n.constructor.prototype!==n)){var h=n.inspect(i,e);return a(h)||(h=g(e,h,i)),h}var _=function(e,t){if(r(t))return e.stylize("undefined","undefined");if(a(t)){var n="'"+JSON.stringify(t).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return e.stylize(n,"string")}return"number"==typeof t?e.stylize(""+t,"number"):s(t)?e.stylize(""+t,"boolean"):c(t)?e.stylize("null","null"):void 0}(e,n);if(_)return _;var S=Object.keys(n),m=function(e){var t={};return e.forEach((function(e,n){t[e]=!0})),t}(S);try{e.showHidden&&Object.getOwnPropertyNames&&(S=Object.getOwnPropertyNames(n))}catch(e){}if(p(n)&&(S.indexOf("message")>=0||S.indexOf("description")>=0))return E(n);if(0===S.length){if(o(n)){var f=n.name?": "+n.name:"";return e.stylize("[Function"+f+"]","special")}if(l(n))return e.stylize(RegExp.prototype.toString.call(n),"regexp");if(d(n))return e.stylize(Date.prototype.toString.call(n),"date");if(p(n))return E(n)}var I,R="",C=!1,A=["{","}"];return Array.isArray(n)&&(C=!0,A=["[","]"]),o(n)&&(R=" [Function"+(n.name?": "+n.name:"")+"]"),l(n)&&(R=" "+RegExp.prototype.toString.call(n)),d(n)&&(R=" "+Date.prototype.toUTCString.call(n)),p(n)&&(R=" "+E(n)),0!==S.length||C&&0!=n.length?i<0?l(n)?e.stylize(RegExp.prototype.toString.call(n),"regexp"):e.stylize("[Object]","special"):(e.seen.push(n),I=C?function(e,t,n,s,r){for(var i=[],o=0,a=t.length;o<a;++o)u(t,String(o))?i.push(T(e,t,n,s,String(o),!0)):i.push("");return r.forEach((function(r){r.match(/^\d+$/)||i.push(T(e,t,n,s,r,!0))})),i}(e,n,i,m,S):S.map((function(t){return T(e,n,i,m,t,C)})),e.seen.pop(),function(e,t,n){return e.reduce((function(e,t){return t.indexOf("\n"),e+t.replace(/\u001b\[\d\d?m/g,"").length+1}),0)>60?n[0]+(""===t?"":t+"\n ")+" "+e.join(",\n  ")+" "+n[1]:n[0]+t+" "+e.join(", ")+" "+n[1]}(I,R,A)):A[0]+R+A[1]}function T(e,t,n,s,i,o){var a,l,h;h={value:void 0};try{h.value=t[i]}catch(e){}try{Object.getOwnPropertyDescriptor&&(h=Object.getOwnPropertyDescriptor(t,i)||h)}catch(e){}if(h.get?l=h.set?e.stylize("[Getter/Setter]","special"):e.stylize("[Getter]","special"):h.set&&(l=e.stylize("[Setter]","special")),u(s,i)||(a="["+i+"]"),l||(e.seen.indexOf(h.value)<0?(l=c(n)?g(e,h.value,null):g(e,h.value,n-1)).indexOf("\n")>-1&&(l=o?l.split("\n").map((function(e){return"  "+e})).join("\n").substr(2):"\n"+l.split("\n").map((function(e){return"   "+e})).join("\n")):l=e.stylize("[Circular]","special")),r(a)){if(o&&i.match(/^\d+$/))return l;(a=JSON.stringify(""+i)).match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(a=a.substr(1,a.length-2),a=e.stylize(a,"name")):(a=a.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),a=e.stylize(a,"string"))}return a+": "+l}e.exports=t,t.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},t.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"}},2224:(e,t,n)=>{const{Convert:s}=n(9783),{SDTDataTypes:r}=n(8605),i={encodeHeader:function(e,t){let n=e<<2&255,i=null;return e===r.Map||e===r.Stream?(i=s.int32ToStr(t+5),n|=3):t+2<=255?(i=s.int8ToStr(t+2),n|=0):t+3<=65535?(i=s.int16ToStr(t+3),n|=1):(i=s.int32ToStr(t+5),n|=3),s.int8ToStr(n)+i}};e.exports.EncodeHeader=i},2288:(e,t,n)=>{const{Baggage:s}=n(9486),{TraceContext:r}=n(5873),{TraceContextSetter:i}=n(7317),{MessageTracingSupport:o}=n(6986);e.exports.Baggage=s,e.exports.TraceContext=r,e.exports.TraceContextSetter=i,e.exports.MessageTracingSupport=o},2299:e=>{e.exports.MessageRxCBInfo=class{constructor(e,t){this.messageRxCBFunction=e,this.userObject=t}}},2318:(e,t,n)=>{const{Bits:s,Convert:r}=n(9783),{Lazy:i}=n(7444),{LOG_ERROR:o}=n(2694),{SMFTransportSessionMessageType:a}=n(1643),{TransportSMFMessage:c}=n(8247),{get:u}=s,{int16ToStr:l,int32ToStr:h}=r,{lazyValue:p}=i;function d(e,t){return e.length-t}function _(e,t,n){let s=t;if(d(e,s)<10)return o("TsSmf parse failed: not enough data, expected at least 10B"),!1;const r=new c;r.smfHeader=n;const i=e.readUInt16BE(s);s+=2,r.uh=u(i,15,1),r.messageType=u(i,8,7);const l=u(i,0,8);if(r.tsHeaderLength=l,r.sessionId=e.toString("latin1",s,s+8),s+=8,r.messageType===a.CREATE_RESP){const t=e.readUInt8(s);if(s++,d(e,s)<t)return o(`TsSmf parse failed: not enough data for RouterTag, expected ${t}B`),!1;r.routerTag=e.toString("latin1",s,s+t),s+=t}return s=t+l,4294967295===n.payloadLength?r.payloadLength=n.payloadLength:r.payloadLength=n.payloadLength-l,r}const E=p((()=>h(51642369)+h(12))),g=p((()=>E.value+h(22)+l(33290))),T=p((()=>E.value+h(22)+l(32778)+h(0)+h(0))),S=p((()=>h(60030977)+h(12)+h(22)+l(34058))),m=p((()=>h(60030977)+h(12)+h(24)+l(34316))),f={genTsCreateHeader:function(){return T.value},genTsDestroyHeader:function(e){return g.value+e},genTsDataTokenMsg:function(e){return S.value+e},genTsDataStreamTokenMsg:function(e,t){return m.value+e+l(t&&t>0?t:0)},genTsDataMsgHeaderParts:function(e){return[h(60030977)+h(12),l(33802)+e]},parseTsSmfHdrAt:_,parseTsSmfMsgAt:function(e,t,n){const s=_(e,t,n);if(!s)return null;const r=t+s.tsHeaderLength;return d(e,r)<s.payloadLength?(o(`Couldn't read full encapsulated TsSmf payload, expected ${s.payloadLength}B`),null):(s.payload=e.slice(r,r+s.payloadLength),s)}};e.exports.Transport=f},2326:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.FlowOperation=s.new({CONNECT:"FlowOperation_CONNECT",DISCONNECT:"FlowOperation_DISCONNECT",START:"FlowOperation_START",STOP:"FlowOperation_STOP",DISPOSE:"FlowOperation_DESTROY",GET_STATS:"FlowOperation_GET_STATS",RESET_STATS:"FlowOperation_RESET_STATS",GET_PROPERTIES:"FlowOperation_GET_PROPERTIES",GET_DESTINATION:"FlowOperation_GET_DESTINATION"})},2484:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.CapabilityType=s.new({PEER_SOFTWARE_VERSION:0,PEER_SOFTWARE_DATE:1,PEER_PLATFORM:2,PEER_PORT_SPEED:3,PEER_PORT_TYPE:4,MAX_DIRECT_MSG_SIZE:5,PEER_ROUTER_NAME:6,MESSAGE_ELIDING:7,NO_LOCAL:8,GUARANTEED_MESSAGE_CONSUME:9,TEMPORARY_ENDPOINT:10,GUARANTEED_MESSAGE_PUBLISH:11,GUARANTEED_MESSAGE_BROWSE:12,ENDPOINT_MGMT:13,SELECTOR:14,MAX_GUARANTEED_MSG_SIZE:15,ACTIVE_CONSUMER_INDICATION:16,COMPRESSION:17,CUT_THROUGH:18,ENDPOINT_DISCARD_BEHAVIOR:19,ENDPOINT_MESSAGE_TTL:20,JNDI:21,PER_TOPIC_SEQUENCE_NUMBERING:22,QUEUE_SUBSCRIPTIONS:23,SUBSCRIPTION_MANAGER:24,TRANSACTED_SESSION:25,MESSAGE_REPLAY:26,COMPRESSED_SSL:27,SHARED_SUBSCRIPTIONS:28,BR_REPLAY_ERRORID:29,AD_APP_ACK_FAILED:30,VAR_LEN_EXT_PARAM:31}),e.exports.ClientCapabilityType=s.new({UNBIND_ACK:0,BR_ERRORID:1,PQ:2})},2497:(e,t,n)=>{const{Convert:s}=n(9783),{LOG_DEBUG:r}=n(2694),{ParseDestination:i}=n(5686),{ParseFieldHeader:o}=n(8906),{ParseFloat:a}=n(3384),{ParseInteger:c}=n(9148),{ParseMap:u}=n(2076),{ParseStream:l}=n(2580),{SDTDataTypes:h}=n(8605),{SDTField:p}=n(7385),{SDTFieldType:d}=n(7849),{utf8ToUcs2:_}=s,{parseFieldHeader:E}=o,{parseFloatField:g}=a,{parseIntegerField:T}=c,{parseMapAt:S}=u,{parseStreamAt:m}=l,{parseDestination:f}=i,I={parseSingleElement(e,t){const n=E(e,t);if(!n)return null;const s=t+n[3],r=n[2];switch(n[0]){case h.Null:return p.create(d.NULLTYPE,null);case h.Boolean:return p.create(d.BOOL,0!==e.readUInt8(s));case h.Integer:return T(!0,e,s,r);case h.UnsignedInteger:return T(!1,e,s,r);case h.Float:return g(e,s,r);case h.Char:return p.create(d.WCHAR,String.fromCharCode(e.readUInt16BE(s)));case h.ByteArray:return p.create(d.BYTEARRAY,e.slice(s,s+r));case h.String:return p.create(d.STRING,_(e.toString("latin1",s,s+r-1)));case h.Destination:return f(e,s,r);case h.SMFMessage:return p.create(d.SMF_MESSAGE,e.slice(s,s+r));case h.Map:return S(e,s,r);case h.Stream:return m(e,s,r);default:return p.create(d.UNKNOWN,e.toString("latin1",s,s+r))}}},R={stringToBuffer:e=>n(8287).hp.from(e,"latin1")};e.exports.ParseSingleElement=I,e.exports.StringToBuffer=R},2515:e=>{e.exports.makeMap=function(...e){const t={};for(let n=0;n<e.length;n+=2)t[e[n]]=e[n+1];return t}},2558:(e,t,n)=>{const s=n(9631),{APIProperties:r}=n(968),{Check:i}=n(802),{MessageConsumerAcknowledgeMode:o}=n(4590),{Topic:a}=n(9620),c={queueDescriptor:void 0,queueProperties:void 0,connectTimeoutInMsecs:1e4,connectAttempts:3,topicEndpointSubscription:void 0,acknowledgeMode:o.AUTO,requiredSettlementOutcomes:[],transportAcknowledgeTimeoutInMsecs:1e3,transportAcknowledgeThresholdPercentage:60,activeIndicationEnabled:!1,noLocal:!1,windowSize:255,_browser:!1,replayStartLocation:void 0,reconnectAttempts:-1,reconnectIntervalInMsecs:3e3,createIfMissing:!1};e.exports.MessageConsumerProperties=class extends r{constructor(e){super(c,e)}get queueDescriptor(){return i.something(this._queueDescriptor)?this._queueDescriptor:c.queueDescriptor}set queueDescriptor(e){e instanceof s.AbstractQueueDescriptor?this._queueDescriptor=e:this._queueDescriptor=e?e.name?new s.QueueDescriptor(e):new s.AbstractQueueDescriptor(e):e}get queueProperties(){return i.something(this._queueProperties)?this._queueProperties:c.queueProperties}set queueProperties(e){this._queueProperties=e?new s.QueueProperties(e):e}get connectTimeoutInMsecs(){return i.something(this._bindTimeoutInMsecs)?this._bindTimeoutInMsecs:c.connectTimeoutInMsecs}set connectTimeoutInMsecs(e){this._bindTimeoutInMsecs=e}get connectAttempts(){return i.something(this._connectAttempts)?this._connectAttempts:c.connectAttempts}set connectAttempts(e){this._connectAttempts=e}get topicEndpointSubscription(){return this._topicEndpointSubscription}set topicEndpointSubscription(e){this._topicEndpointSubscription="string"==typeof e?a.createFromName(e):e}get acknowledgeMode(){return i.something(this._acknowledgeMode)?this._acknowledgeMode:c.acknowledgeMode}set acknowledgeMode(e){this._acknowledgeMode=e}get requiredSettlementOutcomes(){return i.something(this._requiredSettlementOutcomes)&&i.array(this._requiredSettlementOutcomes)?this._requiredSettlementOutcomes:c.requiredSettlementOutcomes}set requiredSettlementOutcomes(e){this._requiredSettlementOutcomes=e}get acknowledgeTimeoutInMsecs(){return i.something(this._transportAcknowledgeTimeoutInMsecs)?this._transportAcknowledgeTimeoutInMsecs:c.transportAcknowledgeTimeoutInMsecs}set acknowledgeTimeoutInMsecs(e){this._transportAcknowledgeTimeoutInMsecs=e}get acknowledgeThreshold(){return i.something(this._transportAcknowledgeThresholdPercentage)?this._transportAcknowledgeThresholdPercentage:c.transportAcknowledgeThresholdPercentage}set acknowledgeThreshold(e){this._transportAcknowledgeThresholdPercentage=e}get transportAcknowledgeTimeoutInMsecs(){return i.something(this._transportAcknowledgeTimeoutInMsecs)?this._transportAcknowledgeTimeoutInMsecs:c.transportAcknowledgeTimeoutInMsecs}set transportAcknowledgeTimeoutInMsecs(e){this._transportAcknowledgeTimeoutInMsecs=e}get transportAcknowledgeThresholdPercentage(){return i.something(this._transportAcknowledgeThresholdPercentage)?this._transportAcknowledgeThresholdPercentage:c.transportAcknowledgeThresholdPercentage}set transportAcknowledgeThresholdPercentage(e){this._transportAcknowledgeThresholdPercentage=e}get activeIndicationEnabled(){return i.something(this._activeIndicationEnabled)?this._activeIndicationEnabled:c.activeIndicationEnabled}set activeIndicationEnabled(e){this._activeIndicationEnabled=e}get noLocal(){return i.something(this._noLocal)?this._noLocal:c.noLocal}set noLocal(e){this._noLocal=e}get windowSize(){return i.something(this._windowSize)?this._windowSize:c.windowSize}set windowSize(e){this._windowSize=e}get browser(){return i.something(this._browser)?this._browser:c._browser}set browser(e){this._browser=e}get replayStartLocation(){return i.something(this._replayStartLocation)?this._replayStartLocation:c.replayStartLocation}set replayStartLocation(e){this._replayStartLocation=e}get reconnectAttempts(){return i.something(this._reconnectAttempts)?this._reconnectAttempts:c.reconnectAttempts}set reconnectAttempts(e){this._reconnectAttempts=e}get reconnectIntervalInMsecs(){return i.something(this._reconnectIntervalInMsecs)?this._reconnectIntervalInMsecs:c.reconnectIntervalInMsecs}set reconnectIntervalInMsecs(e){this._reconnectIntervalInMsecs=e}get createIfMissing(){return i.something(this._createIfMissing)?this._createIfMissing:c.createIfMissing}set createIfMissing(e){this._createIfMissing=e}}},2577:(e,t,n)=>{const s=n(3450),r=n(5024),{ErrorResponseSubcodeMapper:i,ErrorSubcode:o,OperationError:a}=n(6706),{LogFormatter:c}=n(2694),{Long:u}=n(9783),{MessageIds:l}=n(1246),{MessagePublisherAcknowledgeMode:h}=n(6e3),{MessagePublisherEventName:p}=n(3840),{PrivateFlowEventName:d}=n(8860),{PublisherFSMEvent:_}=n(9728),{PublisherFSMEventNames:E}=n(3865),{PublisherStateNames:g}=n(5403),{State:T,StateMachine:S}=n(7414),{StatType:m}=n(5747),{TransportReturnCode:f}=n(8205);e.exports.PublisherFSM=class extends S{constructor({publisher:e,name:t,sessionInterface:n,properties:l}={}){super({name:t});const h=this,S=()=>{const e=h.getCurrentState();return e?e.getName():"<not running>"};this.logger=new c(((...t)=>[`[session=${n.sessionIdHex}]`,`[message-publisher-fsm=${e.flowIdDec}]`,`[${S()}]`,...t])),this.log=this.logger.wrap(this.log,this);const{LOG_TRACE:f,LOG_DEBUG:I,LOG_INFO:R,LOG_WARN:C}=this.logger;Object.assign(this,{_publisher:e,_acknowledgeMode:l.acknowledgeMode,_acknowledgeTimeoutInMsecs:l.acknowledgeTimeoutInMsecs,_sessionInterface:n,_windowSize:l.windowSize,_stateEvents:[]}),this._guaranteedEnabled=l.enabled,this._sendWindow=l.windowSize,this._resetConnectedInfo(),this._notifiedWindowClosed=!1,this._transportFlowControlled=!0,this.initial((function(){return this.transitionTo(h.PublisherUnbound,(e=>{e.getStateMachine().getName()}))})),h.unhandledEventReaction((function(e){switch(e.getName()){case E.FLOW_UNBOUND:return this._guaranteedEnabled=!1,this._publisher.emit(p.GUARANTEED_MESSAGING_DOWN),this.transitionTo(h.PublisherUnbound,(e=>{e.getStateMachine().getName()}));case E.DISPOSE:case E.TRANSPORT_FULL:case E.CAN_SEND:break;default:e.getName()}return this})),h.PublisherUnbound=new T({name:g.UNBOUND,parentContext:h},{emitDownAndBindWaiting(){e.emit(p.DOWN),e.emit(d.BIND_WAITING)}}).entry((function(){this.emitDownAndBindWaiting(),h._connectRetryCount=l.connectRetryCount})).reaction(E.FLOW_UNBOUND,(function(){return this.internalTransition()})).reaction(E.SESSION_UP,(function(){return this.transitionTo(h.PublisherOpenFlowSent)})),h.PublisherOpenFlowSent=new T({name:g.OPENFLOWSENT,parentContext:h},{emitOpenFlowFailedError(t){e.emit(p.CONNECT_FAILED_ERROR,t)},handleOpenFlowResponse(t){const o=t.smfHeader,a=o.pm_respcode;if(t.msgType!==s.SMFAdProtocolMessageType.OPENPUBFLOW)return h.processEvent(new _({name:E.FLOW_FAILED},{returnCode:a,description:`Unexpected response: ${s.SMFAdProtocolMessageType.describe(t.msgType)}`}));if(null===a)return e.incStat(m.RX_DISCARD_SMF_UNKNOWN_ELEMENT),n.sessionIdHex,null;if(200!==a){const e=o.pm_respstr,t=i.getADErrorSubcode(a,e);return h.processEvent(new _({name:E.FLOW_FAILED},{subcode:t,returnCode:a,description:e}))}const c=t.getLastMsgIdAcked(),l=t.getWindow(),d=t.getFlowId(),g=t.getFlowName(),T=t.getPublisherId();if(h._messageIds,void 0===l)return h.processEvent(new _({name:E.FLOW_FAILED},{description:"Window parameter not found"}));if(l>this._windowSize)return h.processEvent(new _({name:E.FLOW_FAILED},{description:"Invalid window negotiation"}));h._sendWindow=l-h._unackedList.length,h._sendWindow<0&&(h._sendWindow=0),Object.assign(h._publisher,{name:g,flowId:d,publisherId:T}),h._guaranteedEnabled=!0,0===h._connectReason||2===h._connectReason?(h._messageIds.setLastSent(c),h._messageIds,h._connectReason,2===h._connectReason&&e.emit(p.FLOW_NAME_CHANGED,{messages:[...h._unackedList],count:h._unackedList.length}),h._connectReason=1,h._unackedList.forEach((e=>{e.getGuaranteedMessageId(),h._renumber(e),e.getGuaranteedMessageId(),h._messageIds.setLastSent(e.getGuaranteedMessageId())}))):h._unackedList.forEach((t=>{t.setFlowId(d),t.setPublisherId(e.publisherId),t.getGuaranteedMessageId()}));const S=h._sessionInterface.getCapability(r.CapabilityType.MAX_GUARANTEED_MSG_SIZE).getValue();return h._unackedList.forEach((e=>{S<e._memoized_payload.length&&C(`Message size ${e._memoized_payload.length} above broker limit ${S}`)})),h._unackedList.length?(h._handleAck(c,!1,t,!0),h._firstUnackedToSend=h._unackedList[0]):h._messageIds.lastAcked=u.fromValue(c),h._messageIds,h.processEvent(new _({name:E.FLOW_UP}))},handleOpenFlowTimeout:()=>(R("Open publisher connection timeout"),h.processEvent(new _({name:E.BIND_TIMEOUT}))),handleUnknownFlowName(){return R("Flow name unknown, republish required"),h._resetConnectedInfo(!0),this.externalTransitionTo(h.PublisherOpenFlowSent)},sendOpenFlow(){const e=n.getCorrelationTag(),t=s.AdProtocolMessage.getOpenMessagePublisher(h._messageIds.lastAcked,h._messageIds.lastSent,l.windowSize,h._publisher._flowName,e);h._messageIds.lastAcked,h._messageIds.lastSent,l.windowSize,h._publisher._flowName,n.sendControl(t),n.enqueueRequest(e,(()=>this.handleOpenFlowTimeout()),l.connectTimeoutInMsecs,null,(e=>this.handleOpenFlowResponse(e)))}}).entry((function(){try{this.sendOpenFlow()}catch(e){C(`Exception during bind attempt: ${e}`),h.processEvent(new _({name:E.SESSION_DOWN}))}})).reaction(E.FLOW_CLOSE,(function(){return this.transitionTo(h.PublisherCloseFlowSent)})).reaction(E.FLOW_UP,(function(){return this.transitionTo(h.PublisherUp)})).reaction(E.SESSION_DOWN,(function(){return this.transitionTo(h.PublisherUnbound)})).reaction(E.BIND_TIMEOUT,(function(){return h._connectRetryCount>0?(h._connectRetryCount--,this.externalTransitionTo(h.PublisherOpenFlowSent)):(this.emitOpenFlowFailedError({subcode:o.TIMEOUT,description:"Open publisher connection failed due to timeout"}),this.transitionTo(h.PublisherUnbound))})).reaction(E.FLOW_FAILED,(function(e){const{subcode:t,returnCode:n,description:s}=e;return e.subcode===o.UNKNOWN_FLOW_NAME?this.handleUnknownFlowName():(this.emitOpenFlowFailedError({event:e,subcode:t,returnCode:n,description:s}),h._resetConnectedInfo(),this.transitionTo(h.PublisherUnbound))})),h.PublisherCloseFlowSent=new T({name:g.CLOSEFLOWSENT,parentContext:h},{handleCloseFlowResponse(t){const r=t.smfHeader,i=r.pm_respcode;return t.msgType!==s.SMFAdProtocolMessageType.CLOSEPUBFLOW?h.processEvent(new _({name:E.FLOW_FAILED},{returnCode:i,description:`Unexpected response: ${s.SMFAdProtocolMessageType.describe(t.msgType)}`})):null===i?(e.incStat(m.RX_DISCARD_SMF_UNKNOWN_ELEMENT),n.sessionIdHex,null):(200!==i&&h.processEvent(new _({name:E.FLOW_FAILED},{returnCode:i,description:r.pm_respstr})),h.processEvent(new _({name:E.FLOW_UNBOUND})))},handleCloseFlowTimeout:()=>(R("Close publisher connection timeout."),h.processEvent(new _({name:E.UNBIND_TIMEOUT}))),sendCloseFlow(){const e=n.getCorrelationTag(),t=s.AdProtocolMessage.getCloseMessagePublisher(h._publisher.flowId,e);n.sendControl(t),n.enqueueRequest(e,(()=>this.handleCloseFlowTimeout()),l.connectTimeoutInMsecs,null,(e=>this.handleCloseFlowResponse(e)))}}).entry((function(){return this.sendCloseFlow(),this})).reaction(E.ACK,(function(e){return h._handleAckEvent(e),this.internalTransition()})).reaction(E.FLOW_UNBOUND,(function(){return this.transitionTo(h.PublisherUnbound)})).reaction(E.FLOW_FAILED,(function(){this.transitionTo(h.PublisherUnbound)})).reaction(E.UNBIND_TIMEOUT,(function(){return this.transitionTo(h.PublisherCloseFlowSent)})),h.PublisherUp=new T({name:g.UP,parentContext:h},{emitFlowUp(){e.emit(p.UP)}}).initial((function(){return this.transitionTo(h._unackedList.length?h.PublisherRetransmitting:h.PublisherDataXfer)})).entry((function(){return h._scheduleStateEvents(h.PublisherUp,(()=>this.emitFlowUp())),this})).reaction(E.ACK,(function(e){return h._handleAckEvent(e),this.internalTransition()})).reaction(E.ACK_TIMEOUT,(function(){return h._firstUnackedToSend=h._unackedList[0],this.transitionTo(h.PublisherRetransmitting)})).reaction(E.FLOW_CLOSE,(function(){return this.transitionTo(h.PublisherCloseFlowSent)})).reaction(E.SESSION_DOWN,(function(){return this.transitionTo(h.PublisherUnbound)})).reaction(E.TRANSPORT_FULL,(function(){return this.internalTransition()})),h.PublisherDataXfer=new T({name:g.DATA_XFER,parentContext:h.PublisherUp}).entry((()=>{h._transportFlowControlled=!1,h._scheduleStateEvents(h.PublisherDataXfer,(()=>h._maybeEmitCanSend()))})).reaction(E.TRANSPORT_FULL,(function(){return this.transitionTo(h.PublisherFlowControlled)})).exit((()=>{h._transportFlowControlled=!0})),h.PublisherFlowControlled=new T({name:g.FLOW_CONTROLLED,parentContext:h.PublisherUp}).reaction(E.TRANSPORT_FULL,(function(){return R("Attempt to send while flow controlled"),this.internalTransition()})).reaction(E.CAN_SEND,(function(){return this.transitionTo(h.PublisherRetransmitting)})),h.PublisherRetransmitting=new T({name:g.RETRANSMITTING,parentContext:h.PublisherUp},{retransmit(){try{h._resendFromUnacked()}catch(e){e instanceof a&&e.subcode===o.INSUFFICIENT_SPACE?h.processEvent(new _({name:E.TRANSPORT_FULL})):(R(`Publisher resendFromUnacked failed: ${e}`),h.processEvent(new _({name:E.FLOW_FAILED})))}}}).entry((function(){this.retransmit()})).reaction(E.RESEND_COMPLETE,(function(){return this.transitionTo(h.PublisherDataXfer)})).reaction(E.TRANSPORT_FULL,(function(){return h._unackedList.length,this.transitionTo(h.PublisherFlowControlled)}))}isDisconnected(){return!this.getCurrentState()||!!this.getActiveState(g.UNBOUND)}prepareAdMessageAndSend(e){if(!this._guaranteedEnabled)throw new a("Session does not provide Guaranteed Message Publish capability",o.GM_UNAVAILABLE,"close flow received from message-router");if(this._sendWindow<=0)throw this._publisher.incStat(m.TX_WINDOW_CLOSED),this._notifiedWindowClosed=!0,new a("Guaranteed Message Window Closed",o.INSUFFICIENT_SPACE);const t=this._unackedList,{LOG_TRACE:n,LOG_DEBUG:i,LOG_INFO:c,LOG_WARN:u}=this.logger;e._payload_is_memoized&&(e._payload_is_memoized=!1,e._memoized_csumm=void 0,e._memoized_payload=void 0);const l=e.clone(),h=s.Codec.Encode.adaptMessageToSmf_payloadMemoize(l);var p=0;try{p=this._sessionInterface.getCapability(r.CapabilityType.MAX_GUARANTEED_MSG_SIZE).getValue()}catch(e){c("Can't pre-check payload size, broker not connected yet?"),e.stack}if(0<p&&h>p)throw new a(`Encoded payload size (${h}) exceeds broker size limit (MAX_GUARANTEED_MSG_SIZE, ${p})`,o.MESSAGE_TOO_LARGE);--this._sendWindow,this._renumber(l),this._cloneNumbers(l,e),t.push(l);const d=l.getGuaranteedMessageId();if(this._messageIds.setLastSent(d),t.length,this._sendWindow,this._transportFlowControlled)return void 0===this._firstUnackedToSend&&(this._firstUnackedToSend=l),f.OK;let g;try{g=this._sessionInterface.sendToTransport(l),g!==f.OK?g===f.NO_SPACE&&(g=f.OK,this._firstUnackedToSend=l,this.processEvent(new _({name:E.TRANSPORT_FULL}))):l.setRedelivered(!0),this._startAckTimer()}catch(e){throw e instanceof a?(o.describe(e.subcode),e.message,t.pop(),this._messageIds.setLastSent(l.getGuaranteedPreviousMessageId()),++this._sendWindow,e):(e.message,e)}return f.OK}_handleAckEvent(e){this._publisher.incStat(m.TX_ACKS_RXED),this._handleAck(e.ack||e.nack,!!e.nack,e.ctrlMessage)}_handleAck(e,t,n=void 0,s=!1){const{_messageIds:r,_unackedList:i}=this,{LOG_DEBUG:o,LOG_INFO:a}=this.logger;if(r.lastAcked.gte(e))return void(s?this._messageIds:a(`Dropping ack: remote ack for ${e}, local ids ${this._messageIds}`));const c=[];for(;i.length&&e.gte(i[0].getGuaranteedMessageId());)c.push(i.shift());-1===i.indexOf(this._firstUnackedToSend)&&(this._firstUnackedToSend=i[0]),this._sendWindow+=c.length,this._sendWindow,r.lastAcked=e;const u=t?c.pop():null,l=c.length;if(l)if(this._acknowledgeMode===h.PER_MESSAGE){i.length;for(let e=0;e<l;++e)this._publisher.emit(p.ACKNOWLEDGED_MESSAGE,c[e])}else{const e=c[l-1];e.getGuaranteedMessageId(),this._publisher.emit(p.ACKNOWLEDGED_MESSAGE,e)}u&&(u.getGuaranteedMessageId(),this._publisher.emit(p.REJECTED_MESSAGE,u,n)),i.length,i.length?this._resetAckTimer():this._clearAckTimer(),this._maybeEmitCanSend()}_maybeEmitCanSend(){const{LOG_TRACE:e}=this.logger;this._notifiedWindowClosed&&0!==this._sendWindow&&(this._notifiedWindowClosed=!1,this._publisher.emit(p.CAN_SEND))}_resendFromUnacked(){const{LOG_ERROR:e,LOG_INFO:t,LOG_DEBUG:n}=this.logger,s=this._unackedList;let r=s.indexOf(this._firstUnackedToSend);if(-1===r)return this._firstUnackedToSend&&e(`Could not find first Unacked Messages in unacked message list: msgId = ${this._firstUnackedToSend.getGuaranteedMessageId}`),void(0===s.length&&(this._messageIds.toString(),this.processEvent(new _({name:E.RESEND_COMPLETE}))));for(s.length,s.map((e=>e.getGuaranteedMessageId().toString()));r<s.length;){s[r].getPublisherId()!==this._publisher.publisherId&&e(`Resending on invalid publisherId '${s[r].getPublisherId()}'when it should be '${this._publisher.publisherId}'`);const n=this._sessionInterface.sendData(s[r]);if(n===f.NO_SPACE)return this._firstUnackedToSend=s[r],t("Publisher sendMessage blocked due to insufficient space, wait for CAN_SEND"),void this.processEvent(new _({name:E.TRANSPORT_FULL}));if(n!==f.OK)return;s[r].setRedelivered(!0),r++,this._startAckTimer()}this._messageIds.toString(),this.processEvent(new _({name:E.RESEND_COMPLETE}))}_resetConnectedInfo(e=!1){const{LOG_DEBUG:t}=this.logger;this._ackTimer&&this._clearAckTimer(),Object.assign(this,{_messageIds:new l}),Object.assign(this._publisher,{publisherId:void 0,flowId:void 0,flowName:null}),e?this._connectReason=2:(this._unackedList=[],this._connectReason=0)}_clearAckTimer(){const{LOG_TRACE:e}=this.logger;this._ackTimer&&this._ackTimer,this._ackTimer&&(clearTimeout(this._ackTimer),this._ackTimer=null)}_emitStateEvents(){const{LOG_TRACE:e}=this.logger;for(;this._stateEvents.length;){const e=this._stateEvents.shift(),t=e[0],n=e[1];this.getActiveState(t.getName())&&n.apply(t)}}_handleAckTimeout(){const{LOG_TRACE:e}=this.logger;this._ackTimer=null,this._publisher.incStat(m.TX_ACK_TIMEOUT),this.processEvent(new _({name:E.ACK_TIMEOUT}))}_renumber(e){const t=this._messageIds,n=t.next;e.setGuaranteedPreviousMessageId(t.lastSent),e.setGuaranteedMessageId(n);const s=this._publisher;e.setFlowId(s.flowId),e.setPublisherId(s.publisherId)}_cloneNumbers(e,t){t.setGuaranteedPreviousMessageId(e.getGuaranteedPreviousMessageId()),t.setGuaranteedMessageId(e.getGuaranteedMessageId()),t.setFlowId(e.getFlowId()),t.setPublisherId(e.getPublisherId())}_resetAckTimer(){this._clearAckTimer(),this._startAckTimer()}_scheduleStateEvents(e,t){this._stateEvents.push([e,t]),this._setPostEventAction((()=>this._emitStateEvents()),"Emit state events")}_setPostEventAction(e,t="No action"){const{LOG_DEBUG:n,LOG_WARN:s}=this.logger;this._postEventAction&&this._postEventAction.desc===t||(this._postEventAction&&this._postEventAction.desc&&s(`Replacing post event action ${this._postEventAction.desc} with ${t}`),this._postEventAction={action:e||(()=>{}),desc:t},this.setPostEventAction((()=>{this._postEventAction.action(),this._postEventAction=null})))}_startAckTimer(){this._ackTimer||(this._ackTimer=setTimeout((()=>this._handleAckTimeout()),this._acknowledgeTimeoutInMsecs))}}},2580:(e,t,n)=>{const s=n(2497),{ParseFieldHeader:r}=n(8906),{SDTField:i}=n(7385),{SDTFieldType:o}=n(7849),{SDTStreamContainer:a}=n(5711),c={parseStreamAt:function(e,t,n){const c=new a;let u=t;for(;u<t+n;){const t=r.parseFieldHeader(e,u),n=s.ParseSingleElement.parseSingleElement(e,u);u+=t[1],n&&c.addField(n)}return i.create(o.STREAM,c)}};e.exports.ParseStream=c},2584:(e,t,n)=>{const{EventEmitter:s}=n(3385),{LogFormatter:r}=n(2694),{MessageConsumerEventName:i}=n(6934),{QueueBrowserEventName:o}=n(8496);function a(e){return`QueueBrowserEventName.${o.describe(e)}`}e.exports.QueueBrowser=class extends s{constructor(e){super({direct:o.MESSAGE,emits:o.values,formatEventName:a}),this._messageConsumer=e,this.logger=new r(((...e)=>["[queue-browser]",...e])),this._setupEventListers()}_setupEventListers(){this._messageConsumer.on(i.UP,this._onConsumerUp.bind(this)),this._messageConsumer.on(i.CONNECT_FAILED_ERROR,this._onConsumerConnectFailed.bind(this)),this._messageConsumer.on(i.DOWN,this._onConsumerDown.bind(this)),this._messageConsumer.on(i.DOWN_ERROR,this._onConsumerDownError.bind(this)),this._messageConsumer.on(i.MESSAGE,this._onConsumerMessage.bind(this)),this._messageConsumer.on(i.DISPOSED,this._onConsumerDisposed.bind(this)),this._messageConsumer.on(i.GM_DISABLED,this._onConsumerGMDisabled.bind(this))}_onConsumerMessage(e){this.emit(o.MESSAGE,e)}_onConsumerUp(e){this.emit(o.UP,e)}_onConsumerConnectFailed(e){this.emit(o.CONNECT_FAILED_ERROR,e)}_onConsumerDown(e){this.emit(o.DOWN,e)}_onConsumerDownError(e){this.emit(o.DOWN_ERROR,e)}_onConsumerDisposed(e){this.emit(o.DISPOSED,e)}_onConsumerGMDisabled(e){this.emit(o.GM_DISABLED,e)}connect(){const{LOG_DEBUG:e,LOG_ERROR:t}=this.logger;try{this._messageConsumer.connect()}catch(e){throw t(e.toString()),e}}disconnect(){const{LOG_DEBUG:e,LOG_ERROR:t}=this.logger;try{this._messageConsumer.disconnect()}catch(e){throw t(e.toString()),e}}start(){const{LOG_DEBUG:e,LOG_ERROR:t}=this.logger;try{this._messageConsumer.start()}catch(e){throw t(e.toString()),e}}stop(){const{LOG_DEBUG:e,LOG_ERROR:t}=this.logger;try{this._messageConsumer.stop()}catch(e){throw t(e.toString()),e}}removeMessageFromQueue(e){this._messageConsumer.applicationAck(e._guaranteedMsgId),e._acked=!0}}},2652:(e,t,n)=>{const{EventEmitter:s}=n(3385),{StatType:r}=n(1737);e.exports.Stats=class extends s{constructor(e){super(),this._parent=e,this._statsMap=[],r.values.forEach((e=>{this._statsMap[e]=0}))}resetStats(){this.emit("reset"),this._statsMap=this._statsMap.map((()=>0))}incStat(e,t=1){this._statsMap[e]+=t,this._parent&&this._parent.incStat(e,t)}getStat(e){return this._statsMap[e]}}},2680:(e,t,n)=>{const{SolaceError:s}=n(6706);e.exports.TransportError=class extends s{constructor(e,t){super("TransportError",e),this.subcode=t}toString(){return`${super.toString()}, subcode=${this.subcode}`}}},2689:(e,t,n)=>{const{CacheCBInfo:s}=n(6686),{CacheContext:r}=n(2969),{CacheLiveDataAction:i}=n(4253),{CacheRequest:o}=n(8847),{CacheRequestResult:a}=n(185),{CacheReturnCode:c}=n(429),{CacheReturnSubcode:u}=n(7651),{CacheSession:l}=n(6682),{CacheSessionProperties:h}=n(7330);e.exports.CacheCBInfo=s,e.exports.CACHE_REQUEST_PREFIX=r.CACHE_REQUEST_PREFIX,e.exports.CacheLiveDataAction=i,e.exports.CacheRequestResult=a,e.exports.CacheReturnCode=c,e.exports.CacheReturnSubcode=u,e.exports.CacheRequest=o,e.exports.CacheSession=l,e.exports.CacheSessionProperties=h},2694:(e,t,n)=>{const{ConsoleLogImpl:s}=n(9563),{GlobalBinding:r}=n(599),{LogImpl:i}=n(1719),{LogLevel:o}=n(1074),{Parameter:a}=n(802),{SolclientFactory:c}=n(4386),{isEnumMember:u,isFunction:l}=a,{getImpl:h,getLogLevel:p,setImpl:d,setLogLevel:_}=r,E={};function g(e,t){Object.keys(E).forEach((n=>{t[`LOG_${n.toUpperCase()}`]=e[n]}))}Object.assign(E,{trace(...e){const t=h();t&&t.trace&&p()>=o.TRACE&&t.trace.apply(null,["solclientjs: ",...e])},debug(...e){const t=h();t&&t.debug&&p()>=o.DEBUG&&t.debug.apply(null,["solclientjs: ",...e])},info(...e){const t=h();t&&t.info&&p()>=o.INFO&&t.info.apply(null,["solclientjs: ",...e])},warn(...e){const t=h();t&&t.warn&&p()>=o.WARN&&t.warn.apply(null,["solclientjs: ",...e])},error(...e){const t=h();t&&t.error&&p()>=o.ERROR&&t.error.apply(null,["solclientjs: ",...e])},fatal(...e){const t=h();t&&t.fatal&&t.fatal.apply(null,["solclientjs: ",...e])}}),c.getLogLevel=()=>p(),c.setLogLevel=e=>{u("logLevel",e,o),_(e)},c.addInitializer((e=>{_(e.logLevel);const t=e.logger||h()||new s;Object.keys(new i).forEach((e=>l(`logger.${e}`,t[e]))),d(t)})),g(E,e.exports),e.exports.LogImpl=i,e.exports.LogLevel=o,e.exports.Binding=r,e.exports.ConsoleLogImpl=s,r.setImpl(new s),e.exports.LogFormatter=class{constructor(e){this._formatter="function"==typeof e?e:"string"==typeof e?function(...t){return[e,...t]}:e||function(...e){return[...e]};const t=this;Object.keys(E).forEach((e=>{this[e]=function(...n){return E[e].apply(null,t._formatter(...n))}})),g(this,this)}get formatter(){return this._formatter}set formatter(e){this._formatter=e}wrap(e,t){const n=this;return function(...s){return e.apply(t,n._formatter(...s))}}}},2712:(e,t,n)=>{const s=n(199),r=n(3450),{Check:i}=n(802),{Convert:o,Hex:a}=n(9783),{ErrorSubcode:c,OperationError:u}=n(6706),{HTTPConnection:l}=n(6350),{LogFormatter:h}=n(2694),{SMFClient:p}=n(3175),{TransportError:d}=n(2680),{TransportProtocol:_}=n(9072),{TransportReturnCode:E}=n(9944),{TransportSessionEvent:g}=n(7368),{TransportSessionEventCode:T}=n(3427),{TransportSessionState:S}=n(3304),{WebTransportSessionBase:m}=n(1517),{int32ToStr:f,strToByteArray:I,strToHexArray:R}=o,{formatHexString:C}=a,{LOG_TRACE:A,LOG_DEBUG:O,LOG_ERROR:N,LOG_INFO:y}=new h("[http-transport-session]"),P=n(8287).hp;e.exports.HTTPTransportSession=class extends m{constructor(e,t,n,s){if(super(e,t,n,s),this._haveToken=!0,this._confMaxWebPayload=s.maxWebPayload,this._maxPayloadBytes=0,this._destroyTimer=null,this._destroyTimeout=s.connectTimeoutInMsecs,this._createUrl=`http${e.match(/(ws|http)(s?:\/\/.+)/)[2]}`,this._routerUrl=this._createUrl,this._rxChannelClient=null,this._httpSendConn=null,this._httpReceiveConn=null,this._smfDataTokenTSHeader=null,this._routerTag="",this._sid=null,null===s.transportProtocol||void 0===s.transportProtocol)throw new u("transportProtocol is not set",c.PARAMETER_OUT_OF_RANGE);this._transportProtocol=s.transportProtocol,this._useBinaryTransport=!1,this._useStreamingTransport=!1,this._streamingTransportPadding=0,this._useBinaryTransport=s.transportProtocol!==_.HTTP_BASE64,this._useStreamingTransport=s.transportProtocol===_.HTTP_BINARY_STREAMING,this._incomingBuffer="",this._packetReadState=0;const r=navigator.userAgent||"";if((r.match(/trident/i)||r.match(/msie/i))&&(this._streamingTransportPadding=257),null===s.transportContentType||void 0===s.transportContentType)throw new u("transportContentType is not set",c.PARAMETER_OUT_OF_RANGE);this._contentType=s.transportContentType}connectTimerExpiry(){y("HTTP transport connect timeout"),this.destroyCleanup("HTTP transport connect timeout",c.TIMEOUT)}get sessionIdHex(){return this._sid?C(this._sid):""}updateMaxWebPayload(){const e=this._confMaxWebPayload-22;this._maxPayloadBytes=this._useBinaryTransport?e:Math.floor(.75*e)}connect(){return this._state!==S.DOWN?E.INVALID_STATE_FOR_OPERATION:this.connectInternal()}connectInternal(){this._connError=null;try{this._createConn=new l(this._createUrl,!this._useBinaryTransport,!1,((e,t)=>this.handleCreateResponse(e,t)),((e,t)=>this.handleCreateConnFailure(e,t)),this._contentType)}catch(e){return y(`Failed to create connection to router: ${e.message}`),this._connError=e,E.CONNECTION_ERROR}if(i.nothing(this._createConn))return y("Failed to create connection to router"),E.CONNECTION_ERROR;const e=r.Codec.Transport.genTsCreateHeader();this._state===S.WAITING_FOR_CREATE||(this.createConnectTimeout(),this._state=S.WAITING_FOR_CREATE);try{this._createConn.send(e)}catch(e){return y(`Error connecting: ${e.message}`),e.stack,this._state=S.CONNECTION_FAILED,this.cancelConnectTimeout(),this._connError=e instanceof d?e:new d(`Could not create HTTP transport session: ${e.message}`,e.subcode||c.CONNECTION_ERROR),E.CONNECTION_ERROR}return E.OK}destroy(e,t){if(this._state,this._state===S.WAITING_FOR_DESTROY||this._state===S.DOWN)return E.OK;if(this._state===S.CONNECTION_FAILED||this._state===S.WAITING_FOR_CREATE)return y("The connection is in unreliable state, close transport"),this.destroyCleanup(e,t,!0),E.OK;y("Destroy transport session immediately"),this._state=S.WAITING_FOR_DESTROY,null!==this._httpSendConn&&(y("Destroy transport session: abort sendConn"),this._httpSendConn.abort()),null!==this._httpReceiveConn&&(y("Destroy transport session: abort receiveConn"),this._httpReceiveConn.abort()),this._destroyTimer=setTimeout((()=>{this.destroyTimerExpiry()}),this._destroyTimeout),this._httpSendConn=new l(this._routerUrl,!this._useBinaryTransport,!1,((e,t)=>this.handleRxDataToken(e,t)),((e,t)=>this.handleSendFailure(e,t)),this._contentType,!0);const n=r.Codec.Transport.genTsDestroyHeader(this._sid);return R(n),this._httpSendConn.send(n),E.OK}send(e,t=!1){let n=e;if(this._state!==S.SESSION_UP)return E.INVALID_STATE_FOR_OPERATION;if(this._queuedData.length>0||!this._haveToken)return this.enqueueData(n,t);let s=null;if(n.length>this._maxPayloadBytes&&(s=n.substr(this._maxPayloadBytes),n=n.substr(0,this._maxPayloadBytes),!this.allowEnqueue(s.length)))return this.enqueueFailNoSpace();this._haveToken=!1;const r=this._smfDataTSHeaderParts[0].length+4+this._smfDataTSHeaderParts[1].length+n.length;return this._httpSendConn.send(this._smfDataTSHeaderParts[0]+f(r)+this._smfDataTSHeaderParts[1]+n),this._clientstats.bytesWritten+=n.length,s?this.enqueueData(s,null):(this._clientstats.msgWritten++,E.OK)}enqueueData(e,t=!1){const n=e.length;return t||this.allowEnqueue(n)?(this._queuedDataSize+=n,this._queuedData.push(e),E.OK):this.enqueueFailNoSpace()}initPreformattedHeaders(e){this._smfDataTSHeaderParts=r.Codec.Transport.genTsDataMsgHeaderParts(e),this._useStreamingTransport?this._smfDataTokenTSHeader=r.Codec.Transport.genTsDataStreamTokenMsg(e,this._streamingTransportPadding):this._smfDataTokenTSHeader=r.Codec.Transport.genTsDataTokenMsg(e)}flush(e){this._queuedDataSize?this._flushCallback=e:e()}sendQueuedData(){if(0===this._queuedDataSize)return;this._haveToken=!1;const e=this.getQueuedDataToSend(),t=this._smfDataTSHeaderParts[0].length+4+this._smfDataTSHeaderParts[1].length+e.length;if(this._httpSendConn.send(this._smfDataTSHeaderParts[0]+f(t)+this._smfDataTSHeaderParts[1]+e),this._clientstats.bytesWritten+=e.length,this._canSendNeeded&&(this._canSendNeeded=!1,this._eventCB(new g(T.CAN_ACCEPT_DATA,"",null,0,this._sid))),this._flushCallback){const e=this._flushCallback;this._flushCallback=null,e()}}handleCreateResponse(e,t){if(this._state===S.WAITING_FOR_DESTROY||this._state===S.DOWN)return;if(this.updateMaxWebPayload(),e!==E.OK)return y(`Received create response with return code ${E.describe(e)}`),void(e===E.DATA_DECODE_ERROR?this.destroyCleanup("Received data decode error on create session response",c.DATA_DECODE_ERROR):this.destroyCleanup("Failed to handle create session response",c.CONNECTION_ERROR));if(0===t.length)return;const n=r.Codec.Decode.decodeCompoundMessage(P.from(t,"latin1"),0);if(!n)return N("Could not parse create response as SMF. Destroying transport"),void this.destroyCleanup("Failed to parse create response message",c.CONNECTION_ERROR);const s=n.getResponse();if(200!==s.responseCode)return void this.destroyCleanup(`Transport create request failed (${s.responseCode}, ${s.responseString})`,c.CONNECTION_ERROR);this.cancelConnectTimeout(),this._createConn.abort(),this._createConn=null,this._state=S.SESSION_UP,this._sid=n.sessionId,this._routerTag=n.routerTag,this._routerUrl=this._createUrl.replace(/\?.*/,""),""!==this._routerTag&&(this._routerUrl=this._routerUrl+this._routerTag),this.initPreformattedHeaders(this._sid);const i=!this._useBinaryTransport,o=this._useStreamingTransport;this._httpSendConn=new l(this._routerUrl,i,!1,((e,t)=>this.handleRxDataToken(e,t)),((e,t)=>this.handleSendFailure(e,t)),this._contentType),this._useStreamingTransport?this._httpReceiveConn=new l(this._routerUrl,i,o,((e,t)=>this.handleRxStreaming(e,t)),((e,t)=>this.handleSendFailure(e,t)),this._contentType,!0):(this._rxChannelClient=new p((e=>this.handleSmfMessage(e)),(e=>this.handleSmfParseError(e)),null),this._httpReceiveConn=new l(this._routerUrl,i,o,((e,t)=>this.handleRxData(e,t)),((e,t)=>this.handleSendFailure(e,t)),this._contentType)),this._httpReceiveConn.send(this._smfDataTokenTSHeader),this._eventCB(new g(T.UP_NOTICE,s.responseString,s.responseCode,0,n.sessionId))}handleDestroyResponse(e){this.cancelDestroyTimeout();const t=e.getResponse(),n=t?t.responseString:"";this.destroyCleanup(`${n} handled Destroy Response addressed to session ${C(e.sessionId)}, on session ${C(this._sid)}`,0)}handleSmfMessage(e){const t=e.smfHeader;if(t.smf_protocol!==r.SMFProtocol.TSESSION)return void this.handleSmfParseError(`Unexpected Message Prototcol (${t.smf_protocol}) on ReceiveData connection`);const n=e.payload,s=e.payloadLength;switch(e.messageType){case r.SMFTransportSessionMessageType.DESTROY_RESP:return void this.handleDestroyResponse(e);case r.SMFTransportSessionMessageType.DATA:if(e.sessionId!==this._sid){const t=e.getResponse(),n=t?` (${t.responseCode} ${t.responseString})`:"",s=t?t.responseCode:null;return I(this._sid),I(e.sessionId),this._state=S.CONNECTION_FAILED,void this._eventCB(new g(T.PARSE_FAILURE,`Session ID mismatch in data message, expected: ${C(this._sid)}, got: ${C(e.sessionId)}, ${n}`,s,c.PROTOCOL_ERROR,this._sid))}s>0&&this._client.rxDataBuffer(n);break;default:this.handleSmfParseError(`Unexpected message type (${e.messageType}) on ReceiveData connection`)}}handleSmfParseError(){this._eventCB(new g(T.DATA_DECODE_ERROR,"Received data decode error",null,c.DATA_DECODE_ERROR,this._sid))}handleRxData(e,t){null!==this._httpReceiveConn&&null!==this._rxChannelClient?this._state!==S.WAITING_FOR_DESTROY?(this._httpReceiveConn.recStat("GotData"),e===E.OK?0===t.length?this._httpReceiveConn.send(this._smfDataTokenTSHeader):this._rxChannelClient.rxDataString(t):this.handleRxError(e,t)):s.Debug.formatDumpBytes(t.substring(0,64),!0,0):this._state===S.DOWN?y("Transport session is down, ignore data from receive connection"):N(`Transport session is not in working state, state: ${this._state}`)}handleRxStreaming(e,t){if(null===this._httpReceiveConn)return void(this._state===S.DOWN||N(`Transport session is not in working state, state: ${this._state}`));if(this._state===S.WAITING_FOR_DESTROY)return void s.Debug.formatDumpBytes(t.substring(0,64),!0,0);if(this._httpReceiveConn.recStat("GotData"),e!==E.OK)return void this.handleRxError(e,t);if(0===t.length)return this._packetReadState=0,void this._httpReceiveConn.send(this._smfDataTokenTSHeader);if(1===this._packetReadState)return void this._client.rxDataString(t);this._incomingBuffer+=t;const n=r.Codec.ParseSMF.parseSMFAt(P.from(this._incomingBuffer,"latin1"),0,!0);if(n){const e=r.Codec.Transport.parseTsSmfHdrAt(P.from(this._incomingBuffer,"latin1"),n.headerLength,n);if(!e)return;switch(e.messageType){case r.SMFTransportSessionMessageType.DESTROY_RESP:return void this.handleDestroyResponse(e);case r.SMFTransportSessionMessageType.DATA:if(e.sessionId!==this._sid){const n=e.getResponse(),s=n?` (${n.responseCode} ${n.responseString})`:"",r=n?n.responseCode:null;return I(this._sid),I(e.sessionId),I(t.substr(0,64)),this._state=S.CONNECTION_FAILED,void this._eventCB(new g(T.PARSE_FAILURE,`Session ID mismatch in data message, expected: ${C(this._sid)}, got: ${C(e.sessionId)}, ${s}`,r,c.PROTOCOL_ERROR,this._sid))}return this._packetReadState=1,this._incomingBuffer.length>n.headerLength+e.tsHeaderLength&&this._client.rxDataString(this._incomingBuffer.substr(n.headerLength+e.tsHeaderLength)),void(this._incomingBuffer="");default:throw new d(`Unexpected message type (${e.messageType}) on ReceiveData connection`,0)}}else if(r.Codec.ParseSMF.isSMFHeaderAvailable(P.from(this._incomingBuffer,"latin1"),0)&&!r.Codec.ParseSMF.isSMFHeaderValid(P.from(this._incomingBuffer,"latin1"),0)){N(`Couldn't decode message due to invalid smf header, dump first 64 bytes (or fewer) of buffer content:\n${s.Debug.formatDumpBytes(this._incomingBuffer.substring(0,64),!0,0)}`);const e="Error parsing incoming message - invalid SMF header detected";this._state=S.CONNECTION_FAILED,this._eventCB(new g(T.PARSE_FAILURE,e,null,c.PROTOCOL_ERROR,null))}}handleRxDataToken(e,t){if(e!==E.OK)return void this.handleRxError(e,t);if(0===t.length)return;const n=r.Codec.Decode.decodeCompoundMessage(P.from(t,"latin1"),0);if(n)if(n.messageType!==r.SMFTransportSessionMessageType.DESTROY_RESP){if(n.sessionId!==this._sid){const e=n.getResponse(),s=e?` (${e.responseCode} ${e.responseString})`:"",r=e?e.responseCode:null;return I(this._sid),I(n.sessionId),I(t.substr(0,64)),void(this._state!==S.WAITING_FOR_DESTROY?(this._state=S.CONNECTION_FAILED,this._eventCB(new g(T.PARSE_FAILURE,`Session ID mismatch in response message, expected: ${C(this._sid)}, got: ${C(n.sessionId)}, ${s}`,r,c.PROTOCOL_ERROR,this._sid))):this.destroyCleanup("Session ID mismatch in response message",c.PROTOCOL_ERROR))}if(n.messageType!==r.SMFTransportSessionMessageType.DATA_TOKEN&&n.messageType!==r.SMFTransportSessionMessageType.DATA_STREAM_TOKEN)throw new d(`Unexpected message type (${n.messageType}) on SendData connection`,0);this._haveToken=!0,this._httpSendConn.recStat("GotToken"),this.sendQueuedData()}else this.handleDestroyResponse(n);else this._state!==S.WAITING_FOR_DESTROY?(this._state=S.CONNECTION_FAILED,this._eventCB(new g(T.PARSE_FAILURE,"Failed to parse received data message",null,c.PROTOCOL_ERROR,this._sid))):this.destroyCleanup("Failed to parse received data message",c.PROTOCOL_ERROR)}handleRxError(e){y(`handleRxError, transport return code ${E.name(e)}`),this._state=S.CONNECTION_FAILED,e===E.DATA_DECODE_ERROR?this._eventCB(new g(T.DATA_DECODE_ERROR,"Received data decode error",null,c.DATA_DECODE_ERROR,this._sid)):this._eventCB(new g(T.SEND_ERROR,"Connection error",c.CONNECTION_ERROR,this._sid))}handleSendFailure(e,t){this._state===S.WAITING_FOR_DESTROY?(y(`Connection destroy failure (${t}) while in state ${this._state}`),this.destroyCleanup(`Connection destroy failure: ${t}`,c.CONNECTION_ERROR)):(y(`Connection failure (${t}) while in state ${this._state}`),this._eventCB(new g(T.SEND_ERROR,`Connection error: ${t}`,e,c.CONNECTION_ERROR,this._sid)))}handleCreateConnFailure(e,t){this._state!==S.DOWN&&(y(`Connection create failure (${t}) while in state ${this._state}`),this.destroyCleanup(`Connection create failure: ${t}`,c.CONNECTION_ERROR))}destroyTimerExpiry(){this.destroyCleanup("Destroy request timeout",c.CONNECTION_ERROR)}cancelDestroyTimeout(){this._destroyTimer&&(clearTimeout(this._destroyTimer),this._destroyTimer=null)}destroyCleanup(e,t,n){this._createConn&&this._createConn.abort(),this._httpSendConn&&this._httpSendConn.abort(),this._httpReceiveConn&&this._httpReceiveConn.abort(),this._createUrl=null,this._routerUrl=null,this._createConn=null,this._httpSendConn=null,this._httpReceiveConn=null,this._smfDataTokenTSHeader=null,this._rxChannelClient=null,this._routerTag="",this._queuedData=[],this._queuedDataSize=0,this._canSendNeeded=!1,this.cancelDestroyTimeout(),this.cancelConnectTimeout(),this._state=S.DOWN;const s=()=>{this._eventCB&&this._eventCB(new g(T.DESTROYED_NOTICE,e||"Session is destroyed",null,t||0,this._sid)),this._client=null,this._eventCB=null};n?setTimeout(s,0):s()}getInfoStr(){return`HTTPTransportSession; sid=${C(this._sid)}; routerTag=${this._routerTag}`}}},2764:(e,t,n)=>{const{Enum:s}=n(7444),r=[["EMPTY_STRING",/^$/,"Zero length",!0],["LT",/</,"Less than sign (<)",!0],["GT",/>/,"Greater than sign (>)",!0],["ASTERISK",/\*/,"Asterisk (*)",!0],["QUESTION_MARK",/\?/,"Question mark (?)",!0],["AMPERSAND",/&/,"Ampersand (&)",!0],["SEMICOLON",/;/,"Semicolon (;)",!0],["LEADING_SLASH",/^\//,"Starts with a slash (/).",!1],["TRAILING_SLASH",/\/$/,"Ends with a slash (/).",!1],["DOUBLE_SLASH",/\/\//,"Empty level (//)",!1],["TOO_LONG",/.{200,}/,"Longer than 200 characters",!0]],o={EMPTY_STRING:"Zero length",LT:"Less than sign (<)",GT:"Greater than sign (>)",ASTERISK:"Asterisk (*)",QUESTION_MARK:"Question mark (?)",AMPERSAND:"Ampersand (&)",SEMICOLON:"Semicolon (;)",LEADING_SLASH:"Starts with a slash (/).",TRAILING_SLASH:"Ends with a slash (/).",DOUBLE_SLASH:"Empty level (//)",TOO_LONG:"Longer than 200 characters",NO_ISSUE:""};e.exports.EndpointNameComplaint=s.new(o),e.exports.explainInvalidEndpointName=function(e,t){for(i in r){const n=r[i];if((!t||n[3])&&e.match(n[1]))return n[2]}return o.NO_ISSUE}},2843:e=>{function t(e){return e.reduce(((e,n)=>e.concat(Array.isArray(n)?t(n):n)),[])}const n=(()=>{const e={nothing:e=>null==e,anything:e=>!n.nothing(e),undefined:e=>void 0===e,defined:e=>!n.undefined(e),array:e=>n.anything(e)&&Array.isArray(e),object:e=>!n.array(e)&&null!==e&&("object"==typeof e||e instanceof Object),instanceOf:(e,t)=>n.object(e)&&e instanceof t,type:(e,t)=>typeof e===t,instanceOfAny:(e,...s)=>n.array(s)&&t(s).some((t=>n.instanceOf(e,t))),empty:e=>!!n.nothing(e)||!(!n.object(e)||0!==Object.keys(e).length)||0===e.length,truthy:(e,t)=>!!t(e),rangeGe:(e,t)=>e>=t,rangeGt:(e,t)=>e>t,rangeLe:(e,t)=>e<=t,rangeLt:(e,t)=>e<t,rangeCompare(e,t,n,...s){switch(t){case"=":case"==":case"===":return e===n;case"~=":case"=~":{const t=s[0]||1e6;return Math.abs(e-n)<t}case"<":return e<n;case"<=":return e<=n;case">":return e>n;case">=":return e>=n;default:throw new Error(`Illegal operator for rangeCompare: ${t}`)}},NaN:e=>Number.isNaN(e),included(e,t){if(n.nothing(t))return!1;if(t.includes)return t.includes(e);if(Array.isArray(t))return t.indexOf(e)>=0;if(n.object(t)){const s=Object.keys(t);return n.included(e,s)}return!1},equal:(e,t)=>e===t,member:(e,t)=>n.anything(t)&&(n.array(t)?n.included(e,t):Object.keys(t).some((n=>t[n]===e))),boolean:e=>n.type(e,"boolean"),number:e=>n.type(e,"number"),string:e=>n.type(e,"string"),function:e=>n.type(e,"function")};return e.none=e.nothing,e.something=e.anything,Object.keys(e).forEach((t=>{e[t].orNull=function(n,...s){return null===n||e[t](n,...s)},e[t].orUndefined=function(n,...s){return void 0===n||e[t](n,...s)},e[t].orNothing=function(n,...s){return e.nothing(n)||e[t](n,...s)}})),e})();e.exports.BaseChecks=n},2868:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.MessageType=s.new({BINARY:0,MAP:1,STREAM:2,TEXT:3})},2969:e=>{e.exports.CacheContext={CACHE_REQUEST_PREFIX:"#CRQ",cacheRequestCorrelationId:0}},2989:(e,t,n)=>{const{assert:s}=n(7444),{BaseMessage:r}=n(8668),{Destination:i,DestinationUtil:o}=n(9620),{SMFHeader:a}=n(9731),{SMFProtocol:c}=n(5052),{SMFSMPMessageType:u}=n(3647),{SMFSMPMessageTypeFlags:l}=n(1278);class h extends r{constructor(){super(new a(c.SMP,1)),this.msgType=0,this.encodedUtf8Subscription=null,this.encodedUtf8QueueName=null,this.smpFlags=0|l.SMF_SMP_FLAG_TOPIC,this._encodedQueueName=null,this._encodedClientName=null}isFlag(e){return this.smpFlags&e}setFlag(e,t){t?this.smpFlags|=e:this.smpFlags&=~e}static getSubscriptionMessage(e,t,n,r){s(t instanceof i,"Topics are not UCS-2 strings. Pass a Topic object.");const o=new h;return o.msgType=n?u.ADDSUBSCRIPTION:u.REMSUBSCRIPTION,o.encodedUtf8Subscription=t.getBytes(),s(o.encodedUtf8Subscription,"Topic had no encoding"),o.setFlag(l.SMF_SMP_FLAG_TOPIC,!0),r&&o.setFlag(l.SMF_SMP_FLAG_RESPREQUIRED,!0),o._smfHeader.pm_corrtag=e,o}static getQueueSubscriptionMessage(e,t,n,r){s(t instanceof i,"Topics are not UCS-2 strings. Pass a Topic object.");const a=new h;return a.msgType=r?u.ADDQUEUESUBSCRIPTION:u.REMQUEUESUBSCRIPTION,a.encodedUtf8QueueName=o.encodeBytes(n.getName()),a.encodedUtf8Subscription=t.getBytes(),s(a.encodedUtf8Subscription,"Topic had no encoding"),s(a.encodedUtf8QueueName,"Queue had no encoding"),a.setFlag(l.SMF_SMP_FLAG_TOPIC,!0),a.setFlag(l.SMF_SMP_FLAG_RESPREQUIRED,!0),a.setFlag(l.SMF_SMP_FLAG_PERSIST,!0),a._smfHeader.pm_corrtag=e,a}}e.exports.SMPMessage=h},3008:e=>{class t{constructor(e){this.cancel=()=>{this.cancel=()=>{},e()}}static newInterval(e,n,...s){const r=setInterval(n,e,...s);return new t((()=>clearInterval(r)))}static newTimeout(e,n,...s){const r=setTimeout(n,e,...s);return new t((()=>clearTimeout(r)))}}e.exports.Timer=t},3124:(e,t,n)=>{const{ErrorSubcode:s}=n(6706),{TransportError:r}=n(2680),i={create(e=!1){const t="undefined"!=typeof XMLHttpRequest?new XMLHttpRequest:null;if(!e&&!t)throw new r("Failed to create an XMLHTTPRequest",s.CREATE_XHR_FAILED);return t}};e.exports.XHRFactory=i},3153:(e,t,n)=>{const{DestinationType:s}=n(9620),{Enum:r}=n(7444),i={[s.TOPIC]:0,[s.QUEUE]:1,[s.TEMPORARY_QUEUE]:1};e.exports.SDTDestType=r.new(i)},3175:(e,t,n)=>{const{BaseSMFClient:s}=n(427),r=n(5958).a;r.SMF_CLIENTCTRL_LOGIN_FAKE_CORRELATIONTAG=s.SMF_MAX_CORRELATION,e.exports.SMFClient=r},3183:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.SessionState=s.new({CONNECTING:1,CONNECTED:2,SESSION_ERROR:3,DISCONNECTING:4,DISCONNECTED:5})},3188:(e,t,n)=>{const{BaseMessage:s}=n(8668),{SMFHeader:r}=n(9731),{SMFProtocol:i}=n(5052);e.exports.KeepAliveMessage=class extends s{constructor(){super(new r(i.KEEPALIVEV2,2)),this._smfHeader.smf_uh=2}}},3246:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.WebTransportEvent=s.new({CONNECT:"Connect",DESTROY:"Destroy",DOWNGRADE:"Downgrade",DESTROYED_NOTICE:"DestroyedNotice",CONNECT_TIMEOUT:"ConnectTimeout",UP_NOTICE:"UpNotice",SEND_ERROR:"SendError"})},3247:(e,t,n)=>{var s=n(2195);const{ErrorSubcode:r}=n(6706);e.exports.MessageConsumerEvent=class{constructor(e,t,n=void 0,s=0,r=void 0,i=void 0){this._messageConsumerEventName=e,this._infoStr=t,this._responseCode=n,this._errorSubcode=s,this._correlationKey=r,this._reason=i}get messageConsumerEventName(){return this._messageConsumerEventName}get name(){return this._messageConsumerEventName}get infoStr(){return this._infoStr}get responseCode(){return this._responseCode}get errorSubcode(){return this._errorSubcode}get subcode(){return this._errorSubcode}get correlationKey(){return this._correlationKey}get reason(){return this._reason}set reason(e){this._reason=e}get requestEventCode(){}inspect(){return{messageConsumerEventName:this.messageConsumerEventName,infoStr:this.infoStr,responseCode:this.responseCode,errorSubcode:r.describe(this.errorSubcode),correlationKey:this.correlationKey?this.correlationKey.toString():null,reason:this.reason?this.reason:null}}toString(){return s(this)}}},3268:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.SDTValueErrorSubcode=s.new({VALUE_OUTSIDE_SUPPORTED_RANGE:1})},3304:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.TransportSessionState=s.new({DOWN:0,WAITING_FOR_CREATE:1,SESSION_UP:2,WAITING_FOR_DESTROY:4,CONNECTION_FAILED:5})},3384:(e,t,n)=>{const{SDTField:s}=n(7385),{SDTFieldType:r}=n(7849),i={parseFloatField:function(e,t,n){switch(n){case 4:return s.create(r.FLOATTYPE,e.readFloatBE(t));case 8:return s.create(r.DOUBLETYPE,e.readDoubleBE(t));default:return s.create(r.UNKNOWN,e.toString("latin1",t,t+n))}}};e.exports.ParseFloat=i},3385:(e,t,n)=>{const{EventEmitter:s}=n(6018),{Timer:r}=n(3008);e.exports={EventEmitter:s,Timer:r}},3399:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.AuthenticationScheme=s.new({BASIC:"AuthenticationScheme_basic",CLIENT_CERTIFICATE:"AuthenticationScheme_clientCertificate",AUTHENTICATION_SCHEME_BASIC:"AuthenticationScheme_basic",AUTHENTICATION_SCHEME_CLIENT_CERTIFICATE:"AuthenticationScheme_clientCertificate",OAUTH2:"AuthenticationScheme_oauth2"})},3427:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.TransportSessionEventCode=s.new({UP_NOTICE:1,DESTROYED_NOTICE:2,CAN_ACCEPT_DATA:4,DATA_DECODE_ERROR:5,PARSE_FAILURE:6,CONNECT_TIMEOUT:7,SEND_ERROR:8,DOWNGRADE_FAILED:10,DOWNGRADE_SUCCEEDED:11})},3450:(e,t,n)=>{const s=n(7623),{AdProtocolMessage:r,BinaryMetaBlock:i,ClientCtrlMessage:o,KeepAliveMessage:a,SMPMessage:c}=n(8247),{SMFAdProtocolMessageType:u}=n(7250),{SMFAdProtocolParam:l}=n(5099),{SMFClientCtrlMessageType:h}=n(9640),{SMFClientCtrlParam:p}=n(9685),{SMFParameterType:d,SMFExtendedParameterType:_}=n(7750),{SMFProtocol:E}=n(5052),{SMFSMPMessageType:g}=n(3647),{SMFSMPMessageTypeFlags:T}=n(1278),{SMFTransportSessionMessageType:S}=n(1643);e.exports.AdProtocolMessage=r,e.exports.BinaryMetaBlock=i,e.exports.ClientCtrlMessage=o,e.exports.Codec=s,e.exports.KeepAliveMessage=a,e.exports.SMFAdProtocolMessageType=u,e.exports.SMFAdProtocolParam=l,e.exports.SMFClientCtrlMessageType=h,e.exports.SMFClientCtrlParam=p,e.exports.SMFParameterType=d,e.exports.SMFProtocol=E,e.exports.SMFSMPMessageTypeFlags=T,e.exports.SMFSMPMessageType=g,e.exports.SMFTransportSessionMessageType=S,e.exports.SMPMessage=c,e.exports.SMFExtendedParameterType=_},3561:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.ConsumerStateNames=s.new({UNBOUND:"UNBOUND",UNBOUND_AWAIT_SESSION_UP:"UNBOUND_AWAIT_SESSION_UP",UNBOUND_AWAIT_FLOWOPEN:"UNBOUND_AWAIT_FLOWOPEN",UNBOUND_AWAIT_ANY:"UNBOUND_AWAIT_ANY",BIND_SENT:"BIND_SENT",FLOW_UP:"FLOW_UP",FLOW_UP_XFER:"FLOW_UP_XFER",FLOW_UP_XFER_INACTIVE:"FLOW_UP_XFER_INACTIVE",UNBIND_SENT:"UNBIND_SENT",RECONNECTING:"RECONNECTING",RECONNECTING_BIND_SENT:"RECONNECTING_BIND_SENT",RECONNECTING_AWAIT_SESSION_UP:"RECONNECTING_AWAIT_SESSION_UP",RECONNECTING_AWAIT_TIMER:"RECONNECTING_AWAIT_TIMER",CREATE_SENT:"CREATE_SENT"})},3647:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.SMFSMPMessageType=s.new({ADDSUBSCRIPTION:0,REMSUBSCRIPTION:1,ADDQUEUESUBSCRIPTION:2,REMQUEUESUBSCRIPTION:3,ADDSUBSCRIPTIONFORCLIENTNAME:4,REMSUBSCRIPTIONFORCLIENTNAME:5})},3671:(e,t,n)=>{const{LOG_ERROR:s}=n(2694),{State:r}=n(7339),{StateContext:i}=n(1493);e.exports.StateMachine=class extends i{constructor(e){if(e.parentContext)throw new Error(`State machine cannot have parent state: ${e.parentContext}`);super(e),this.impl.ancestorList=[this],this.impl.eventQueue=[],this.impl.finalState=new r({name:"impl.final",parentContext:this}),this.impl.handleUncaughtException=(e,t)=>(s(`Uncaught exception in ${this} while processing ${e}: ${t.stack}`),this.terminate())}process(e){const{impl:t}=this,{eventQueue:n}=t;if(n.push(e),t.processingEvents)return!1;for(t.processingEvents=!0;n.length;)n.shift().apply(this);return t.processingEvents=!1,this._onEventCompletion(),!0}start(){if(this.getCurrentState())throw new Error(`Cannot start ${this.getName()}; it is already started.`);this.process((()=>{const e=this.onInitial();if(void 0===e.destState)throw new Error(`Missing destination state from initial transition for ${this}`);if(e.destState===this)throw new Error(`Destination state for initial transition for ${this} cannot be the FSM.`);const t=e.destState.getAncestorList();if(t[0]!==this)throw new Error(`Invalid destination state (${e.destState}) from initial transition for state machine (${this}); destState ancestor (${t[0]})`);this.impl.currentState=this.processReactionResult(e)}))}isRunning(){return this.impl.processingEvents}processEvent(e){const{impl:t}=this;this.process((()=>{let n;if(this.log(`Processing event ${e}`),t.currentState)try{n=t.currentState.handleEvent(e),t.currentState=t.currentState.processReactionResult(n,e)}catch(s){this.log(`Caught exception ${s}, continuing`),n=t.handleUncaughtException.call(t.currentState,e,s),t.currentState=t.currentState.processReactionResult(n,e)}}))||this.log(`Deferring event ${e}`)}terminateFsm(){const e=this.getCurrentState();if(e){if(this.impl.processingEvents)throw new Error("Cannot terminate state machine while FSM is processing events. To terminate the FSM from within a reaction, return State~terminate() from a reaction.");this.process((()=>{const t=e.terminate();this.impl.currentState=e.processReactionResult(t)}))}}setPostEventAction(e){if(!this.impl.processingEvents)throw new Error("Cannot set post event hook unless FSM is processing events.");if(!e||"function"!=typeof e)throw new Error(`postEventAction must be a function; got (${e})`);this.impl.postEventAction=e.bind(this)}_onEventCompletion(){const e=this.impl.postEventAction;e&&(this.impl.postEventAction=void 0,this.log("Running post event action"),e.apply(this))}getCurrentState(){return this.impl.currentState}getActiveState(e){const t=this.impl.currentState.getAncestorList();for(let n=1;n<t.length;++n)if(t[n].getName()===e)return t[n]}isStateActive(e){return void 0!==this.getActiveState(e)}unhandledEventReaction(e){if("function"!=typeof e)throw new Error(`In ${this}: unhandled event reaction must be a function; got ${e}`);return this.impl.handleUnhandledEvent=e.bind(this),this}uncaughtExceptionReaction(e){if("function"!=typeof e)throw new Error(`In ${this}: Uncaught exception reaction must be a function; got ${e}`);return this.impl.handleUncaughtException=e,this}getFinalState(){return this.impl.finalState}}},3718:(e,t,n)=>{const{LOG_INFO:s,LOG_WARN:r}=n(2694),{Process:i}=n(968);e.exports.TSHState=class{constructor(e,t,n,s){this._ssl=!!e,this._transportProtocol=t,this._exitCallback=n,this._nextState=s,this._unsupportedRuntimeMessage=`${this._transportProtocol} not supported by this runtime: ${i.userAgent}`}getNextState(){return this._nextState}setNextState(e){this._nextState=e}getTransportProtocol(){return this._transportProtocol}getUseSsl(){return this._ssl}validateLegal(){return!0}onEnter(){this.validateLegal()||(this._nextState&&this._exitCallback?this._exitCallback(this._nextState,this._unsupportedRuntimeMessage):r(`${this._unsupportedRuntimeMessage}, no next state.`))}completeDowngrade(e){return this._nextState&&this._exitCallback?(s(`Connect failed (${e}), try next state.`),this._exitCallback(this._nextState,"Connect failed"),!0):(r(`Connect failed (${e}), no next state.`),!1)}toString(){return this._transportProtocol+(this._ssl?" (SSL)":"")}}},3739:(e,t,n)=>{const s=n(199),r=n(8892),{Destination:i}=n(9620),{LOG_ERROR:o}=n(2694),{Long:a}=n(9783),{MessageCacheStatus:c}=n(7366),{MessageDeliveryModeType:u}=n(177),{MessageDumpFlag:l}=n(3901),{MessageUserCosType:h}=n(6676),{SDTMapContainer:p,SDTFieldType:d}=n(769),{StringUtils:_}=n(968),E={fpDestination(e){const t=e.getDestination();return null!==t&&t instanceof i?["Destination",!0,t.toString(),null]:["Destination",!1,"",null]},fpSenderId:e=>["SenderId",void 0!==e.getSenderId()&&null!==e.getSenderId(),e.getSenderId(),null],fpAppmsgType:e=>["AppMessageType",void 0!==e.getApplicationMessageType()&&null!==e.getApplicationMessageType(),e.getApplicationMessageType(),null],fpAppMsgId:e=>["AppMessageID",void 0!==e.getApplicationMessageId()&&null!==e.getApplicationMessageId(),e.getApplicationMessageId(),null],fpSequenceNumber(e){const t=e.getSequenceNumber();return"number"==typeof t?["SequenceNumber",!0,t,null]:["SequenceNumber",!1,"",null]},fpTopicSequenceNumber(e){const t=e.getTopicSequenceNumber();return a.isLong(t)?["TopicSequenceNumber",!0,t.toString(),null]:["TopicSequenceNumber",!1,"",null]},fpCorrelationId:e=>["CorrelationId",void 0!==e.getCorrelationId()&&null!==e.getCorrelationId(),e.getCorrelationId(),null],fpHttpContentType:e=>["HTTP Content Type",void 0!==e.getHttpContentType()&&null!==e.getHttpContentType(),e.getHttpContentType(),null],fpHttpContentEncoding:e=>["HTTP Content Encoding",void 0!==e.getHttpContentEncoding()&&null!==e.getHttpContentEncoding(),e.getHttpContentEncoding(),null],fpSendTimestamp(e){const t=e.getSenderTimestamp();return"number"==typeof t?["SendTimestamp",!0,`${t} (${r.MessageDumpUtil.formatDate(t)})`,null]:["SendTimestamp",!1,"",null]},fpRcvTimestamp(e){const t=e.getReceiverTimestamp();return"number"==typeof t?["RcvTimestamp",!0,`${t} (${r.MessageDumpUtil.formatDate(t)})`,null]:["RcvTimestamp",!1,"",null]},fpClassOfService:e=>"number"==typeof e.getUserCos()?["Class Of Service",!0,h.nameOf(e.getUserCos()),null]:["Class Of Service",!1,"",null],fpDeliveryMode:e=>"number"==typeof e.getDeliveryMode()?["DeliveryMode",!0,u.nameOf(e.getDeliveryMode()),null]:["DeliveryMode",!1,"",null],fpGuaranteedMsgId(e){const t=e.getGuaranteedMessageId();return a.isLong(t)?["Message Id",!0,t.toString(10),null]:["Message Id",!1,"",null]},fpReplicationGroupMessageId(e){const t=e.getReplicationGroupMessageId();return void 0===t?["Replication Group Message Id",!1,"",null]:["Replication Group Message Id",!0,t.toString(),null]},fpTimeToLive(e){const t=e.getTimeToLive();if("number"==typeof t){const e=r.MessageDumpUtil,n=new Date;return["TimeToLive",!0,`${t} (${e.formatDate(n.getTime()+t)})`,null]}return["TimeToLive",!1,"",null]},fpExpiration(e){const t=e.getGMExpiration();return"number"==typeof t?["Expiration",!0,`${t} (${r.MessageDumpUtil.formatDate(t)})`,null]:["Expiration",!1,"",null]},fpMessageDMQEligible:e=>["DMQ Eligible",e.isDMQEligible(),"",null],fpMessageRedelivered:e=>["Message Re-delivered",e.isRedelivered(),"",null],fpMessageDeliveryCount(e){try{return["Message Delivery Count",!0,e.getDeliveryCount(),null]}catch(e){return["Message Delivery Count",!1,"",null]}},fpDiscardIndication:e=>["Discard Indication",e.isDiscardIndication(),"",null],fpAckImmediately:e=>["ACK Immediately",e.isAcknowledgeImmediately(),"",null],fpElidingEligible:e=>["Eliding Eligible",e.isElidingEligible(),"",null],fpReplyMessage:e=>["Reply Message",e.isReplyMessage(),"",null],fpReplyTo(e){const t=e.getReplyTo();return null!==t&&t instanceof i?["ReplyTo",!0,t.toString(),null]:["ReplyTo",!1,"",null]},fpDeliverToOne:e=>["Deliver To One",e.isDeliverToOne(),"",null],fpCacheMessage:e=>["Message from cache",e.getCacheStatus()!==c.LIVE,"",null],fpCacheRequestId(e){const t=e.getCacheRequestId();return"number"==typeof t?["Cache Request Id",!0,t,null]:["Cache Request Id",!1,"",null]},fpUserPropertyMap(e,t){const n=e.getUserPropertyMap();if(null!==n&&n instanceof p){const e=`${n.getKeys().length} entries`;let s=null;if(t===l.MSGDUMP_FULL)try{s=r.MessageDumpUtil.printMap(n,2)}catch(e){o(e.message,e.stack),s="Error"}return["User Property Map",!0,e,s]}return["User Property Map",!1,"",null]},fpCorrelationTag(e){const t=e.getCorrelationKey();return["Correlation Tag Pointer",null!=t,t,null]},fpUserData:e=>_.notEmpty(e.getUserData())?["User Data",!0,`len=${e.getUserData().length}`,s.Debug.formatDumpBytes(e.getUserData(),!0,2)]:["User Data",!1,"",null],fpXmlMetadata(e,t){const n=e.getXmlMetadata();if(_.notEmpty(n)){const e=`len=${n.length}`;let r=null;return t===l.MSGDUMP_FULL&&(r=s.Debug.formatDumpBytes(n,!0,2)),["XML Metadata",!0,e,r]}return["XML Metadata",!1,"",null]},fpTracingCreationContext(e,t){const n=e.getCreationContext(),s=null!=n&&null!=n.getTraceId()&&null!=n.getSpanId();let r=null;return t&&t===l.MSGDUMP_FULL&&(r=null!=n?n.toString():null),["Tracing CreationContext",s,r,null]},fpTracingTransportContext(e,t){const n=e.getTransportContext(),s=null!=n&&null!=n.getTraceId()&&null!=n.getSpanId();let r=null;return t&&t===l.MSGDUMP_FULL&&(r=null!=n?n.toString():null),["Trace Context SMF Parameter",s,r,null]},fpTracingBaggage(e,t){const n=null!=e.getBaggage()&&null!=e.getBaggage().getBaggage();let s=null;if(t&&t===l.MSGDUMP_FULL){const t=e.getBaggage();s=null!=t?t.getBaggage():null}return["Tracing Baggage",n,s,null]},fpSdtStream(e,t){const n=e.getSdtContainer();if(null!==n&&n.getType()===d.STREAM){const e=r.MessageDumpUtil,s=`${e.countItems(n.getValue())} entries`;let i=null;if(t===l.MSGDUMP_FULL)try{i=e.printStream(n.getValue(),2)}catch(e){o(e.message,e.stack),i="Error"}return["SDT Stream",!0,s,i]}return["SDT Stream",!1,"",null]},fpSdtMap(e,t){const n=e.getSdtContainer();if(null!==n&&n.getType()===d.MAP){const e=`${n.getValue().getKeys().length} entries`;let s=null;if(t===l.MSGDUMP_FULL)try{s=r.MessageDumpUtil.printMap(n.getValue(),2)}catch(e){o(e.message,e.stack),s="Error"}return["SDT Map",!0,e,s]}return["SDT Map",!1,"",null]},fpBinaryAttachment(e,t){if(!e._binaryAttachment||e._binaryAttachment.length<1)return["Binary Attachment",!1,"",null];const n=e._binaryAttachment.toString("latin1"),r=`len=${n.length}`;let i=null;return t===l.MSGDUMP_FULL&&(i=s.Debug.formatDumpBytes(n,!0,2)),["Binary Attachment",!0,r,i]},fpXmlContent(e,t){const n=e.getXmlContent();if(_.notEmpty(n)){const e=`len=${n.length}`;let r=null;return t===l.MSGDUMP_FULL&&(r=s.Debug.formatDumpBytes(n,!0,2)),["XML",!0,e,r]}return["XML",!1,"",null]}};e.exports.MessageDumpStandardProvider=E},3840:(e,t,n)=>{const{Enum:s}=n(7444),r=s.new({ACKNOWLEDGED_MESSAGE:"MessagePublisherEventName_acknowledgedMessage",CONNECT_FAILED_ERROR:"MessagePublisherEventName_connectFailedError",CAN_SEND:"MessagePublisherEventName_canSend",DISPOSED:"MessagePublisherEventName_disposed",DOWN:"MessagePublisherEventName_down",FLOW_NAME_CHANGED:"MessagePublisherEventName_flowNameChanged",GUARANTEED_MESSAGING_DOWN:"MessagePublisherEventName_guaranteedMessagingDown",REJECTED_MESSAGE:"MessagePublisherEventName_rejectedMessage",DISCONNECT_FAILED_ERROR:"MessagePublisherEventName_disconnectFailedError",UP:"MessagePublisherEventName_up",TRANSPORT_FULL:"MessagePublisherEventName_transportFull"});e.exports.MessagePublisherEventName=r},3865:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.PublisherFSMEventNames=s.new({SESSION_UP:"PublisherSessionUp",SESSION_UP_NO_AD:"PublisherSessionUpNoAD",SESSION_DOWN:"PublisherSessionDown",FLOW_FAILED:"MessagePublisherFailed",FLOW_UP:"MessagePublisherUp",FLOW_CLOSE:"MessagePublisherClose",FLOW_UNBOUND:"MessagePublisherUnbound",TRANSPORT_FULL:"PublisherTransportFull",ACK:"PublisherAck",ACK_TIMEOUT:"PublisherAckTimeout",BIND_TIMEOUT:"PublisherBindTimeout",UNBIND_TIMEOUT:"PublisherUnbindTimeout",CAN_SEND:"PublisherCanSend",TRANSPORT_ERROR:"PublisherTransportError",RESEND_COMPLETE:"PublisherResendComplete",DISPOSE:"PublisherDispose"})},3884:(e,t,n)=>{const s=n(9620),{Convert:r}=n(9783),{EncodeHeader:i}=n(2224),{EncodeInteger:o}=n(7579),{EncodeMap:a}=n(299),{EncodeStream:c}=n(9549),{IEEE754LIB:u}=n(4493),{SDTDataTypes:l}=n(8605),{SDTDestType:h}=n(3153),{SDTField:p}=n(7385),{SDTFieldType:d}=n(7849),{StringUtils:_}=n(968),{encodeHeader:E}=i,{int48ToStr:g}=o,{encodeMap:T}=a,{encodeStream:S}=c,{nullTerminate:m}=_,f=Math.pow(2,48);function I(e,t){if(!(e instanceof p))return!1;const n=e.getValue();let i=null,o=0;switch(e.getType()){case d.BOOL:o=l.Boolean,i=r.int8ToStr(n?1:0);break;case d.UINT8:o=l.UnsignedInteger,i=r.int8ToStr(n);break;case d.INT8:o=l.Integer,i=r.int8ToStr(n);break;case d.UINT16:o=l.UnsignedInteger,i=r.int16ToStr(n);break;case d.INT16:o=l.Integer,i=r.int16ToStr(n);break;case d.UINT32:o=l.UnsignedInteger,i=r.int32ToStr(n);break;case d.INT32:o=l.Integer,i=r.int32ToStr(n);break;case d.UINT64:o=l.UnsignedInteger,i=String.fromCharCode(0)+String.fromCharCode(0)+g(n);break;case d.INT64:o=l.Integer,i=n>=0?String.fromCharCode(0)+String.fromCharCode(0)+g(n):String.fromCharCode(255)+String.fromCharCode(255)+g(f+n);break;case d.WCHAR:o=l.Char,i=r.int16ToStr(n.charCodeAt(0));break;case d.STRING:o=l.String,i=m(unescape(encodeURIComponent(n)));break;case d.BYTEARRAY:o=l.ByteArray,i=n.toString("latin1");break;case d.FLOATTYPE:o=l.Float,i=u.toIEEE754Single(n);break;case d.DOUBLETYPE:o=l.Float,i=u.toIEEE754Double(n);break;case d.MAP:o=l.Map,i=T(n);break;case d.STREAM:o=l.Stream,i=S(n);break;case d.DESTINATION:o=l.Destination,n instanceof s.Destination&&(i=r.int8ToStr(h[n.getType()])+n.getBytes());break;case d.NULLTYPE:o=l.Null,i="";break;case d.UNKNOWN:i=null}if(null!==i){const e=E(o,i.length);return t.push(e),t.push(i),!0}return!1}const R={encodeSingleElement:function(e){const t=[];return I(e,t),t.join("")},encodeSingleElementToBuf:I};e.exports.EncodeSingleElement=R},3901:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.MessageDumpFlag=s.new({MSGDUMP_BRIEF:0,MSGDUMP_FULL:1})},3922:e=>{class t extends Error{constructor(e,t,n){super(t||""),this.message=t,this.name=e,Error.captureStackTrace?Error.captureStackTrace(this,n):this.stack=(new Error).stack}}e.exports.SolaceError=t},4115:(e,t,n)=>{var s=n(2195);const{Enum:r}=n(7444),i=r.new({BEGINNING:0,DATE:1,RGMID:2});e.exports.ReplayStartLocation=class{constructor(e){Object.assign(this,e),void 0===this._type&&(this._type=i.BEGINNING)}toString(){return s(this)}},e.exports.ReplayStartType=i},4205:(e,t,n)=>{"use strict";var s=n(8287).hp;const r=function(){function e(e,t){return null!=t&&e instanceof t}let t,n,r;try{t=Map}catch(e){t=function(){}}try{n=Set}catch(e){n=function(){}}try{r=Promise}catch(e){r=function(){}}function i(o,c,u,l,h){"object"==typeof c&&(u=c.depth,l=c.prototype,h=c.includeNonEnumerable,c=c.circular);const p=[],d=[],_=void 0!==s;return void 0===c&&(c=!0),void 0===u&&(u=1/0),function o(u,E){if(null===u)return null;if(0===E)return u;let g,T;if("object"!=typeof u)return u;if(e(u,t))g=new t;else if(e(u,n))g=new n;else if(e(u,r))g=new r((function(e,t){u.then((function(t){e(o(t,E-1))}),(function(e){t(o(e,E-1))}))}));else if(i.__isArray(u))g=[];else if(i.__isRegExp(u))g=new RegExp(u.source,a(u)),u.lastIndex&&(g.lastIndex=u.lastIndex);else if(i.__isDate(u))g=new Date(u.getTime());else{if(_&&s.isBuffer(u))return g=s.allocUnsafe?s.allocUnsafe(u.length):new s(u.length),u.copy(g),g;e(u,Error)?g=Object.create(u):void 0===l?(T=Object.getPrototypeOf(u),g=Object.create(T)):(g=Object.create(l),T=l)}if(c){const e=p.indexOf(u);if(-1!=e)return d[e];p.push(u),d.push(g)}if(e(u,t)){let e,t;u.forEach((function(n,s){e=o(s,E-1),t=o(n,E-1),g.set(e,t)}))}if(e(u,n)){let e;u.forEach((function(t){e=o(t,E-1),g.add(e)}))}for(let e in u){let t;T&&(t=Object.getOwnPropertyDescriptor(T,e)),t&&null==t.set||(g[e]=o(u[e],E-1))}if(Object.getOwnPropertySymbols){const e=Object.getOwnPropertySymbols(u);for(let t=0;t<e.length;t++){let n=e[t],s=Object.getOwnPropertyDescriptor(u,n);(!s||s.enumerable||h)&&(g[n]=o(u[n],E-1),s.enumerable||Object.defineProperty(g,n,{enumerable:!1}))}}if(h){const e=Object.getOwnPropertyNames(u);for(let t=0;t<e.length;t++){let n=e[t],s=Object.getOwnPropertyDescriptor(u,n);s&&s.enumerable||(g[n]=o(u[n],E-1),Object.defineProperty(g,n,{enumerable:!1}))}}return g}(o,u)}function o(e){return Object.prototype.toString.call(e)}function a(e){let t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),t}return i.clonePrototype=function(e){if(null===e)return null;let t=function(){};return t.prototype=e,new t},i.__objToStr=o,i.__isDate=function(e){return"object"==typeof e&&"[object Date]"===o(e)},i.__isArray=function(e){return"object"==typeof e&&"[object Array]"===o(e)},i.__isRegExp=function(e){return"object"==typeof e&&"[object RegExp]"===o(e)},i.__getRegExpFlags=a,i}();e.exports=r},4207:(e,t,n)=>{const{TransportProtocol:s}=n(9072),{TSHState:r}=n(3718),{WebTransportCapabilities:i}=n(261);e.exports.StateBinary=class extends r{constructor(e,t,n){super(e,s.HTTP_BINARY,t,n)}validateLegal(){return i.xhrBinary()}}},4253:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.CacheLiveDataAction=s.new({FULFILL:1,QUEUE:2,FLOW_THRU:3})},4255:e=>{e.exports.TimingBucket=class{constructor(e,t){this.name=e,this.interval=t,this.buckets=[]}get bucketCount(){let e=0;for(let t=0,n=this.buckets.length;t<n;++t)e+=this.buckets[t]||0;return e}log(e){if(void 0===e||isNaN(e))return;const t=Math.floor(e/this.interval)*this.interval;this.buckets[t]=this.buckets[t]||0,this.buckets[t]++}toString(){const e=[];return this.buckets.forEach((t=>{e.push(`${t}: ${this.buckets[t]}`)})),`{${e.join(", ")}}`}}},4356:(e,t,n)=>{const{SessionRequestType:s}=n(168);e.exports={SubscriptionUpdateTimeoutMessages:{[s.ADD_SUBSCRIPTION]:"Add subscription request timeout",[s.REMOVE_SUBSCRIPTION]:"Remove subscription request timeout",[s.ADD_P2PINBOX]:"Add P2P inbox subscription timeout",[s.REMOVE_P2PINBOX]:"Remove P2P inbox subscription timeout",[s.REMOVE_DTE_SUBSCRIPTION]:"Remove endpoint topic subscription",default:"Request timeout"}}},4386:(e,t,n)=>{const{FactoryProfile:s,SolclientFactoryProfiles:r}=n(755),{ProfileBinding:i}=n(4807),{SolclientFactory:o}=n(5656),{SolclientFactoryProperties:a}=n(852);e.exports.FactoryProfile=s,e.exports.ProfileBinding=i,e.exports.SolclientFactoryProfiles=r,e.exports.SolclientFactoryProperties=a,e.exports.SolclientFactory=o},4396:(e,t,n)=>{const{Lazy:s}=n(2128),{lazyProperty:r}=s;class i{constructor(e,t){Object.keys(e).forEach((t=>{t.split(",").map((e=>e.trim())).forEach((n=>{const s=e[t],i="string"==typeof s?s.split("."):s,o=(Array.isArray(i)?i:[i]).concat(n);r(this,n,(()=>function(e,t,n){return n.reduce(((e,t)=>"string"==typeof t?e[t]:t),t)}(0,this,o)))}))}))}static resolve(e,t){return new i(e,t)}}e.exports.Resolver=i},4493:e=>{const t={toIEEE754(e,t,n){let s=e;const r=(1<<t-1)-1;let i,o,a;if(isNaN(s))o=(1<<r)-1,a=1,i=0;else if(s===1/0||s===-1/0)o=(1<<r)-1,a=0,i=s<0?1:0;else if(0===s)o=0,a=0,i=1/s==-1/0?1:0;else if(i=s<0,s=Math.abs(s),s>=Math.pow(2,1-r)){const e=Math.min(Math.floor(Math.log(s)/Math.LN2),r);o=e+r,a=s*Math.pow(2,n-e)-Math.pow(2,n)}else o=0,a=s/Math.pow(2,1-r-n);const c=[];for(let e=n;e;e-=1)c.push(a%2?1:0),a=Math.floor(a/2);for(let e=t;e;e-=1)c.push(o%2?1:0),o=Math.floor(o/2);c.push(i?1:0),c.reverse();let u=c.join("");const l=[];for(;u.length;)l.push(parseInt(u.substring(0,8),2)),u=u.substring(8);return l},fromIEEE754(e,t,n){const s=[];for(let t=e.length;t;t-=1){let n=e[t-1];for(let e=8;e;e-=1)s.push(n%2?1:0),n>>=1}s.reverse();const r=s.join(""),i=(1<<t-1)-1,o=parseInt(r.substring(0,1),2)?-1:1,a=parseInt(r.substring(1,1+t),2),c=parseInt(r.substring(1+t),2);return a===(1<<t)-1?0!==c?NaN:o*(1/0):a>0?o*Math.pow(2,a-i)*(1+c/Math.pow(2,n)):0!==c?o*Math.pow(2,-(i-1))*(c/Math.pow(2,n)):0},strToByteArr(e){const t=[];for(let n=0;n<e.length;n++)t.push(255&e.charCodeAt(n));return t},byteArrToStr(e){const t=[];for(let n=0;n<e.length;n++)t.push(String.fromCharCode(255&e[n]));return t.join("")},fromIEEE754Double(e){return this.fromIEEE754(this.strToByteArr(e),11,52)},toIEEE754Double(e){return this.byteArrToStr(this.toIEEE754(e,11,52))},fromIEEE754Single(e){return this.fromIEEE754(this.strToByteArr(e),8,23)},toIEEE754Single(e){return this.byteArrToStr(this.toIEEE754(e,8,23))}};e.exports.IEEE754LIB=t},4494:(e,t,n)=>{const{CacheGetResultCode:s}=n(7909),r={messageID:null,version:0,responseCode:s.INVALID,responseString:"",matchTopic:"",sessionID:null,isSuspect:null,hasMore:null,hasTimestamps:null,replyTo:null,messageStream:null,clusterNameStream:null};e.exports.CacheGetResult=class{constructor(e=r){Object.assign(this,e)}readFromStream(e){this.messageID=e.getNext().getValue(),this.version=e.getNext().getValue(),this.responseCode=e.getNext().getValue(),this.responseString=e.getNext().getValue(),this.matchTopic=e.getNext().getValue(),this.sessionID=e.getNext().getValue(),this.isSuspect=e.getNext().getValue(),this.hasMore=e.getNext().getValue(),this.hasTimestamps=e.getNext().getValue(),e.hasNext()&&(this.messageStream=e.getNext().getValue()),e.hasNext()&&(this.clusterNameStream=this.messageStream,this.messageStream=e.getNext().getValue())}}},4527:(e,t,n)=>{const{BaseMessage:s}=n(8668);e.exports.TransportSMFMessage=class extends s{constructor(){super(null,null),this.uh=0,this.messageType=null,this.sessionId=null,this.routerTag=null,this.payload=null,this.payloadLength=0,this.tsHeaderLength=0}}},4548:(e,t,n)=>{const s=n(9631),{APIProperties:r}=n(968),{Check:i}=n(802),o={queueDescriptor:void 0,connectTimeoutInMsecs:1e4,connectAttempts:3,windowSize:255,transportAcknowledgeTimeoutInMsecs:1e3,transportAcknowledgeThresholdPercentage:60};e.exports.QueueBrowserProperties=class extends r{constructor(e){super(o,e)}get queueDescriptor(){return i.something(this._queueDescriptor)?this._queueDescriptor:o.queueDescriptor}set queueDescriptor(e){this._queueDescriptor=e?new s.QueueDescriptor(e):e}get connectTimeoutInMsecs(){return i.something(this._bindTimeoutInMsecs)?this._bindTimeoutInMsecs:o.connectTimeoutInMsecs}set connectTimeoutInMsecs(e){this._bindTimeoutInMsecs=e}get connectAttempts(){return i.something(this._connectAttempts)?this._connectAttempts:o.connectAttempts}set connectAttempts(e){this._connectAttempts=e}get windowSize(){return i.something(this._windowSize)?this._windowSize:o.windowSize}set windowSize(e){this._windowSize=e}get transportAcknowledgeTimeoutInMsecs(){return i.something(this._transportAcknowledgeTimeoutInMsecs)?this._transportAcknowledgeTimeoutInMsecs:o.transportAcknowledgeTimeoutInMsecs}set transportAcknowledgeTimeoutInMsecs(e){this._transportAcknowledgeTimeoutInMsecs=e}get transportAcknowledgeThresholdPercentage(){return i.something(this._transportAcknowledgeThresholdPercentage)?this._transportAcknowledgeThresholdPercentage:o.transportAcknowledgeThresholdPercentage}set transportAcknowledgeThresholdPercentage(e){this._transportAcknowledgeThresholdPercentage=e}}},4590:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.MessageConsumerAcknowledgeMode=s.new({AUTO:"AUTO",CLIENT:"CLIENT"})},4723:(e,t,n)=>{const{LOG_TRACE:s}=n(2694),{TcpRawTransport:r,CompressedTransport:i,TlsOnlyTransport:o,TcpTlsTransport:a}={},{WebTransport:c}=n(9556),u={createTransport(e,t,n,s,u){const l=e;if(Object.assign(s,{connectTimeoutInMsecs:1e5}),r&&l.trim().startsWith("tcp")){if(!l.trim().startsWith("tcps")){if(0===s.compressionLevel)return new r(l,t,n,s);const e=new i(t,n,s),o=new r(l,e.eventCB.bind(e),e,s);return e.setClientStats(o.getClientStats()),o.setClientStats(null),e.setUnderlyingTransport(o),e}if("PLAIN_TEXT"!==s.sslConnectionDowngradeTo)return new a(l,t,n,s);const e=new o(l,t,n,s),c=new r(l,e.eventCB.bind(e),e,s);return e.setClientStats(c.getClientStats()),c.setClientStats(null),e.setUnderlyingTransport(c),e}return new c(l,t,n,s,u)},startCompression(e){const t=e._eventCB,n=e._client,s=e._props,r=new i(t,n,s);return e.rehome(r.eventCB.bind(r),r),r.setClientStats(e.getClientStats()),e.setClientStats(null),r.setUnderlyingTransport(e),r.connect(),r},severTls(e,t,n){if(t){const t=e=>n(this.startCompression(e));e.tlsShutdown(t)}else e.tlsShutdown(n)}};e.exports.TransportFactory=u},4741:(e,t,n)=>{const s=n(6247),{Base64:r,Bits:i,Convert:o}=n(9783),{BinaryMetaBlock:a,SMFHeader:c,SMPMessage:u}=n(8247),{Check:l}=n(802),{ClientCtrlMessage:h,KeepAliveMessage:p,AdProtocolMessage:d}=n(8247),{Codec:_}=n(769),{ContentSummaryElement:E}=n(4970),{ContentSummaryType:g}=n(8283),{DestinationType:T}=n(9620),{encAdp:S}=n(1318),{encCC:m}=n(1868),{ErrorSubcode:f,OperationError:I}=n(6706),{Lazy:R}=n(7444),{LOG_TRACE:C,LOG_INFO:A}=n(2694),{ParamParse:O}=n(760),{PriorityUserCosMap:N}=n(394),{SDTField:y,SDTFieldType:P,SDTMapContainer:D,SDTStreamContainer:b}=n(769),{SMFParameterType:M,SMFExtendedParameterType:v}=n(7750),{SMFProtocol:w}=n(5052),{SMP:L}=n(9963),{encode:U}=r,{set:F}=i,{int8ToStr:x,int16ToStr:B,int24ToStr:G,int32ToStr:k,int64ToStr:W,uint8ArrayToString:$}=o,{lazyValue:q}=R,{encContentSummary:V,encDeliveryMode:H,encLightSMFParam:Y,encodeSMFParam:Q,encodeSMFExtendedParam:X,FORCED_LENGTH_MODE:K}=O,{encodeSingleElement:j}=_,{encSmp:z}=L,Z=q((()=>(new N).forward));function J(e,t,n,s){if(l.anything(n)&&n.length>0){const r=new E(s,NaN,n.length);e.push(r),t.push(n)}}function ee(e,t,n,s){l.anything(s)&&e.addField(t,y.create(n,s))}function te(e){let t;(e.getCorrelationId()||e.getApplicationMessageId()||e.getApplicationMessageType()||e.getReplyTo()||e.getSenderId()||e.getSequenceNumber()||e.getSenderTimestamp()||e.getUserPropertyMap()||e.isReplyMessage()||e.getCreationContext()||e._compressedCE||e.getHttpContentEncoding()||e.getHttpContentType()||e.getBaggage()&&e.getBaggage().getBaggage()||e.getType()!==s.MessageType.BINARY)&&(t=function(e){let t;const n=new D;ee(n,"ci",P.STRING,e.getCorrelationId()),ee(n,"mi",P.STRING,e.getApplicationMessageId()),ee(n,"mt",P.STRING,e.getApplicationMessageType()),ee(n,"rt",P.DESTINATION,e.getReplyTo()),ee(n,"si",P.STRING,e.getSenderId()),ee(n,"sn",P.INT64,e.getSequenceNumber()),ee(n,"ts",P.INT64,e.getSenderTimestamp()),ee(n,"ex",P.INT64,e.getGMExpiration()),ee(n,"ce",P.STRING,e._compressedCE?e._compressedCE:e.getHttpContentEncoding()),ee(n,"ct",P.STRING,e.getHttpContentType());const r=e.getBaggage();ee(n,"bag",P.STRING,r?r.getBaggage():null);const i=e.getCreationContext();ee(n,"ctx",P.BYTEARRAY,i?i.getEncodedTraceContext():null);const o=new D;e.getUserPropertyMap()&&o.addField("p",y.create(P.MAP,e.getUserPropertyMap())),n.getKeys().length>0&&o.addField("h",y.create(P.MAP,n));let c=0;switch(l.anything(e._getCompressedBinaryAttachment())&&(c|=64),e.getType()){case s.MessageType.BINARY:c|=128;break;case s.MessageType.MAP:c|=10,l.nothing(e._getCompressedBinaryAttachment())&&(t=j(e._structuredContainer));break;case s.MessageType.STREAM:c|=11,l.nothing(e._getCompressedBinaryAttachment())&&(t=j(e._structuredContainer));break;case s.MessageType.TEXT:c|=7,l.nothing(e._getCompressedBinaryAttachment())&&(t=j(e._structuredContainer));break;default:A(`Unhandled messageType: ${e.getType()}`)}const u=e.isReplyMessage()?128:0,h=y.create(P.BYTEARRAY,String.fromCharCode(c,u)),p=new b;p.addField(h),p.addField(y.create(P.MAP,o));const d=new a;return d.type=0,d.payload=j(y.create(P.STREAM,p)),e.binaryMetadataChunk=d,t}(e));const n=[],r=[];J(n,r,e.getXmlMetadata(),g.XML_META),J(n,r,e.getXmlContent(),g.XML_PAYLOAD),t?J(n,r,t,g.BINARY_ATTACHMENT):l.anything(e._getCompressedBinaryAttachment())?J(n,r,e._getCompressedBinaryAttachment().toString("latin1"),g.BINARY_ATTACHMENT):J(n,r,e._binaryAttachment?e._binaryAttachment.toString("latin1"):"",g.BINARY_ATTACHMENT);const i=e.binaryMetadataChunk;if(null!==i){const e=i.asEncodedSmf(),t=16777215;if(e.length>t)throw e.length,new I(`binary-meta data (${e.length}) over the ${t} limit`,f.PARAMETER_OUT_OF_RANGE);J(n,r,e,g.BINARY_METADATA)}return e._memoized_csumm=n,e._memoized_payload=r.join(""),e._payload_is_memoized=!0,e._memoized_payload?e._memoized_payload.length:0}const ne=(e,t)=>(e=>Array.from(Array(Math.pow(2,e))).map(((e,t)=>t)))(t).map((n=>F(0,n,e,t))),se=ne(31,1),re=ne(30,1),ie=ne(29,1),oe=ne(28,1),ae=ne(27,1),ce=ne(24,3),ue=ne(22,2),le=ne(16,6),he=ne(12,4),pe=ne(0,8),de=ne(8,8),_e=ne(0,8);function Ee(e){let t=0;t|=se[e.smf_di?1:0],t|=re[e.smf_elidingEligible?1:0],t|=ie[e.smf_dto?1:0],t|=oe[e.smf_adf?1:0],t|=ae[e.smf_deadMessageQueueEligible?1:0],t|=ce[e.smf_version||0],t|=ue[e.smf_uh||0],t|=le[e.smf_protocol||0],t|=he[e.smf_priority||0],t|=pe[e.smf_ttl||0];const n=[];e.pm_tr_topicname_bytes&&n.push(Q(2,M.TR_TOPICNAME,`${e.pm_tr_topicname_bytes}`)),e.pm_queue_len&&n.push(Y(0,M.LIGHT_QUEUE_NAME_OFFSET,B(de[e.pm_queue_offset]|_e[e.pm_queue_len]))),e.pm_topic_len&&n.push(Y(0,M.LIGHT_TOPIC_NAME_OFFSET,B(de[e.pm_topic_offset]|de[e.pm_topic_len]))),null!==e.pm_corrtag&&void 0!==e.pm_corrtag&&n.push(Y(0,M.LIGHT_CORRELATION,G(e.pm_corrtag))),e.pm_ad_ackimm&&n.push(Y(0,M.LIGHT_ACK_IMMEDIATELY,"")),null!==e.pm_msg_priority&&n.push(Q(0,M.MESSAGEPRIORITY,x(e.pm_msg_priority))),null!==e.pm_userdata&&""!==e.pm_userdata&&n.push(Q(0,M.USERDATA,e.pm_userdata)),e.pm_username&&n.push(Q(0,M.USERNAME,U(e.pm_username))),e.pm_password&&n.push(Q(0,M.PASSWORD,U(e.pm_password))),e.pm_respcode&&n.push(Q(0,M.RESPONSE,k(e.pm_respcode)+e.pm_respstr)),null!==e.pm_deliverymode&&n.push(Q(0,M.DELIVERY_MODE,H(e.pm_deliverymode))),void 0!==e.pm_ad_msgid&&(n.push(Q(2,M.ASSURED_MESSAGE_ID,W(e.pm_ad_msgid))),n.push(Q(2,M.ASSURED_PREVMESSAGE_ID,W(e.pm_ad_prevmsgid)))),e.pm_ad_flowid&&n.push(Q(0,M.ASSURED_FLOWID,k(e.pm_ad_flowid))),e.pm_ad_redelflag&&n.push(Q(0,M.ASSURED_REDELIVERED_FLAG,void 0)),void 0!==e.pm_ad_ttl&&n.push(Q(0,M.AD_TIMETOLIVE,W(e.pm_ad_ttl))),e.pm_ad_publisherid&&n.push(Q(0,M.PUBLISHER_ID,k(e.pm_ad_publisherid))),e.pm_ad_publisherMsgId&&n.push(Q(0,M.PUBLISHER_MSGID,W(e.pm_ad_publisherMsgId))),e.pm_content_summary&&n.push(Q(2,M.MESSAGE_CONTENT_SUMMARY,V(e.pm_content_summary)));let s="",r=0;e.pm_oauth2_access_token&&(s+=X(0,v.OAUTH2_ACCESS_TOKEN,e.pm_oauth2_access_token),r=r||0),e.pm_oidc_id_token&&(s+=X(0,v.OIDC_ID_TOKEN,e.pm_oidc_id_token),r=r||0),e.pm_oauth2_issuer_identifier&&(s+=X(0,v.OAUTH2_ISSUER_IDENTIFIER,e.pm_oauth2_issuer_identifier),r=r||0),e.pm_ts_transport_context&&(s+=X(0,v.TS_TRANSPORT_CONTEXT,$(e.pm_ts_transport_context),K.SIX),r=r||0),s.length>0&&n.push(Q(r,M.EXTENDED_TYPE_STREAM,s));const i=n.join(""),o=12+i.length,a=o+e.payloadLength;return e.setMessageSizes(o,e.payloadLength),k(t)+k(o)+k(a)+i}const ge={encodeCompoundMessage:function(e){let t="";var n,r;e instanceof s.Message?(e.smfHeader||(e.smfHeader=new c(w.TRMSG,255)),n=e,r=e._smfHeader,function(e,t){const n=t;e._payload_is_memoized||te(e);const s=e._memoized_csumm,r=e._memoized_payload;0===s.length||1===s.length&&s[0].type===g.BINARY_ATTACHMENT||(n.pm_content_summary=s),n.payload=r}(n,r),function(e,t){const n=t,r=e.getDeliveryMode();n.smf_dto=e.isDeliverToOne(),n.pm_deliverymode=r,n.smf_adf=r===s.MessageDeliveryModeType.DIRECT?0:1,n.smf_di=e.isDiscardIndication(),n.smf_elidingEligible=e.isElidingEligible(),n.smf_deadMessageQueueEligible=e.isDMQEligible(),n.pm_ad_flowid=e.getFlowId(),n.pm_ad_publisherid=e.getPublisherId(),n.pm_ad_publishermsgId=e.getPublisherMessageId(),n.pm_ad_msgid=e.getGuaranteedMessageId(),n.pm_ad_prevmsgid=e.getGuaranteedPreviousMessageId(),n.pm_ad_ttl=e.getTimeToLive(),n.pm_ad_ackimm=e.isAcknowledgeImmediately(),n.pm_ad_redelflag=e.isRedelivered();const i=e.getTransportContext();null!=i&&(n.pm_ts_transport_context=i.getEncodedTraceContext());const o=e.getDestination();if(o&&(n.pm_tr_topicname_bytes=o.getBytes(),o.type===T.QUEUE||o.type===T.TEMPORARY_QUEUE)){const{offset:e}=o;n.pm_queue_len=n.pm_tr_topicname_bytes.length-e,n.pm_queue_offset=e}n.smf_priority=Z.value.get(e.getUserCos()),void 0!==e.getPriority()&&"number"==typeof e.getPriority()&&e.getPriority()<=255&&e.getPriority()>=0?n.pm_msg_priority=e.getPriority():n.pm_msg_priority=null;const a=e.getUserData();n.pm_userdata=null==a?null:e.getUserData()}(n,r),t=e._smfHeader.payload):e instanceof h?t=m(e):e instanceof u?t=z(e):e instanceof p||e instanceof d&&(t=S(e));const i=e.smfHeader;return i.setPayloadSize(t.length),Ee(i)+t},encodeSMF:Ee,adaptMessageToSmf_payloadMemoize:te};e.exports.Encode=ge},4807:(e,t,n)=>{const{OperationError:s}=n(6706);let r=null;const i={get value(){if(null===r)throw new s("Profile binding not initialized. Call solace.SolclientFactory.init");return r},set value(e){r=e}};e.exports.ProfileBinding=i},4838:(e,t,n)=>{const{LOG_TRACE:s}=n(2694),{parseURL:r}=n(968);let i;i=(e,t)=>setTimeout((()=>{try{const n=e.map((e=>{const t=r(e).host;return{url:e,host:t,address:t,resolved:!1}}));return t(null,n)}catch(e){return t(e)}}),0),e.exports.hostListDNSFilter=i},4970:e=>{e.exports.ContentSummaryElement=class{constructor(e=null,t=0,n=0){this.type=e,this.position=t,this.length=n}}},5011:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.CacheRequestType=s.new({INVALID:0,BULK_MSG:1,REGISTER_REQUEST:2,REGISTER_RESPONSE:3,HEARTBEAT_REQUEST:4,HEARTBEAT_RESPONSE:5,EVENT_NOTIFY:6,EVENT_ACK:7,ACTION_REQUEST:8,ACTION_RESPONSE:9,GET_REQUEST:10,GET_RESPONSE:11,GET_NEXT_REQUEST:12,GET_NEXT_RESPONSE:13,SET_REQUEST:14,SET_RESPONSE:15,GET_MSG_REQUEST:16,GET_MSG_RESPONSE:17,GET_NEXT_MSG_REQUEST:18,GET_NEXT_MSG_RESPONSE:19,UNREGISTER_IND:20,BULK_SET_REQUEST:21,BULK_SET_RESPONSE:22,PURGE_MSG_SEQUENCE_REQUEST:23,PURGE_MSG_SEQUENCE_RESPONSE:24,GET_MSG_SEQUENCE_REQUEST:25,GET_NEXT_MSG_SEQUENCE_REQUEST:26,GET_TOPIC_INFO_REQUEST:27,GET_TOPIC_INFO_RESPONSE:28,READY_MARKER:29,GET_TOPIC_INFO_REQUEST_RANGE:30,SYNC_READY_MARKER:31,VACUUM_REQUEST:32,VACUUM_RESPONSE:33})},5017:(e,t)=>{var n,s=function(e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t=null;try{t=new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,1,13,2,96,0,1,127,96,4,127,127,127,127,1,127,3,7,6,0,1,1,1,1,1,6,6,1,127,1,65,0,11,7,50,6,3,109,117,108,0,1,5,100,105,118,95,115,0,2,5,100,105,118,95,117,0,3,5,114,101,109,95,115,0,4,5,114,101,109,95,117,0,5,8,103,101,116,95,104,105,103,104,0,0,10,191,1,6,4,0,35,0,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,126,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,127,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,128,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,129,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,130,34,4,66,32,135,167,36,0,32,4,167,11])),{}).exports}catch(e){}function n(e,t,n){this.low=0|e,this.high=0|t,this.unsigned=!!n}function s(e){return!0===(e&&e.__isLong__)}function r(e){var t=Math.clz32(e&-e);return e?31-t:t}n.prototype.__isLong__,Object.defineProperty(n.prototype,"__isLong__",{value:!0}),n.isLong=s;var i={},o={};function a(e,t){var n,s,r;return t?(r=0<=(e>>>=0)&&e<256)&&(s=o[e])?s:(n=u(e,0,!0),r&&(o[e]=n),n):(r=-128<=(e|=0)&&e<128)&&(s=i[e])?s:(n=u(e,e<0?-1:0,!1),r&&(i[e]=n),n)}function c(e,t){if(isNaN(e))return t?S:T;if(t){if(e<0)return S;if(e>=_)return C}else{if(e<=-E)return A;if(e+1>=E)return R}return e<0?c(-e,t).neg():u(e%d|0,e/d|0,t)}function u(e,t,s){return new n(e,t,s)}n.fromInt=a,n.fromNumber=c,n.fromBits=u;var l=Math.pow;function h(e,t,n){if(0===e.length)throw Error("empty string");if("number"==typeof t?(n=t,t=!1):t=!!t,"NaN"===e||"Infinity"===e||"+Infinity"===e||"-Infinity"===e)return t?S:T;if((n=n||10)<2||36<n)throw RangeError("radix");var s;if((s=e.indexOf("-"))>0)throw Error("interior hyphen");if(0===s)return h(e.substring(1),t,n).neg();for(var r=c(l(n,8)),i=T,o=0;o<e.length;o+=8){var a=Math.min(8,e.length-o),u=parseInt(e.substring(o,o+a),n);if(a<8){var p=c(l(n,a));i=i.mul(p).add(c(u))}else i=(i=i.mul(r)).add(c(u))}return i.unsigned=t,i}function p(e,t){return"number"==typeof e?c(e,t):"string"==typeof e?h(e,t):u(e.low,e.high,"boolean"==typeof t?t:e.unsigned)}n.fromString=h,n.fromValue=p;var d=4294967296,_=d*d,E=_/2,g=a(1<<24),T=a(0);n.ZERO=T;var S=a(0,!0);n.UZERO=S;var m=a(1);n.ONE=m;var f=a(1,!0);n.UONE=f;var I=a(-1);n.NEG_ONE=I;var R=u(-1,2147483647,!1);n.MAX_VALUE=R;var C=u(-1,-1,!0);n.MAX_UNSIGNED_VALUE=C;var A=u(0,-2147483648,!1);n.MIN_VALUE=A;var O=n.prototype;O.toInt=function(){return this.unsigned?this.low>>>0:this.low},O.toNumber=function(){return this.unsigned?(this.high>>>0)*d+(this.low>>>0):this.high*d+(this.low>>>0)},O.toString=function(e){if((e=e||10)<2||36<e)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative()){if(this.eq(A)){var t=c(e),n=this.div(t),s=n.mul(t).sub(this);return n.toString(e)+s.toInt().toString(e)}return"-"+this.neg().toString(e)}for(var r=c(l(e,6),this.unsigned),i=this,o="";;){var a=i.div(r),u=(i.sub(a.mul(r)).toInt()>>>0).toString(e);if((i=a).isZero())return u+o;for(;u.length<6;)u="0"+u;o=""+u+o}},O.getHighBits=function(){return this.high},O.getHighBitsUnsigned=function(){return this.high>>>0},O.getLowBits=function(){return this.low},O.getLowBitsUnsigned=function(){return this.low>>>0},O.getNumBitsAbs=function(){if(this.isNegative())return this.eq(A)?64:this.neg().getNumBitsAbs();for(var e=0!=this.high?this.high:this.low,t=31;t>0&&!(e&1<<t);t--);return 0!=this.high?t+33:t+1},O.isZero=function(){return 0===this.high&&0===this.low},O.eqz=O.isZero,O.isNegative=function(){return!this.unsigned&&this.high<0},O.isPositive=function(){return this.unsigned||this.high>=0},O.isOdd=function(){return!(1&~this.low)},O.isEven=function(){return!(1&this.low)},O.equals=function(e){return s(e)||(e=p(e)),(this.unsigned===e.unsigned||this.high>>>31!=1||e.high>>>31!=1)&&this.high===e.high&&this.low===e.low},O.eq=O.equals,O.notEquals=function(e){return!this.eq(e)},O.neq=O.notEquals,O.ne=O.notEquals,O.lessThan=function(e){return this.comp(e)<0},O.lt=O.lessThan,O.lessThanOrEqual=function(e){return this.comp(e)<=0},O.lte=O.lessThanOrEqual,O.le=O.lessThanOrEqual,O.greaterThan=function(e){return this.comp(e)>0},O.gt=O.greaterThan,O.greaterThanOrEqual=function(e){return this.comp(e)>=0},O.gte=O.greaterThanOrEqual,O.ge=O.greaterThanOrEqual,O.compare=function(e){if(s(e)||(e=p(e)),this.eq(e))return 0;var t=this.isNegative(),n=e.isNegative();return t&&!n?-1:!t&&n?1:this.unsigned?e.high>>>0>this.high>>>0||e.high===this.high&&e.low>>>0>this.low>>>0?-1:1:this.sub(e).isNegative()?-1:1},O.comp=O.compare,O.negate=function(){return!this.unsigned&&this.eq(A)?A:this.not().add(m)},O.neg=O.negate,O.add=function(e){s(e)||(e=p(e));var t=this.high>>>16,n=65535&this.high,r=this.low>>>16,i=65535&this.low,o=e.high>>>16,a=65535&e.high,c=e.low>>>16,l=0,h=0,d=0,_=0;return d+=(_+=i+(65535&e.low))>>>16,h+=(d+=r+c)>>>16,l+=(h+=n+a)>>>16,l+=t+o,u((d&=65535)<<16|(_&=65535),(l&=65535)<<16|(h&=65535),this.unsigned)},O.subtract=function(e){return s(e)||(e=p(e)),this.add(e.neg())},O.sub=O.subtract,O.multiply=function(e){if(this.isZero())return this;if(s(e)||(e=p(e)),t)return u(t.mul(this.low,this.high,e.low,e.high),t.get_high(),this.unsigned);if(e.isZero())return this.unsigned?S:T;if(this.eq(A))return e.isOdd()?A:T;if(e.eq(A))return this.isOdd()?A:T;if(this.isNegative())return e.isNegative()?this.neg().mul(e.neg()):this.neg().mul(e).neg();if(e.isNegative())return this.mul(e.neg()).neg();if(this.lt(g)&&e.lt(g))return c(this.toNumber()*e.toNumber(),this.unsigned);var n=this.high>>>16,r=65535&this.high,i=this.low>>>16,o=65535&this.low,a=e.high>>>16,l=65535&e.high,h=e.low>>>16,d=65535&e.low,_=0,E=0,m=0,f=0;return m+=(f+=o*d)>>>16,E+=(m+=i*d)>>>16,m&=65535,E+=(m+=o*h)>>>16,_+=(E+=r*d)>>>16,E&=65535,_+=(E+=i*h)>>>16,E&=65535,_+=(E+=o*l)>>>16,_+=n*d+r*h+i*l+o*a,u((m&=65535)<<16|(f&=65535),(_&=65535)<<16|(E&=65535),this.unsigned)},O.mul=O.multiply,O.divide=function(e){if(s(e)||(e=p(e)),e.isZero())throw Error("division by zero");var n,r,i;if(t)return this.unsigned||-2147483648!==this.high||-1!==e.low||-1!==e.high?u((this.unsigned?t.div_u:t.div_s)(this.low,this.high,e.low,e.high),t.get_high(),this.unsigned):this;if(this.isZero())return this.unsigned?S:T;if(this.unsigned){if(e.unsigned||(e=e.toUnsigned()),e.gt(this))return S;if(e.gt(this.shru(1)))return f;i=S}else{if(this.eq(A))return e.eq(m)||e.eq(I)?A:e.eq(A)?m:(n=this.shr(1).div(e).shl(1)).eq(T)?e.isNegative()?m:I:(r=this.sub(e.mul(n)),i=n.add(r.div(e)));if(e.eq(A))return this.unsigned?S:T;if(this.isNegative())return e.isNegative()?this.neg().div(e.neg()):this.neg().div(e).neg();if(e.isNegative())return this.div(e.neg()).neg();i=T}for(r=this;r.gte(e);){n=Math.max(1,Math.floor(r.toNumber()/e.toNumber()));for(var o=Math.ceil(Math.log(n)/Math.LN2),a=o<=48?1:l(2,o-48),h=c(n),d=h.mul(e);d.isNegative()||d.gt(r);)d=(h=c(n-=a,this.unsigned)).mul(e);h.isZero()&&(h=m),i=i.add(h),r=r.sub(d)}return i},O.div=O.divide,O.modulo=function(e){return s(e)||(e=p(e)),t?u((this.unsigned?t.rem_u:t.rem_s)(this.low,this.high,e.low,e.high),t.get_high(),this.unsigned):this.sub(this.div(e).mul(e))},O.mod=O.modulo,O.rem=O.modulo,O.not=function(){return u(~this.low,~this.high,this.unsigned)},O.countLeadingZeros=function(){return this.high?Math.clz32(this.high):Math.clz32(this.low)+32},O.clz=O.countLeadingZeros,O.countTrailingZeros=function(){return this.low?r(this.low):r(this.high)+32},O.ctz=O.countTrailingZeros,O.and=function(e){return s(e)||(e=p(e)),u(this.low&e.low,this.high&e.high,this.unsigned)},O.or=function(e){return s(e)||(e=p(e)),u(this.low|e.low,this.high|e.high,this.unsigned)},O.xor=function(e){return s(e)||(e=p(e)),u(this.low^e.low,this.high^e.high,this.unsigned)},O.shiftLeft=function(e){return s(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?u(this.low<<e,this.high<<e|this.low>>>32-e,this.unsigned):u(0,this.low<<e-32,this.unsigned)},O.shl=O.shiftLeft,O.shiftRight=function(e){return s(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?u(this.low>>>e|this.high<<32-e,this.high>>e,this.unsigned):u(this.high>>e-32,this.high>=0?0:-1,this.unsigned)},O.shr=O.shiftRight,O.shiftRightUnsigned=function(e){return s(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?u(this.low>>>e|this.high<<32-e,this.high>>>e,this.unsigned):u(32===e?this.high:this.high>>>e-32,0,this.unsigned)},O.shru=O.shiftRightUnsigned,O.shr_u=O.shiftRightUnsigned,O.rotateLeft=function(e){var t;return s(e)&&(e=e.toInt()),0==(e&=63)?this:32===e?u(this.high,this.low,this.unsigned):e<32?(t=32-e,u(this.low<<e|this.high>>>t,this.high<<e|this.low>>>t,this.unsigned)):(t=32-(e-=32),u(this.high<<e|this.low>>>t,this.low<<e|this.high>>>t,this.unsigned))},O.rotl=O.rotateLeft,O.rotateRight=function(e){var t;return s(e)&&(e=e.toInt()),0==(e&=63)?this:32===e?u(this.high,this.low,this.unsigned):e<32?(t=32-e,u(this.high<<t|this.low>>>e,this.low<<t|this.high>>>e,this.unsigned)):(t=32-(e-=32),u(this.low<<t|this.high>>>e,this.high<<t|this.low>>>e,this.unsigned))},O.rotr=O.rotateRight,O.toSigned=function(){return this.unsigned?u(this.low,this.high,!1):this},O.toUnsigned=function(){return this.unsigned?this:u(this.low,this.high,!0)},O.toBytes=function(e){return e?this.toBytesLE():this.toBytesBE()},O.toBytesLE=function(){var e=this.high,t=this.low;return[255&t,t>>>8&255,t>>>16&255,t>>>24,255&e,e>>>8&255,e>>>16&255,e>>>24]},O.toBytesBE=function(){var e=this.high,t=this.low;return[e>>>24,e>>>16&255,e>>>8&255,255&e,t>>>24,t>>>16&255,t>>>8&255,255&t]},n.fromBytes=function(e,t,s){return s?n.fromBytesLE(e,t):n.fromBytesBE(e,t)},n.fromBytesLE=function(e,t){return new n(e[0]|e[1]<<8|e[2]<<16|e[3]<<24,e[4]|e[5]<<8|e[6]<<16|e[7]<<24,t)},n.fromBytesBE=function(e,t){return new n(e[4]<<24|e[5]<<16|e[6]<<8|e[7],e[0]<<24|e[1]<<16|e[2]<<8|e[3],t)};var N=n;return e.default=N,"default"in e?e.default:e}({});void 0===(n=function(){return s}.apply(t,[]))||(e.exports=n)},5024:(e,t,n)=>{const{AuthenticationScheme:s}=n(3399),{CapabilityType:r,ClientCapabilityType:i}=n(2484),{MessageRxCBInfo:o}=n(2299),{MutableSessionProperty:a}=n(7113),{Session:c}=n(9564),{SessionEvent:u}=n(8229),{ProvisionEvent:l}=n(918),{SessionEventCBInfo:h}=n(5256),{SessionEventCode:p}=n(6334),{SessionEventName:d}=n(6324),{SessionProperties:_}=n(9656),{SessionState:E}=n(3183),{SolclientFactory:g}=n(4386),{SslDowngrade:T}=n(6415);g.createSession=g.createFactory(((e,t,n)=>new c(e,t,n))),e.exports.AuthenticationScheme=s,e.exports.CapabilityType=r,e.exports.ClientCapabilityType=i,e.exports.MessageRxCBInfo=o,e.exports.MutableSessionProperty=a,e.exports.Session=c,e.exports.SessionEventCBInfo=h,e.exports.SessionEventCode=p,e.exports.SessionEvent=u,e.exports.ProvisionEvent=l,e.exports.SessionEventName=d,e.exports.SessionProperties=_,e.exports.SessionState=E,e.exports.SslDowngrade=T},5027:(e,t,n)=>{const s=n(7339),{FsmObject:r}=n(6031);e.exports.ExitPoint=class extends r{constructor(e){let t;super({name:e.exitPointName}),this.impl.innerState=new s.State({name:`${e.state.getName()} innerExitPoint: ${e.exitPointName}`,parentContext:e.state}).initial((()=>e.state.transitionTo(t))),t=new s.State({name:`${e.state.getName()} outerExitPoint: ${e.exitPointName}`,parentContext:e.state.getParent()}).initial(e.func)}getDestState(){return this.impl.innerState}}},5050:(e,t,n)=>{const{Check:s}=n(802),{ErrorSubcode:r,OperationError:i}=n(6706),o={validateInstance(e,t,n,...s){s.forEach((s=>{s.shift()(e,t,n,...s)}))},valInstance(e,t,n,r,o){if(!s.instanceOf(t[n],r))throw new i(`${e} validation: Property '${n}' must be instance of ${o}`)},valNotEmpty(e,t,n){if(s.none(t[n])||""===t[n])throw new i(`${e} validation: Property '${n}' cannot be empty.`,r.PARAMETER_OUT_OF_RANGE)},valLength(e,t,n,o){if(s.string(t[n])&&t[n].length>o)throw new i(`${e} validation: Property '${n}' exceeded max length ${o}`,r.PARAMETER_OUT_OF_RANGE)},valRange(e,t,n,o,a){if(s.number(t[n])&&(t[n]<o||t[n]>a))throw new i(`${e} validation: Property '${n}' out of range [${o}; ${a}].`,r.PARAMETER_OUT_OF_RANGE)},valString(e,t,n){if(!s.string(t[n]))throw new i(`${e} validation: Property '${n}' must be type string; was ${typeof t[n]}`,r.PARAMETER_INVALID_TYPE)},valNumber(e,t,n){if(!s.number(t[n]))throw new i(`${e} validation: Property '${n}' must be type number; was ${typeof t[n]}`,r.PARAMETER_INVALID_TYPE)},valBoolean(e,t,n){const o=t[n];if(!s.boolean(o))throw new i(`${e} validation: Property '${n}' must be type boolean; was ${typeof o}`,r.PARAMETER_INVALID_TYPE)},valIsMember(e,t,n,s,o,a=!1){const c=t[n];if(!(a&&null==c||s.values.indexOf(c)>=0))throw new i(`${e} validation: Property '${n}'=${c} must be a member of ${o}`,r.PARAMETER_INVALID_TYPE)},valStringOrArray(e,t,n){const s=t[n];if("string"!=typeof s&&!Array.isArray(s))throw new i(`${e} validation: Property '${n}' must be a string or array`,r.PARAMETER_INVALID_TYPE)},valArrayIsMember(e,t,n,s,o,a,c,u){if(null==t[n]){if(a)return;throw new i(`${e} validation: Property '${n}' must be type Array`,r.PARAMETER_INVALID_TYPE)}if(!Array.isArray(t[n]))throw new i(`${e} validation: Property '${n}' must be type Array`,r.PARAMETER_INVALID_TYPE);if(!c&&0===t[n].length)throw new i(`${e} validation: Property '${n}' cannot be empty`,r.PARAMETER_INVALID_TYPE);t[n].forEach(((a,c)=>{if(!s.values.includes(a))throw new i(`${e} validation: Property '${n}' must be an array of ${o}`,r.PARAMETER_INVALID_TYPE);if(!u&&t[n].indexOf(a,c+1)>=0)throw new i(`${e} validation: Property '${n}' cannot have duplicate element value`,r.PARAMETER_OUT_OF_RANGE)}))},valArrayOfString(e,t,n){const o=t[n];if(s.something(o)){if(!Array.isArray(o))throw new i(`${e} validation: Property '${n}' must be type Array`,r.PARAMETER_INVALID_TYPE);o.forEach((t=>{if("string"!=typeof t)throw new i(`${e} validation: Property '${n}' must be an array of string`,r.PARAMETER_INVALID_TYPE)}))}},valTopicString(t,s,o){const{DestinationUtil:a,DestinationType:c}=n(9620);e.exports.APIPropertiesValidators.valString(t,s,o);const u=s[o],l=a.validateAndEncode(c.TOPIC,u);if(l.error)throw new i(`${t} validation: Property '${o}' must be a valid topic string: ${l.error}`,r.PARAMETER_OUT_OF_RANGE)},valTopicStringOrEmpty(t,n,s){const r=n[s];r&&r.length&&e.exports.APIPropertiesValidators.valTopicString(t,n,s)}};e.exports.APIPropertiesValidators=o},5052:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.SMFProtocol=s.new({CSPF:1,CSMP:2,PUBMSG:3,XMLLINK:4,WSE:5,SEMP:6,SUBCTRL:7,PUBCTRL:8,ADCTRL:9,KEEPALIVE:10,KEEPALIVEV2:11,CLIENTCTRL:12,TRMSG:13,JNDI:14,SMP:15,SMRP:16,SMF_IN_SMF:17,SMF_IN_RV:18,ADCTRL_PASSTHROUGH:19,TSESSION:20})},5099:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.SMFAdProtocolParam=s.new({LASTMSGIDSENT:1,LASTMSGIDACKED:2,WINDOW:3,TRANSPORT_PRIORITY:4,APPLICATION_ACK:5,FLOWID:6,QUEUENAME:7,DTENAME:8,TOPICNAME:9,FLOWNAME:10,EP_DURABLE:11,ACCESSTYPE:12,SELECTOR:13,TRANSPORT_WINDOW:14,LINGER_OPTION:15,LASTMSGIDRECEIVED:16,EP_ALLOTHER_PERMISSION:17,FLOWTYPE:18,EP_QUOTA:19,EP_MAX_MSGSIZE:20,GRANTED_PERMISSIONS:21,EP_RESPECTS_TTL:22,TRANSACTION_CTRL_MESSAGE_TYPE:23,TRANSACTED_SESSION_ID:24,TRANSACTED_SESSION_NAME:25,TRANSACTION_ID:26,TRANSACTED_SESSION_STATE:27,TRANSACTION_FLOW_DESCRIPTOR_PUB_NOTIFY:28,TRANSACTION_FLOW_DESCRIPTOR_PUB_ACK:29,TRANSACTION_FLOW_DESCRIPTOR_SUB_ACK:30,NOLOCAL:31,ACTIVE_FLOW_INDICATION:32,WANT_FLOW_CHANGE_NOTIFY:33,EP_BEHAVIOUR:34,PUBLISHER_ID:35,APPLICATION_PUB_ACK:36,NUM_MESSAGES_SPOOLED:37,CUT_THROUGH:38,PUBLISHER_FLAGS:39,APP_MSG_ID_TYPE:40,QUEUE_ENDPOINT_HASH:41,MAX_REDELIVERY:42,PAYLOAD:43,ENDPOINT_ID:44,ACK_SEQUENCE_NUMBER:45,ACK_RECONCILE_REQUEST:46,START_OF_ACK_RECONCILE:47,TIMESTAMP:48,MAX_DELIVERED_UNACKED_MESSAGES_PER_FLOW:49,REPLAY_START_LOCATION:51,ENDPOINT_ERROR_ID:52,SPOOLER_UNIQUE_ID:54,PARTITION_GROUP_ID:56})},5113:(e,t,n)=>{const{TransportProtocol:s}=n(9072),{TSHState:r}=n(3718);e.exports.StateBase64=class extends r{constructor(e,t,n){super(e,s.HTTP_BASE64,t,n)}}},5129:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.ErrorSubcode=s.new({UNKNOWN_ERROR:4294967295,NO_ERROR:0,SESSION_NOT_CONNECTED:2,INVALID_SESSION_OPERATION:3,INVALID_OPERATION:3,TIMEOUT:4,MESSAGE_VPN_NOT_ALLOWED:5,MESSAGE_VPN_UNAVAILABLE:6,CLIENT_USERNAME_IS_SHUTDOWN:7,DYNAMIC_CLIENTS_NOT_ALLOWED:8,CLIENT_NAME_ALREADY_IN_USE:9,CLIENT_NAME_INVALID:10,CLIENT_DELETE_IN_PROGRESS:11,TOO_MANY_CLIENTS:12,LOGIN_FAILURE:13,INVALID_VIRTUAL_ADDRESS:14,CLIENT_ACL_DENIED:15,SUBSCRIPTION_ACL_DENIED:16,PUBLISH_ACL_DENIED:17,PARAMETER_OUT_OF_RANGE:18,PARAMETER_CONFLICT:19,PARAMETER_INVALID_TYPE:20,INTERNAL_ERROR:21,INSUFFICIENT_SPACE:22,OUT_OF_RESOURCES:23,PROTOCOL_ERROR:24,COMMUNICATION_ERROR:25,KEEP_ALIVE_FAILURE:26,TOPIC_MISSING:28,INVALID_TOPIC_SYNTAX:31,MESSAGE_TOO_LARGE:32,XML_PARSE_ERROR:33,SUBSCRIPTION_ALREADY_PRESENT:34,SUBSCRIPTION_NOT_FOUND:35,SUBSCRIPTION_INVALID:36,SUBSCRIPTION_ERROR_OTHER:37,SUBSCRIPTION_TOO_MANY:38,SUBSCRIPTION_ATTRIBUTES_CONFLICT:39,NO_LOCAL_NOT_SUPPORTED:40,DATA_ERROR_OTHER:42,CREATE_XHR_FAILED:43,CONNECTION_ERROR:44,DATA_DECODE_ERROR:45,INACTIVITY_TIMEOUT:46,UNKNOWN_TRANSPORT_SESSION_ID:47,AD_MESSAGING_NOT_SUPPORTED:48,CREATE_WEBSOCKET_FAILED:49,REPLICATION_IS_STANDBY:50,BASIC_AUTHENTICATION_IS_SHUTDOWN:51,CLIENT_CERTIFICATE_AUTHENTICATION_IS_SHUTDOWN:52,GM_UNAVAILABLE:100,UNKNOWN_FLOW_NAME:111,ALREADY_BOUND:112,INVALID_TOPIC_NAME_FOR_TOPIC_ENDPOINT:113,UNKNOWN_QUEUE_NAME:114,UNKNOWN_TOPIC_ENDPOINT_NAME:115,MAX_CLIENTS_FOR_QUEUE:116,MAX_CLIENTS_FOR_TE:117,UNEXPECTED_UNBIND:118,QUEUE_NOT_FOUND:119,SPOOL_OVER_QUOTA:120,QUEUE_SHUTDOWN:121,TOPIC_ENDPOINT_SHUTDOWN:122,NO_MORE_NON_DURABLE_QUEUE_OR_TOPIC_ENDPOINT:123,ENDPOINT_ALREADY_EXISTS:124,PERMISSION_NOT_ALLOWED:125,INVALID_SELECTOR:126,MAX_MESSAGE_USAGE_EXCEEDED:127,ENDPOINT_PROPERTY_MISMATCH:128,NO_SUBSCRIPTION_MATCH:129,MESSAGE_DELIVERY_MODE_MISMATCH:130,MESSAGE_ALREADY_ACKNOWLEDGED:131,SUBSCRIPTION_DOES_NOT_MATCH:133,SELECTOR_DOES_NOT_MATCH:134,INVALID_DTE_NAME:135,UNSUBSCRIBE_NOT_ALLOWED_CLIENTS_BOUND:136,CALLBACK_ERROR:137,NOLOCAL_DISCARD:138,GM_NOT_READY:140,LOW_PRIORITY_MSG_CONGESTION:141,QUOTA_OUT_OF_RANGE:142,FAILED_LOADING_TRUSTSTORE:143,FAILED_LOADING_CERTIFICATE_AND_KEY:144,UNRESOLVED_HOSTS:145,REPLAY_NOT_SUPPORTED:146,REPLAY_DISABLED:147,CLIENT_INITIATED_REPLAY_NON_EXCLUSIVE_NOT_ALLOWED:148,CLIENT_INITIATED_REPLAY_INACTIVE_FLOW_NOT_ALLOWED:149,CLIENT_INITIATED_REPLAY_BROWSER_FLOW_NOT_ALLOWED:150,REPLAY_TEMPORARY_NOT_SUPPORTED:151,UNKNOWN_START_LOCATION_TYPE:152,REPLAY_CANCELLED:153,REPLAY_MESSAGE_UNAVAILABLE:154,REPLAY_START_TIME_NOT_AVAILABLE:155,REPLAY_MESSAGE_REJECTED:156,REPLAY_LOG_MODIFIED:157,MISMATCHED_ENDPOINT_ERROR_ID:158,OUT_OF_REPLAY_RESOURCES:159,TOPIC_OR_SELECTOR_MODIFIED_ON_DURABLE_TOPIC_ENDPOINT:160,REPLAY_FAILED:161,REPLAY_STARTED:162,COMPRESSED_TLS_NOT_SUPPORTED:163,SHARED_SUBSCRIPTIONS_NOT_SUPPORTED:164,SHARED_SUBSCRIPTIONS_NOT_ALLOWED:165,SHARED_SUBSCRIPTIONS_ENDPOINT_NOT_ALLOWED:166,REPLAY_START_MESSAGE_NOT_AVAILABLE:167,MESSAGE_ID_NOT_COMPARABLE:168,REPLAY_ANONYMOUS_QUEUE_NOT_SUPPORTED:169,PARTITIONED_QUEUE_BROWSING_NOT_SUPPORTED:170,PARTITIONED_QUEUE_SELECTORS_NOT_SUPPORTED:171,REPLAY_ANONYMOUS_QUEUE_WITHOUT_SUBSCRIPTIONS_NOT_SUPPORTED:172,SYNC_REPLICATION_INELIGIBLE:173,INVALID_QUEUE_OR_TOPIC_ENDPOINT_DURABILITY:174,INVALID_QUEUE_NAME:175,INVALID_TE_NAME:176,MESSAGE_SIZE_OUT_OF_RANGE:177,UNSUPPORTED_PERMISSIONS:178,MAX_ENDPOINTS_EXCEEDED:179,ASSURED_DELIVERY_NOT_READY:180,COPY_FROM_QUEUE_TEMPLATE_MISSING:181,COPY_FROM_TE_TEMPLATE_MISSING:182})},5136:(e,t,n)=>{var s=n(2195);const{DestinationType:r}=n(8805),{DestinationUtil:i}=n(617);class o{constructor(e,t=r.TOPIC){if("object"==typeof e)this._name=e.name,this._type=e.type,this._bytes=e.bytes,this._offset=e.offset,e.isValidated?(this._isValidated=!0,this._isWildcarded=e.isWildcarded,this._subscriptionInfo=e.subscriptionInfo||{}):(this._isValidated=!1,this._subscriptionInfo={});else{this._name=e,this._type=t;const n=i.encode(t,e);this._bytes=n.bytes,this._offset=n.offset,this._isValidated=!1,this._subscriptionInfo={}}}getName(){return this._name}get name(){return this.getName()}getType(){return this._type}get type(){return this.getType()}getBytes(){return this._bytes}get bytes(){return this.getBytes()}getOffset(){return this._offset}get offset(){return this.getOffset()}validate(){if(this._isValidated){if(this._error)throw this._error;return}const{error:e,isWildcarded:t}=i.legacyValidate(this.type,this.bytes,this.name);if(this._isValidated=!0,e)throw this._error=e,e;this._isWildcarded=t}isWildcarded(){return this.validate(),this._isWildcarded}getSubscriptionInfo(){return this._subscriptionInfo||{}}toString(){return s(this)}equals(e){return e instanceof o&&this.toString().valueOf()===e.toString().valueOf()}}e.exports.Destination=o},5181:e=>{e.exports.WebSocketCloseCodes={0:{name:"Unknown code",description:"No status code was returned by the operation"},1e3:{name:"Normal Closure",description:"The connection closed normally"},1001:{name:"Going Away",description:"The endpoint is going away due to a server failure or client navigation"},1002:{name:"Protocol Error",description:"A WebSocket protocol error occurred"},1003:{name:"Unsupported Data",description:"The endpoint cannot handle the specified data type"},1004:{name:"Reserved",description:""},1005:{name:"No Status Recvd",description:"Expected a status code but none was provided"},1006:{name:"Abnormal Closure",description:"No close frame was received before remote hangup"},1007:{name:"Invalid Frame Payload Data",description:"A message contained data inconsistent with its encoding"},1008:{name:"Policy Violation",description:"A message violated endpoint policy"},1009:{name:"Message Too Big",description:"A data frame was too large"},1010:{name:"Missing Extension",description:"The endpoint did not negotiate an expected extension"},1011:{name:"Internal Error",description:"The server encountered an unexpected condition that prevented it from fulfilling the request"},1012:{name:"Service Restart",description:"The server is restarting"},1013:{name:"Try Again Later",description:"The server is terminating the connection due to a temporary condition"},1014:{name:"Bad Gateway",description:"A gateway or proxy received an invalid response from the upstream server"},1015:{name:"TLS Handshake",description:"The connection was closed due to a failure to perform a TLS handshake"}}},5192:(e,t,n)=>{const{SolaceError:s}=n(3922);class r extends s{constructor(e,t,n){super("OperationError",e,r),this.subcode=t,this.reason=n}}e.exports.OperationError=r},5208:(e,t,n)=>{const s=n(9556),{Lazy:r}=n(7444),{LOG_TRACE:i}=n(2694),{TransportBase:o}=n(383),{TransportProtocol:a}=n(9072),{lazyValue:c}=r,u=c((()=>({[a.HTTP_BASE64]:s.StateBase64,[a.HTTP_BINARY]:s.StateBinary,[a.HTTP_BINARY_STREAMING]:s.StateStreamingAndBinary,[a.WS_BINARY]:s.StateWebSocketBinary})));e.exports.TransportProtocolHandler=class{constructor(e,t){const n=o.useSsl(e);let s=null,r=null;t.slice().reverse().forEach((e=>{const t=u.value[e];s=new t(n,this.switchState.bind(this),r),r=s})),this._transport=s,this._transport.onEnter()}getTransportProtocol(){return this._transport.getTransportProtocol()}completeDowngrade(e){return this._transport.completeDowngrade(e)}canCompleteDowngrade(){return null!==this._transport.getNextState()}toString(){return this._transport.toString()}switchState(e,t){this._transport,this._transport=e,e.onEnter()}}},5237:(e,t,n)=>{e.exports=n(7847)},5256:e=>{e.exports.SessionEventCBInfo=class{constructor(e,t){this.userObject=t,this.sessionEventCBFunction=e}}},5403:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.PublisherStateNames=s.new({UNBOUND:"PublisherUnbound",OPENFLOWSENT:"PublisherOpenFlowSent",UP:"PublisherUp",FAILED:"PublisherFailed",CLOSEFLOWSENT:"PublisherCloseFlowSent",DATA_XFER:"PublisherDataXfer",FLOW_CONTROLLED:"MessagePublisherFlowControlled",RETRANSMITTING:"PublisherRetransmitting"})},5424:(e,t,n)=>{const{ErrorSubcode:s}=n(5129),{makeMap:r}=n(7444),i=s,o=r(400,r("client name parse error",i.CLIENT_NAME_INVALID,"document is too large",i.MESSAGE_TOO_LARGE,"inactivity timeout",i.INACTIVITY_TIMEOUT,"max num subscriptions exceeded",i.SUBSCRIPTION_TOO_MANY,"message too long",i.MESSAGE_TOO_LARGE,"nolocal discard",i.NOLOCAL_DISCARD,"not enough space",i.OUT_OF_RESOURCES,"subscription already exists",i.SUBSCRIPTION_ALREADY_PRESENT,"subscription attributes conflict with existing subscription",i.SUBSCRIPTION_ATTRIBUTES_CONFLICT,"subscription not found",i.SUBSCRIPTION_NOT_FOUND,"subscription parse error",i.SUBSCRIPTION_INVALID,"topic parse error",i.INVALID_TOPIC_SYNTAX,"unknown transport session identifier",i.UNKNOWN_TRANSPORT_SESSION_ID,"xml parse error",i.XML_PARSE_ERROR,"unsupported ssl downgrade value",i.LOGIN_FAILURE),401,r("",i.LOGIN_FAILURE),403,r("basic authentication is shutdown",i.BASIC_AUTHENTICATION_IS_SHUTDOWN,"client certificate authentication is shutdown",i.CLIENT_CERTIFICATE_AUTHENTICATION_IS_SHUTDOWN,"client name already in use",i.CLIENT_NAME_ALREADY_IN_USE,"client username is shutdown",i.CLIENT_USERNAME_IS_SHUTDOWN,"dynamic clients not allowed",i.DYNAMIC_CLIENTS_NOT_ALLOWED,"invalid virtual router address",i.INVALID_VIRTUAL_ADDRESS,"forbidden",i.CLIENT_ACL_DENIED,"message vpn not allowed",i.MESSAGE_VPN_NOT_ALLOWED,"publish acl denied",i.PUBLISH_ACL_DENIED,"replication is standby",i.REPLICATION_IS_STANDBY,"selector does not match",i.SELECTOR_DOES_NOT_MATCH,"subscription acl denied",i.SUBSCRIPTION_ACL_DENIED,"subscription does not match",i.SUBSCRIPTION_DOES_NOT_MATCH,"compression is shutdown",i.LOGIN_FAILURE,"shared subscriptions not supported on topic endpoints",i.SHARED_SUBSCRIPTIONS_ENDPOINT_NOT_ALLOWED,"shared subscriptions not supported on queues",i.SHARED_SUBSCRIPTIONS_ENDPOINT_NOT_ALLOWED,"shared subscription permission denied",i.SHARED_SUBSCRIPTIONS_NOT_ALLOWED),404,r("",i.LOGIN_FAILURE),503,r("low priority msg congestion",i.LOW_PRIORITY_MSG_CONGESTION,"message vpn unavailable",i.MESSAGE_VPN_UNAVAILABLE,"replication is standby",i.REPLICATION_IS_STANDBY,"service unavailable",i.GM_UNAVAILABLE,"spool over quota",i.SPOOL_OVER_QUOTA,"subscriber delete in progress",i.CLIENT_DELETE_IN_PROGRESS,"too many clients",i.TOO_MANY_CLIENTS,"too many connections for vpn",i.TOO_MANY_CLIENTS,"max message usage exceeded",i.MAX_MESSAGE_USAGE_EXCEEDED),507,r("ad not ready",i.GM_NOT_READY)),a=r("PARENT",o,400,r("already bound",i.ALREADY_BOUND,"endpoint already exists",i.ENDPOINT_ALREADY_EXISTS,"subscription already exists",i.SUBSCRIPTION_ALREADY_PRESENT,"already exists",i.ENDPOINT_ALREADY_EXISTS,"endpoint property mismatch",i.ENDPOINT_PROPERTY_MISMATCH,"invalid durable topic endpoint name",i.INVALID_DTE_NAME,"invalid selector",i.INVALID_SELECTOR,"invalid topic name",i.INVALID_TOPIC_NAME_FOR_TOPIC_ENDPOINT,"invalid queue or topic endpoint durability",i.INVALID_QUEUE_OR_TOPIC_ENDPOINT_DURABILITY,"queue not found",i.QUEUE_NOT_FOUND,"quota out of range",i.QUOTA_OUT_OF_RANGE,"unknown flow name",i.UNKNOWN_FLOW_NAME,"unsubscribe not allowed",i.UNSUBSCRIBE_NOT_ALLOWED_CLIENTS_BOUND,"invalid queue name",i.INVALID_QUEUE_NAME,"invalid topic endpoint name",i.INVALID_TE_NAME,"message size out of range",i.MESSAGE_SIZE_OUT_OF_RANGE,"unsupported permissions",i.UNSUPPORTED_PERMISSIONS),403,r("permission not allowed",i.PERMISSION_NOT_ALLOWED,"client initiated replay not allowed on non-exclusive topic endpoint",i.CLIENT_INITIATED_REPLAY_NON_EXCLUSIVE_NOT_ALLOWED,"client initiated replay not allowed on non-exclusive queue",i.CLIENT_INITIATED_REPLAY_NON_EXCLUSIVE_NOT_ALLOWED,"client initiated replay from inactive flow not allowed",i.CLIENT_INITIATED_REPLAY_INACTIVE_FLOW_NOT_ALLOWED,"client initiated replay from browser flow not allowed",i.CLIENT_INITIATED_REPLAY_BROWSER_FLOW_NOT_ALLOWED,"replay not supported on temporary queue",i.REPLAY_TEMPORARY_NOT_SUPPORTED,"replay not supported on anonymous queue",i.REPLAY_ANONYMOUS_QUEUE_NOT_SUPPORTED,"replay not supported on anonymous queue without subscriptions",i.REPLAY_ANONYMOUS_QUEUE_WITHOUT_SUBSCRIPTIONS_NOT_SUPPORTED,"unknown start location type",i.UNKNOWN_START_LOCATION_TYPE,"mismatched endpoint error id",i.MISMATCHED_ENDPOINT_ERROR_ID,"replay start message unavailable",i.REPLAY_START_MESSAGE_NOT_AVAILABLE,"browsing not supported on partitioned queue",i.PARTITIONED_QUEUE_BROWSING_NOT_SUPPORTED,"selectors not supported on partitioned queue",i.PARTITIONED_QUEUE_SELECTORS_NOT_SUPPORTED),503,r("durable topic endpoint shutdown",i.TOPIC_ENDPOINT_SHUTDOWN,"endpoint shutdown",i.TOPIC_ENDPOINT_SHUTDOWN,"max clients exceeded for durable topic endpoint",i.MAX_CLIENTS_FOR_TE,"max clients exceeded for topic endpoint",i.MAX_CLIENTS_FOR_TE,"max clients exceeded for queue",i.MAX_CLIENTS_FOR_QUEUE,"no more non-durable queue or topic endpoint",i.NO_MORE_NON_DURABLE_QUEUE_OR_TOPIC_ENDPOINT,"no subscription match",i.NO_SUBSCRIPTION_MATCH,"queue shutdown",i.QUEUE_SHUTDOWN,"te shutdown",i.TOPIC_ENDPOINT_SHUTDOWN,"unknown durable topic endpoint",i.UNKNOWN_TOPIC_ENDPOINT_NAME,"unknown queue",i.UNKNOWN_QUEUE_NAME,"replay disabled",i.REPLAY_DISABLED,"replay cancelled",i.REPLAY_CANCELLED,"replay message unavailable",i.REPLAY_MESSAGE_UNAVAILABLE,"replay started",i.REPLAY_STARTED,"replayed message rejected by topic endpoint",i.REPLAY_MESSAGE_REJECTED,'replayed message rejected by queue"',i.REPLAY_MESSAGE_REJECTED,"replay log modified",i.REPLAY_LOG_MODIFIED,"mismatched endpoint error id",i.MISMATCHED_ENDPOINT_ERROR_ID,"out of replay resources",i.OUT_OF_REPLAY_RESOURCES,"topic or selector modified on durable topic endpoint",i.TOPIC_OR_SELECTOR_MODIFIED_ON_DURABLE_TOPIC_ENDPOINT,"replay failed",i.REPLAY_FAILED,"replay start time not available",i.REPLAY_START_TIME_NOT_AVAILABLE,"replay start message unavailable",i.REPLAY_START_MESSAGE_NOT_AVAILABLE,"sync replication ineligible",i.SYNC_REPLICATION_INELIGIBLE,"max endpoints exceeded",i.MAX_ENDPOINTS_EXCEEDED,"copy-from queue template missing",i.COPY_FROM_QUEUE_TEMPLATE_MISSING,"copy-from topic-endpoint template missing",i.COPY_FROM_TE_TEMPLATE_MISSING),507,r("assured delivery not ready",i.ASSURED_DELIVERY_NOT_READY));function c(e,t,n){if(200===t)return 0;const s=e[t]||{},r=(n||"").toLowerCase(),i=Object.keys(s).find((e=>e===r||r.indexOf(e)>=0));return i?s[i]:s[""]?s[""]:e.PARENT?c(e.PARENT,t,n):void 0}function u(e,t,n){const s=c(e,t,n);return void 0===s?i.UNKNOWN_ERROR:s}const l={getErrorSubcode:(e,t)=>u(o,e,t),getADErrorSubcode:(e,t)=>u(a,e,t)};e.exports.ErrorResponseSubcodeMapper=l},5439:(e,t,n)=>{const{ReplayStartLocation:s,ReplayStartType:r}=n(4115);class i extends s{constructor(e){super({_replayStartValue:e.getTime(),_type:r.DATE})}inspect(){return`[Epoch Time: ${this._replayStartValue}]`}static createReplayStartLocationDate(e){return new i(e)}}e.exports.ReplayStartLocationDate=i},5447:e=>{e.exports.BidiMap=class{constructor(...e){this.forward=new Map,this.reverse=new Map,e.forEach((e=>{this.setValues(e[0],e[1])}))}setValues(e,t){this.forward.set(e,t),this.reverse.set(t,e)}}},5456:(e,t,n)=>{var s=n(2195);const{DestinationType:r}=n(8805),{ErrorSubcode:i,OperationError:o}=n(6706),a={[r.TOPIC]:{_layers:[function(e,t,n,s,r){let i=s;return t.length-i>10&&!r.isNoExport&&t.startsWith("#noexport/",i)?(i+=10,r.isNoExport=!0):r.isNoExport=!1,{error:void 0,index:i,result:r}},function(e,t,n,s,r,i){let o,a,c=s,u=-1;return t.length-c>7&&!r.isShare&&t.startsWith("#share/",s)&&t.length-(c+7)>2?(c+=7,u=c,c=t.indexOf("/",u),c>0?(a=t.substring(u,c),c+=1,r.isShare=!0,r.shareGroup=a,r.dispatchTopicIndex=c):(o=i(`Illegal share Group in '${t}'@${u}.`),r.isShare=!0)):r.isShare=!1,{error:o,index:c,result:r}}],parse:function(e,t,n,s,r){const{error:i,result:o}=function(e,t,n,s,r,i){const o=e.length||0;let a,c=0,u=r||{};for(let r=0;r<o;++r){const{error:o,index:l,result:h}=e[r](t,n,s,c,u,i);if(c=l,u=h,a=o,a)break}return{error:a,result:u}}(this._layers,e,t,n,s,r);return{error:i,result:o}}}};function c(e,t){return new o(`Invalid ${e}: ${t}`,i.INVALID_TOPIC_SYNTAX)}class u{constructor(e){this._name=e,this._isShare=!1,this._isNoExport=!1,this._dispatchTopicIndex=-1,this._shareGroup=null}getName(){return this._name}get name(){return this.getName()}get isShare(){return this._isShare}set isShare(e){this._isShare=e}get isNoExport(){return this._isNoExport}set isNoExport(e){this._isNoExport=e}get dispatchTopicIndex(){return this._dispatchTopicIndex<0?0:this._dispatchTopicIndex}set dispatchTopicIndex(e){this._dispatchTopicIndex=e<0?-1:e}get shareGroup(){return this.isShare?this._shareGroup:null}set shareGroup(e){this.isShare&&(this._shareGroup=e)}toString(){return s(this)}static parseFromName(e,t=r.TOPIC){let n=new u(e),s=null;const i=a[t];if(i){const{error:r,result:o}=i.parse(t,e,null,n,c.bind(null,t));n=o,s=r}return{error:s,subInfo:n}}}e.exports.SubscriptionInfo=u},5594:()=>{},5656:(e,t,n)=>{const{ErrorSubcode:s,OperationError:r}=n(6706),{FactoryProfile:i,SolclientFactoryProfiles:o}=n(755),{Parameter:a}=n(802),{ProfileBinding:c}=n(4807),{SolclientFactoryProperties:u}=n(852),l={initializeCount:0,initializers:[]},h={addInitializer(e){l.initializers.push(e)},createFactory:e=>function(...t){if(0===l.initializeCount)throw new r("SolclientFactory not initialized",s.INVALID_OPERATION);return e(...t)},init(e){if(l.initializeCount>0)return this;const t=new u(e),s=e&&e.profile||o.version7;if(a.isInstanceOf("factoryProps.profile",s,i),c.value=s,l.initializers.forEach((e=>{e.call(this,t,l)})),++l.initializeCount,null!=e){const{LOG_DEBUG:e}=n(2694)}return this},reset(){l.initializeCount=0},_getInitializeCount:()=>l.initializeCount,get profiles(){return o}};e.exports.SolclientFactory=h},5686:(e,t,n)=>{const{DestinationFromNetwork:s,DestinationType:r,Queue:i}=n(9620),{LOG_DEBUG:o,LOG_INFO:a}=n(2694),{SDTDestType:c}=n(3153),{SDTField:u}=n(7385),{SDTFieldType:l}=n(7849),{Topic:h}=n(9620),p={parseDestination:function(e,t,n){const o=e.readUInt8(t),p=e.toString("latin1",t+1,t+n);let d=s.createDestinationFromBytes(p);if(c[d.type]!==o)if(c[r.QUEUE]===o)d=i.createFromLocalName(p);else{if(c[r.TOPIC]!==o)return a(`Drop SDT field with invalid destination type ${o} when decoding ${p} to ${d.type}`),null;d.type,d=new h(p)}return u.create(l.DESTINATION,d)}};e.exports.ParseDestination=p},5711:(e,t,n)=>{const s=n(7385),{ErrorSubcode:r,OperationError:i}=n(6706);e.exports.SDTStreamContainer=class{constructor(){this._stream=[],this._writable=!0,this._readPt=0}hasNext(){return this._stream.length>this._readPt}getNext(){return this._readPt<this._stream.length?this._stream[this._readPt++]:void 0}rewind(){this._readPt=0}addField(e,t=void 0){if(this._writable)if(e instanceof s.SDTField)this._stream.push(e);else{if(void 0===t)throw new i("Invalid parameters to addField: expected SDTField, or type and value",r.PARAMETER_CONFLICT);this._stream.push(s.SDTField.create(e,t))}}}},5747:(e,t,n)=>{const{Stats:s}=n(2652),{StatsByMode:r}=n(8352),{StatType:i}=n(1737);e.exports={Stats:s,StatType:i,StatsByMode:r}},5749:(e,t,n)=>{const{SolaceError:s}=n(3922);class r extends s{constructor(e){super("NotImplementedError",e||"",r)}}e.exports.NotImplementedError=r},5796:e=>{let t;t=()=>{},e.exports.assert=t},5837:()=>{},5873:(e,t,n)=>{const{Parameter:s}=n(802),{Convert:r}=n(9783),{TraceContextSetter:i}=n(7317),{isBoolean:o,isStringOrNothing:a}=s,{stringToUint8Array:c,hexStringToUint8Array:u}=r;class l{constructor(e){this._traceId=a("traceId",e.traceId),this._spanId=a("spanId",e.spanId),this._isSampled=o("isSampled",e.isSampled),this._traceState=e.traceState,this._version=e.version}static clone(e){const t=new i;return t._setSpanId(e.getSpanId()),t._setTraceId(e.getTraceId()),t._setSampled(e.getIsSampled()),t._setTraceState(e.getTraceState()),t._setVersion(e.getVersion()),new l(t)}get version(){return this._version||1}getVersion(){return this.version}get MAX_TRACE_STATE_LENGTH(){return 512}get traceId(){return this._traceId}getTraceId(){return this._traceId}get spanId(){return this._spanId}getSpanId(){return this._spanId}get isSampled(){return this._isSampled}getIsSampled(){return this._isSampled||!1}get traceState(){return this._traceState}getTraceState(){return this._traceState||null}getTruncatedTraceState(){return this._standardTraceStateTruncation(this.MAX_TRACE_STATE_LENGTH)}getEncodedTraceContext(){if(null==this.traceId||null==this.spanId)return null;const e=null==this.traceState?0:this.traceState.length,t=new ArrayBuffer(32+e);let n=0;const s=new DataView(t);let r=0;r|=this.version<<4,r|=this.isSampled?4:0,s.setUint8(n,r,!1),n++;const i=u(this.traceId);for(let e=0;e<16;e++)s.setUint8(n+e,i[e],!1);n+=16;const o=u(this.spanId);for(let e=0;e<8;e++)s.setUint8(n+e,o[e],!1);if(n+=8,s.setUint8(n,1,!1),n++,s.setUint16(n,0,!1),s.setUint16(n+2,0,!1),n+=4,null==this.traceState)s.setUint16(n,0,!1),n+=2;else{const e=this.getTruncatedTraceState();if(null!=e){const t=e.length,r=new Uint16Array([t]);s.setUint16(n,r,!1),n+=2;const i=c(e);for(let e=0;e<i.length;e++)s.setUint8(n+e,i[e],!1);n+=i.length}}return new Uint8Array(t)}_standardTraceStateTruncation(e){if(!this._traceState||null==this._traceState)return null;if(this._traceState.length<e)return this._traceState;const t=new Array,n=new Array;let s=0;const r=this._traceState.split(",");for(let i=0;i<r.length;i++){let o=r[i];if(""!==o&&null!=o){let r=o.length;r>128?t.push(o):s+r+(s>0?1:0)<=e?(n.push(o),s+=r+(s>0?1:0)):t.push(o)}}for(let r=0;r<t.length;r++){let i=t[r],o=i.length;s+o+(s>0?1:0)<=e&&(n.push(i),s+=o+(s>0?1:0))}return n.join(",")}toString(){return"{traceId="+this.getTraceId()+", spanId="+this.getSpanId()+", sampled="+this.isSampled+", traceState="+(null==this.traceState?"}":"'"+this.traceState+"'}")}}e.exports.TraceContext=l},5898:(e,t,n)=>{const{MessagePublisher:s}=n(56),{MessagePublisherAcknowledgeMode:r}=n(6e3),{MessagePublisherEventName:i}=n(3840),{MessagePublisherProperties:o}=n(996),{MessagePublisherPropertiesValidator:a}=n(475),{PublisherFSMEvent:c}=n(9728),{PublisherFSMEventNames:u}=n(3865);e.exports.MessagePublisherAcknowledgeMode=r,e.exports.MessagePublisher=s,e.exports.MessagePublisherProperties=o,e.exports.PublisherFSMEvent=c,e.exports.PublisherFSMEventNames=u,e.exports.MessagePublisherPropertiesValidator=a,e.exports.MessagePublisherEventName=i},5921:(e,t,n)=>{const s=n(7339),{FsmObject:r}=n(6031);e.exports.EntryPoint=class extends r{constructor(e){let t;super({name:e.entryPointName}),this.impl=this.impl||{},this.impl.outerState=new s.State({name:`${e.state.getName()} outerEntryPoint: ${e.entryPointName}`,parentContext:e.state.getParent()}).initial((()=>e.state.transitionTo(t))),t=new s.State({name:`${e.state.getName()} innerEntryPoint: ${e.entryPointName}`,parentContext:e.state}).initial(e.func)}getDestState(){return this.impl.outerState}}},5958:(e,t,n)=>{const s=n(199),r=n(3450),{BaseSMFClient:i}=n(427),{BufferQueue:o}=n(9097),{Convert:a,Hex:c}=n(9783),{LogFormatter:u}=n(2694),l=n(8287).hp,{stringToUint8Array:h}=a,{formatHexString:p}=c,{LOG_ERROR:d}=new u("[buffer-smf-client]");function _(e){const t=e.peekView(Math.min(e.remaining(),64));d(`First 64 bytes (or fewer) of incoming buffer: \n${s.Debug.formatDumpBytes(t[0].toString("latin1",t[1]),!0,0)}`)}e.exports.a=class extends i{constructor(e,t,n){super(e,t,n),this._incomingBuffer=new o(8e7)}reset(){super.reset(),this._incomingBuffer&&this._incomingBuffer.reset()}rxDataString(e){this._rxDataCB(l.from(h(e)))}rxDataArrayBuffer(e){this._rxDataCB(l.from(e))}rxDataBuffer(e){this._rxDataCB(e)}_rxDataCB(e){this._session&&this._session.resetKeepAliveCounter();const t=this._incomingBuffer,n=t.put(e);let s=t.remaining();for(n||(_(t),this._rxMessageErrorCB(`Buffer overflow (length: ${s})`),this._incomingBuffer.reset());s>12;){const e=7&t.readUInt8(0);if(3!==e)return d(`Invalid smf version in smf header, version=${e}`),d("BufferSMFClient._rxDataCB(): couldn't decode message due to invalid smf header"),_(t),this._incomingBuffer.reset(),void this._rxMessageErrorCB("Error parsing incoming SMF - invalid SMF header detected");const n=t.readUInt32BE(8);if(n>t.remaining())break;const i=t.peekView(n),o=r.Codec.Decode.decodeCompoundMessage(i[0],i[1]);if(!o||!o.smfHeader){const e=this._session?this._session._sessionId:null,n=e?p(e):"N/A";return d(`BufferSMFClient._rxDataCB(): couldn't decode message (sessionId=${n})`),_(t),this._incomingBuffer.reset(),void this._rxMessageErrorCB("Error parsing incoming SMF")}t.advance(o.smfHeader.messageLength),this._rxSmfCB(o),s=t.remaining()}s||this._incomingBuffer.reset()}}},6e3:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.MessagePublisherAcknowledgeMode=s.new({PER_MESSAGE:"PER_MESSAGE",WINDOWED:"WINDOWED"})},6018:(e,t,n)=>{const s=n(7007).EventEmitter,{ArrayUtils:r}=n(968),{ErrorSubcode:i,OperationError:o}=n(6706),{LOG_WARN:a}=n(2694),{flatten:c,includes:u}=r,l=["error","newListener","removeListener"],h=["newListener","removeListener"],p={ignore(){},fail(){throw new Error("Emitter disabled")}};e.exports.EventEmitter=class extends s{constructor(e){super();const{direct:t,emits:n,unsafe:s,formatEventName:r}=e||{};this.formatEventName=r||(e=>e);const i=this.emit.bind(this);this._installDirectFilter(t,i),this._installErrorHandlers(s),this._installEmitVerifier(),this._listenerVerificationFilter=function(e){if("function"==typeof e)return t=>u(l,t)||e(t);if(!Array.isArray(e))return null;const t=new Set(c(e));l.forEach((e=>t.add(e)));const n=Array.from(t);return e=>u(n,e)}(n),this._emits=n}_installDirectFilter(e,t){if(e){if(u(h,e))throw new o(`Cannot configure listener collection events [${h.join(", ")}] as direct`,i.INTERNAL_ERROR);this._defaultEmitDirect=(...n)=>t(e,...n),this.emitDirect=this._defaultEmitDirect,this._directEventName=e,this.on=(e,t)=>{this._verifyListenerEvent(e);const n=super.on(e,t);return this._setEmitDirect(e,!0,t),n},this.addListener=(e,t)=>this.on(e,t),this.once=(e,t)=>{this._verifyListenerEvent(e);const n=super.once(e,t);return this._setEmitDirect(e,!1),n},this.prependListener=(e,t)=>{this._verifyListenerEvent(e);const n=super.prependListener(e,t);return this._setEmitDirect(e,!0,t),n},this.prependOnceListener=(e,t)=>{this._verifyListenerEvent(e);const n=super.prependOnceListener(e,t);return this._setEmitDirect(e,!1),n},this.removeAllListeners=e=>{const t=super.removeAllListeners(e);return e!==this._directEventName&&void 0!==e||(this.emitDirect=this._defaultEmitDirect),t},this.removeListener=(e,t)=>{const n=super.removeListener(e,t);return e===this._directEventName&&0===this.listenerCount(e)&&(this.emitDirect=this._defaultEmitDirect),n},this.directListenerCount=()=>this.listenerCount(this._directEventName),this.setOnFirstDirectListener=e=>{this._onFirstDirectListener=e}}}_setEmitDirect(e,t,n){e===this._directEventName&&(t&&1===this.directListenerCount()?this.emitDirect=n:this.emitDirect=this._defaultEmitDirect,1===this.directListenerCount()&&void 0!==this._onFirstDirectListener&&this._onFirstDirectListener())}_verifyListenerEvent(e){this._listenerVerificationFilter&&(null==e&&this.throwInternal(new o(`Emitter rejects listener for no-name event: ${e}`,i.PARAMETER_OUT_OF_RANGE)),this._listenerVerificationFilter(e)||this.throwInternal(new o(`Emitter rejects listeners for ${e}, emits ${this._emits}`,i.PARAMETER_OUT_OF_RANGE)))}_installEmitVerifier(){}_installErrorHandlers(e){if(e)return void(this.throwInternal=e=>{throw e});const t=this.emit.bind(this);this.throwInternal=function(e){throw this._internalError=!0,e},this.emit=(e,...n)=>{try{t(e,...n)}catch(s){if(this._internalError)throw this._internalError=void 0,s;const r=this.formatErrorEvent(s,e,...n);try{a(`Listener for '${r.info.event.formattedName}' threw exception, dispatching to 'error'`),t("error",r)}catch(e){a("Listener for 'error' threw exception:",e,"\nOriginal exception:",s)}}}}get isDirect(){return this.emitDirect&&this.emitDirect!==this._defaultEmitDirect}formatErrorEvent(e,t,...n){const s=this.formatEventName(t);return Object.assign(new o(`Unhandled error in event handler for '${s}'`,i.CALLBACK_ERROR,`On event: ${[t,...n]} ${e}`),{stack:e.stack,info:{event:{name:t,formattedName:s,args:n},error:e}})}disableEmitter(){this._defaultEmitDirect=p.ignore,this.removeAllListeners(),this.emit=p.ignore,this.addListener("removeListener",p.fail),this.addListener("newListener",p.fail)}}},6031:(e,t,n)=>{const{OperationError:s}=n(6706);e.exports.FsmObject=class{constructor(e){if(!e)throw new s("No spec provided");if(!e.name)throw new s("No name provided for spec");this.impl={name:e.name}}toString(){let e=this.getExtraStringInfo();return e.length>0&&(e=`; ${e}`),`{${this.constructor.name}: ${this.getName()}${e}}`}getExtraStringInfo(){return""}getName(){return this.impl.name}}},6039:(e,t,n)=>{const{APIPropertiesValidators:s}=n(968),{QueueDescriptor:r}=n(8976),{QueueType:i}=n(6228),{validateInstance:o,valBoolean:a,valIsMember:c,valTopicString:u}=s,l={validate(e){const t=o.bind(null,"QueueDescriptor",e);e instanceof r&&t("name",[u]),t("type",[c,i,"QueueType"]),t("durable",[a])}};e.exports.QueueDescriptorValidator=l},6057:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.SessionStateName=s.new({DISCONNECTED:"SessionDisconnected",CONNECTING:"SessionConnecting",WAITING_FOR_TRANSPORT:"WaitingForTransport",WAITING_FOR_TRANSPORT_UP:"WaitingForTransportUp",WAITING_FOR_LOGIN:"WaitingForLogin",TRANSPORT_UP:"SessionTransportUp",FULLY_CONNECTED:"SessionFullyConnected",SESSION_ERROR:"SessionError",DISCONNECTING:"SessionDisconnecting",REAPPLYING_SUBSCRIPTIONS:"ReapplyingSubscriptions",WAITING_FOR_PUBFLOW:"WaitingForMessagePublisher",DISPOSED:"SessionDisposed",WAITING_FOR_SUBCONFIRM:"WaitForSubConfirm",WAITING_FOR_CAN_ACCEPT_DATA:"WaitForCanAcceptData",DISCONNECTING_FLOWS:"DisconnectingFlows",FLUSHING_TRANSPORT:"FlushingTransport",DESTROYING_TRANSPORT:"DestroyingTransport",RECONNECTING:"Reconnecting",TRANSPORT_FAIL:"TransportFail",WAITING_FOR_INTERCONNECT_TIMEOUT:"WaitingForInterconnectTimeout",WAITING_FOR_DNS:"WaitingForDNS",WAITING_FOR_TRANSPORT_CHANGE:"WaitingForTransportChange"})},6145:(e,t,n)=>{const{ErrorSubcode:s}=n(6706),{FsmEvent:r,State:i,StateMachine:o}=n(7414),{LogFormatter:a}=n(2694),{TransportReturnCode:c}=n(9944),{TransportSessionEvent:u}=n(7368),{TransportSessionEventCode:l}=n(3427),{WebTransportEvent:h}=n(3246),{WebTransportState:p}=n(73),{LOG_TRACE:d,LOG_INFO:_}=new a;e.exports.WebTransportFSM=class extends o{constructor(e,t){super({name:"WebTransportFSM"});const n=e,o=this,l=new a;l.formatter=function(...e){return[`[web-transport-fsm=${t()}]`,...e]},this.log=l.wrap(this.log,this),this.transport=n,this.initial((function(){return this.transitionTo(this.WebTransportDown,(e=>{e.getStateMachine().getName()}))})),this.unhandledEventReaction((function(e){return e.getName(),this.getCurrentState().getName(),this})),this.WebTransportDown=new i({name:p.DOWN,parentContext:this}).reaction(h.CONNECT,(function(){return this.transitionTo(o.WebTransportConnecting)})).reaction(h.DESTROY,(function(e){return n.destroyInternal(e._destroyMsg,e._subcode),this.transitionTo(o.WebTransportDestroying)})),this.WebTransportConnecting=new i({name:p.CONNECTING,parentContext:this}).entry((()=>{try{if(n.connectInternal()!==c.OK){const e=n.getConnError(),t=new r({name:h.DESTROY});return t._destroyMsg=e?e.message:"Error occurred while establishing transport",t._subcode=e?e.subcode:null,t._eventReason=e,this.processEvent(t)}}catch(e){_(`transport.connectInternal threw: ${e.message}`);const t=new r({name:h.DESTROY});return t._destroyMsg=e.message,t._subcode=e.subcode?e.subcode:s.CONNECTION_ERROR,t._eventReason=e,this.processEvent(t)}})).reaction(h.SEND_ERROR,(e=>(n.notifyEvent(e._transportEvent),o.attemptDowngrade(e._transportEvent)))).reaction(h.CONNECT_TIMEOUT,(e=>o.attemptDowngrade(e._transportEvent))).reaction(h.DESTROYED_NOTICE,(e=>(n.notifyEvent(e._transportEvent),this.transitionTo(o.WebTransportDown)))).reaction(h.UP_NOTICE,(function(e){return n.notifyEvent(e._transportEvent),this.transitionTo(o.WebTransportUp)})).reaction(h.DESTROY,(function(e){return n.destroyInternal(e._destroyMsg,e._subcode),this.transitionTo(o.WebTransportDestroying)})),this.WebTransportDowngrading=new i({name:p.DOWNGRADING,parentContext:this}).reaction(h.DESTROYED_NOTICE,(function(e){return _("Web transport: request downgrade"),n.completeDowngrade()?this.transitionTo(o.WebTransportConnecting):(_("Web transport: connection error, no downgrade"),n.notifyEvent(e._transportEvent),o.notifyDowngradeFailed(),this.transitionTo(o.WebTransportDown))})).reaction(h.DESTROY,(function(e){return n.destroyInternal(e._destroyMsg,e._subcode),this.transitionTo(o.WebTransportDestroying)})),this.WebTransportUp=new i({name:p.UP,parentContext:this}).reaction(h.DOWNGRADE,(e=>o.attemptDowngrade(new u(e._downgradeMsg,e._subcode)))).reaction(h.DESTROYED_NOTICE,(function(e){return n.notifyEvent(e._transportEvent),this.transitionTo(o.WebTransportDown)})).reaction(h.DESTROY,(function(e){return n.destroyInternal(e._destroyMsg,e._subcode),this.transitionTo(o.WebTransportDestroying)})).reaction(h.SEND_ERROR,(function(e){return n.notifyEvent(e._transportEvent),n.destroyInternal(e._destroyMsg,e._subcode),this.transitionTo(o.WebTransportDestroying)})),this.WebTransportDestroying=new i({name:p.DESTROYING,parentContext:this}).reaction(h.DESTROYED_NOTICE,(function(e){return n.notifyEvent(e._transportEvent),this.transitionTo(o.WebTransportDown)}))}attemptDowngrade(e){const{infoStr:t,errorSubcode:n}=e;return this.transport.beginDowngrade(t,n)?this.transitionTo(this.WebTransportDowngrading):(this.transport.destroyInternal(t,n),this.transport.notifyEvent(e),this.transitionTo(this.WebTransportDestroying))}notifyDowngradeFailed(){this.transport.notifyEvent(new u(l.DOWNGRADE_FAILED,"Downgrade failed"))}}},6228:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.QueueType=s.new({QUEUE:"QUEUE",TOPIC_ENDPOINT:"TOPIC_ENDPOINT"})},6247:(e,t,n)=>{const{ReplicationGroupMessageId:s,RgmidFactory:r}=n(6475),{Message:i}=n(1884),{MessageCacheStatus:o}=n(7366),{MessageDeliveryModeType:a}=n(177),{MessageDumpFlag:c}=n(3901),{MessageDumpStandardProvider:u}=n(3739),{MessageDumpUtil:l}=n(8892),{MessageType:h}=n(2868),{MessageOutcome:p}=n(568),{MessageUserCosType:d}=n(6676),{SolclientFactory:_}=n(4386);_.createMessage=_.createFactory((()=>new i)),_.createReplicationGroupMessageId=_.createFactory((e=>r.fromString(e))),e.exports.Message=i,e.exports.MessageCacheStatus=o,e.exports.MessageDeliveryModeType=a,e.exports.MessageDumpFlag=c,e.exports.MessageDumpStandardProvider=u,e.exports.MessageDumpUtil=l,e.exports.MessageType=h,e.exports.MessageOutcome=p,e.exports.MessageUserCosType=d,e.exports.ReplicationGroupMessageId=s,e.exports.RgmidFactory=r},6284:e=>{function t(e,t){const n=String(e),s=String(t);return n>s?1:n<s?-1:0}e.exports={ArrayOperations:{defaultComparator:t,insertOrdered:function(e,n,s=t){const r=[...e];return this.inplaceInsertOrdered(r,n,s)},inplaceInsertOrdered:function(e,n,s=t){let r=0,i=0,o=e.length;if(o>0)if(s(n,e[o-1])>0)r=o;else for(r=i+o>>1;o>i;)s(n,e[r])<0?o=r:i=r+1,r=i+o>>1;e.splice(r,0,n)}}}},6321:function(e,t,n){let s;{const e="undefined"!=typeof window?window:this;n(8858)(e),s=function(...e){return new URL(...e)}}e.exports.parseURL=s},6324:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.SessionEventName=s.new({CONNECT:"SessionConnect",DISCONNECT:"SessionDisconnect",DISPOSE:"SessionDispose",CONNECT_TIMEOUT:"SessionConnectTimeout",CONNECT_WAIT_TIMEOUT:"SessionConnectWaitTimeout",DOWNGRADE_TIMEOUT:"SessionDowngradeTimeout",TRANSPORT_UP:"SessionTransportUp",TRANSPORT_DESTROYED:"SessionTransportDestroyed",TRANSPORT_CAN_ACCEPT_DATA:"SessionTransportCanAcceptData",TRANSPORT_PARSE_ERROR:"SessionTransportParseError",TRANSPORT_PROTOCOL_SMP:"SessionSMPMessage",TRANSPORT_PROTOCOL_CLIENTCTRL:"SessionClientCtrlMessage",EXCEPTION:"SessionException",SUBSCRIBE_TIMEOUT:"SessionSubscribeTimeout",CREATE_SUBSCRIBER:"SessionCreateSubscriber",FLOW_UP:"SessionFlowUp",FLOW_FAILED:"SessionFlowFailed",SEND_ERROR:"SessionSendError",FLOWS_DISCONNECTED:"SessionFlowsDisconnected",TRANSPORT_FLUSHED:"SessionTransportFlushed",DNS_RESOLUTION_COMPLETE:"SessionDNSResolutionComplete",TRANSPORT_CHANGE_DONE:"SessionTransportChangeDone"})},6334:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.SessionEventCode=s.new({UP_NOTICE:0,DOWN_ERROR:1,CONNECT_FAILED_ERROR:2,REJECTED_MESSAGE_ERROR:4,SUBSCRIPTION_ERROR:5,SUBSCRIPTION_OK:6,VIRTUALROUTER_NAME_CHANGED:7,REQUEST_ABORTED:8,REQUEST_TIMEOUT:9,PROPERTY_UPDATE_OK:10,PROPERTY_UPDATE_ERROR:11,CAN_ACCEPT_DATA:13,DISCONNECTED:14,RECONNECTING_NOTICE:22,RECONNECTED_NOTICE:23,REPUBLISHING_UNACKED_MESSAGES:24,ACKNOWLEDGED_MESSAGE:25,UNSUBSCRIBE_TE_TOPIC_OK:26,UNSUBSCRIBE_TE_TOPIC_ERROR:27,MESSAGE:28,GUARANTEED_MESSAGE_PUBLISHER_DOWN:29,PROVISION_ERROR:30,PROVISION_OK:31})},6350:(e,t,n)=>{const s=n(199),{Convert:r,Base64:i}=n(9783),{LOG_DEBUG:o,LOG_INFO:a,LOG_WARN:c,LOG_ERROR:u}=n(2694),{sendXhrBinary:l,sendXhrText:h}=n(7440),{StringBuffer:p,TimingBucket:d}=n(968),{TransportReturnCode:_}=n(9944),{XHRFactory:E}=n(3124),{arrayBufferToString:g}=r;function T(e){return!e.match(/^(http|ws)(s?):/i)&&window.location&&window.location.origin?window.location.origin+("/"!==e.charAt(0)?"/":"")+e:e}class S{constructor(){this.WaitedToken=new d("WaitedToken",100),this.HadToken=new d("HadToken",100),this.ReturnedToken=new d("ReturnedToken",100)}toString(){let e="";return[this.WaitedToken,this.HadToken,this.ReturnedToken].forEach((t=>{t&&t.bucketCount()>0&&(e+=`${t.name} >> ${t}\n`)})),e}}class m{constructor(e,t,n,s,r,i,o){this.Options={url:T(e),contentType:i,base64Enc:t,streamProgressEvents:n,connectionClose:o},this._streamProgressBytes=0,this._xhr=null,this._rxDataCb=s,this._connErrorCb=r,this._reqActive=!1,this._REQCOUNTER=0,this._REQBASE=Math.floor(1e3*Math.random()),this._xhr=E.create(),this._handleAbortedReq=!m.browserSupportsXhrBinary(),this.stats=new S}recStat(e){}send(e,t=0,n=1){t>0&&(this._xhr.abort(),this._xhr=E.create()),this._xhr.open("POST",this.Options.url,!0),this._streamProgressBytes=0,this._xhr.onreadystatechange=()=>this.xhrStateChange(e,t,n),this._reqActive=!0,this.Options.base64Enc?h(this._xhr,e,this.Options.contentType,this.Options.connectionClose):l(this._xhr,e,this.Options.contentType,this.Options.connectionClose),this.recStat("SendMsg")}xhrStateChange(e,t,n){const r=this._xhr.readyState,o=this._xhr.LOADING,c=this._xhr.DONE;if(!(this.Options.streamProgressEvents&&r===o||r===c))return;if(!this._reqActive)return;let l=null;if(this._handleAbortedReq)try{l=this._xhr.status}catch(e){return void a(`Error trying to access status in XHR due to request aborted: ${e.message}`)}else l=this._xhr.status;if(200===l||304===l){let e=null;if(e=this._xhr.responseType&&"arraybuffer"===this._xhr.responseType?g(this._xhr.response):this._xhr.responseText,e=e.substring(this._streamProgressBytes,e.length),this._streamProgressBytes+=e.length,0===e.length&&r===o)return;if(this.Options.base64Enc)try{e=i.decode(e)}catch(t){return u(`Data decode error on: ${e}`),u(`Data decode error is: ${t.message}`),void this._rxDataCb(_.DATA_DECODE_ERROR,e)}else{const t=[],n=e.length;for(let s=0;s<n;s++)t.push(String.fromCharCode(255&e.charCodeAt(s)));e=t.join("")}return r===c&&(this._reqActive=!1),this._rxDataCb(_.OK,e),void(r===c&&e.length>0&&this._rxDataCb(_.OK,""))}const h=this._xhr.statusText;let d="";d=this._xhr.responseType&&"arraybuffer"===this._xhr.responseType?g(this._xhr.response):this._xhr.responseText||"";const E=d.length,T=(this.Options.url,e?e.length:0),{formatDumpBytes:S}=s.Debug,m=S(d.substr(0,Math.min(E,64)),!0,0);S((e||"").substr(0,Math.min(T,256)),!0,0);const f=n;this._reqActive&&400!==l&&0===d.length&&(0===t||t<f)?(a(`XHR failed while request active, will retry send, retry=${t+1}`),this.send(e,t+1,f)):(this._reqActive=!1,this._connErrorCb(l,new p(`HTTP request failed(status=${l} statusText=${h}, `,`responseText length=${E}, responseText[0..64]=\n`,m,`XHR errorCode=${this._xhr._error?this._xhr._error.code:""})`).toString()))}isUsingBase64(){return this.Options.base64Enc}abort(){this._reqActive=!1,this._xhr&&this._xhr.abort&&this._xhr.abort()}static browserSupportsXhrBinary(){return l!==h}static browserSupportsStreamingResponse(){const e=E.create(),t=e&&null===e.onprogress;return a(`http browserStreamingCheck - if XMLHTTPRequest supported and XMLHTTPRequest support onprogress: ${t}`),t}}e.exports.HTTPConnection=m},6360:(e,t,n)=>{const{EncodeSingleElement:s}=n(3884),{IEEE754LIB:r}=n(4493),{ParseSingleElement:i,StringToBuffer:o}=n(2497),{encodeSingleElement:a}=s,{parseSingleElement:c}=i,{stringToBuffer:u}=o,l={encodeSingleElement:a,parseSingleElement:c,stringToBuffer:u,IEEE754LIB:r};e.exports.Codec=l},6415:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.SslDowngrade=s.new({NONE:"NONE",PLAINTEXT:"PLAIN_TEXT"})},6472:(e,t,n)=>{const{CapabilityType:s}=n(2484);e.exports.DefaultCapabilities={createDefaultCapabilities:e=>({[s.GUARANTEED_MESSAGE_CONSUME]:!0,[s.GUARANTEED_MESSAGE_PUBLISH]:!0,[s.SHARED_SUBSCRIPTIONS]:!0,[s.MAX_GUARANTEED_MSG_SIZE]:e.assumedMaxAdSize,[s.AD_APP_ACK_FAILED]:!0})}},6475:(e,t,n)=>{const{ErrorSubcode:s,OperationError:r}=n(6706),{Long:i}=n(9783),{Parameter:o}=n(802),{ReplayStartLocation:a,ReplayStartType:c}=n(9309),u=n(8287).hp,l="rmid1:",h=/^[0-9a-fA-F]{32}$/g,p=i.UZERO,{isString:d,isValue:_,isInstanceOf:E}=o;class g extends a{constructor(e,t){super({_replayStartValue:{suid:e,messageId:t},_type:c.RGMID}),this._suid=e,this._messageId=t}compare(e){E("otherReplicationGroupMessageId",e,g);const t=e;if(!this._suid.equals(t._suid)){const e="Unable to compare Replication Group Message ID from different origins";throw new r(`Parameter otherReplicationGroupMessageId[${t.toString()}] failed validation`,s.MESSAGE_ID_NOT_COMPARABLE,e)}return this._messageId.gt(t._messageId)?1:this._messageId.lt(t._messageId)?-1:0}inspect(){return`[Replication Group Message Id: ${this.toString()}]`}toString(){const e=u.from(this._suid.toBytesBE().concat(this._messageId.toBytesBE())).toString("hex");return`${l}${e.substring(0,5)}-${e.substring(5,16)}-${e.substring(16,24)}-${e.substring(24,32)}`}}function T(e){return new g(e.suid,e.msgid)}const S={fromString:function(e){if(d("id",e),_("id",e.length,41,s.PARAMETER_OUT_OF_RANGE,`length expected: 41 but is ${e.length}`),!e.startsWith(l))throw new r("Parameter id has invalid Replication Group Message ID format",s.PARAMETER_OUT_OF_RANGE,`id: ${e}, does not start with ${l}`);const t=e.substring(6).split("-");if(4!==t.length||5!==t[0].length||11!==t[1].length||8!==t[2].length||8!==t[3].length)throw new r("Parameter id has invalid Replication Group Message ID format",s.PARAMETER_OUT_OF_RANGE,`id: ${e}, does not have valid separation of components`);const n=t.join("").trim();if(h.test(""),!h.test(n))throw new r("Parameter id has invalid Replication Group Message ID format",s.PARAMETER_OUT_OF_RANGE,`id: ${e}, invalid data string value`);let o;try{o=u.from(n,"hex")}catch(t){throw new r("Parameter id has invalid Replication Group Message ID format",s.PARAMETER_OUT_OF_RANGE,`id: ${e}, failed to read data, cause: ${t.message}`)}const a=o?o.length:0;if(16!==a)throw new r("Parameter id has invalid Replication Group Message ID format",s.PARAMETER_OUT_OF_RANGE,`id: ${e}, failed to read data from id expected length of 16 got ${a}`);const c=i.fromBits(o.readUInt32BE(4),o.readUInt32BE(0),!0);if(c.eq(p))throw new r("Parameter id has invalid Replication Group Message ID format",s.PARAMETER_OUT_OF_RANGE,`id: ${e}, has invalid origin`);return T({suid:c,msgid:i.fromBits(o.readUInt32BE(12),o.readUInt32BE(8),!0)})}};S.from=T,S.INVALID_SUID=p,e.exports.ReplicationGroupMessageId=g,e.exports.RgmidFactory=S},6598:(e,t,n)=>{var s=n(2195);const r=n(3450),{CapabilityType:i}=n(5024),{ConsumerFSM:o}=n(9035),{ConsumerFSMEvent:a}=n(946),{ConsumerFSMEventNames:c}=n(1699),{ErrorResponseSubcodeMapper:u,ErrorSubcode:l,OperationError:h}=n(6706),{Flow:p,FlowOperation:d}=n(8860),{MessageConsumerEvent:_}=n(3247),{MessageOutcome:E}=n(6247),{MessageConsumerEventName:g}=n(6934),{MessageConsumerProperties:T}=n(2558),{MessageConsumerPropertiesValidator:S}=n(6681),{Queue:m,Topic:f}=n(9620),{QueueAccessType:I,QueuePermissions:R,QueueDiscardBehavior:C}=n(9631);function A(e){return`MessageConsumerEventName.${g.describe(e)}`}let O=0;e.exports.MessageConsumer=class extends p{constructor({properties:e,sessionInterfaceFactory:t}={}){const n=new T(e);S.validate(n.browser?"QueueBrowserProperties":"MessageConsumerProperties",n,e),super(n,t,{direct:g.MESSAGE,emits:g.values,formatEventName:A});const s=this.logger.formatter;this.logger.formatter=(...e)=>s("[message-consumer]",...e),this._active=void 0,this._fsm=this._makeFSM(),this.endpointErrorId=void 0,this.partitionGroupId=void 0,this._on(g.ACTIVE,(()=>this._onFlowActive(!0))),this._on(g.INACTIVE,(()=>this._onFlowActive(!1))),this._on(g.DOWN_ERROR,this._onFlowDisconnected.bind(this)),this._on(g.UP,this._onFlowUp.bind(this)),this._fsm.start()}_makeFSM(){const e=this._properties,t="ConsumerFSM "+O++;return new o({name:t,consumer:this,sessionInterface:this._sessionInterface,properties:e})}start(){this._operationCheck(d.START),this._fsm.requestStartDispatchUser()}stop(){this._operationCheck(d.STOP),this._fsm.requestStopDispatchUser()}connect(){if(null!==this._sessionInterface.getCapability(i.GUARANTEED_MESSAGE_CONSUME)&&!this._sessionInterface.isCapable(i.GUARANTEED_MESSAGE_CONSUME))throw new h("Consumer is not supported by router for this client",l.INVALID_OPERATION,null);super.connect(),this.processFSMEvent(new a({name:c.FLOW_OPEN}))}disconnect(){super.disconnect(),this.processFSMEvent(new a({name:c.FLOW_CLOSE}))}getDestination(){const e=this._fsm.getDestination();return e instanceof m?new m(e):new f(e)}_disconnectSession(){super._disconnectSession(),this.processFSMEvent(new a({name:c.SESSION_DISCONNECT}))}_operationCheck(e){if(super._operationCheck(e),e===d.GET_DESTINATION&&this._isDisconnected())throw new h("Cannot get destination of a disconnected flow",l.INVALID_OPERATION)}applicationAck(e,t=!1){const{LOG_TRACE:n}=this.logger;this._fsm.applicationAck(e,t)}applicationSettle(e,t){const{LOG_TRACE:n}=this.logger;E.nameOf(t),this._fsm.applicationSettle(e,t)}getDisposedEvent(){return g.DISPOSED}handleDataMessage(e){const{LOG_TRACE:t}=this.logger;e.setMessageConsumer(this),this._fsm.acceptMessage(e)}handleUncorrelatedControlMessage(e){const{LOG_INFO:t,LOG_DEBUG:n,LOG_TRACE:s}=this.logger;t("Handling uncorrelated control message");const i=e.msgType,{SMFAdProtocolMessageType:o}=r;switch(i){case o.UNBIND:{const t=e.smfHeader.pm_respcode,n=e.smfHeader.pm_respstr,s=u.getADErrorSubcode(t,n);e.getEndpointErrorId(),void 0!==e.getEndpointErrorId()&&(this.endpointErrorId=e.getEndpointErrorId()),this.processFSMEvent(new a({name:c.FLOW_UNBOUND},new h(n,s,t)))}break;case o.FLOWCHANGEUPDATE:this.processFSMEvent(new a({name:c.FLOW_ACTIVE_IND},{active:e.getActiveFlow()}));break;default:o.describe(i)}}getProperties(){return super.getProperties()}onVRNChanged(){this.processFSMEvent(new a({name:c.VIRTUALROUTER_NAME_CHANGED}))}get accessType(){return this._accessType}set accessType(e){this._accessType=e}get active(){return this._active}set active(e){e!==this._active&&this._emit(e?g.ACTIVE:g.INACTIVE),this._active=e}get queueDiscardBehavior(){return this._queueDiscardBehavior}set queueDiscardBehavior(e){this._queueDiscardBehavior=e}get respectsTTL(){return this._respectsTTL}set respectsTTL(e){this._respectsTTL=e}get flowId(){return this._flowId}set flowId(e){this._flowId=e}get permissions(){return this._permissions||0}set permissions(e){this._permissions=e}_onFlowActive(e){const{LOG_DEBUG:t}=this.logger;this._flowId,this._active=e}_onFlowDisconnected(e){const{LOG_INFO:t}=this.logger;t(`${this} disconnected: ${e}.message`)}_disposeFSM(){const{LOG_INFO:e}=this.logger;e("Disposing FSM"),this.processFSMEvent(new a({name:c.DISPOSE}))}_onFlowUp(){const{LOG_INFO:e}=this.logger;e(`Flow is up: flowId = ${this._flowId}`)}inspect(){return Object.assign(super.inspect(),{destination:this._destination,accessType:I.describe(this.accessType),permissions:R.describe(this.permissions),respectsTTL:this.respectsTTL,active:this.wantFlowChangeNotify?this.active:"(indications disabled)",wantFlowChangeNotify:this.wantFlowChangeNotify,queueDiscardBehavior:C.describe(this.queueDiscardBehavior),maxWindowSize:this._fsm.maxWindowSize})}toString(){return s(this)}_isDisconnected(){return this._fsm.isDisconnected()}addSubscription(e,t,n){this._sessionInterface.updateQueueSubscription(e,this._fsm.getDestination(),!0,this,((n,s,r,i)=>{if(n){const n=new _(g.SUBSCRIPTION_OK,i,r,s,t,`Topic: ${e.getName()}`);this._emit(g.SUBSCRIPTION_OK,n)}else{const n=new _(g.SUBSCRIPTION_ERROR,i,r,s,t,`Topic: ${e.getName()}`);this._emit(g.SUBSCRIPTION_ERROR,n)}}),n)}removeSubscription(e,t,n){this._sessionInterface.updateQueueSubscription(e,this._fsm.getDestination(),!1,this,((n,s,r,i)=>{if(n){const n=new _(g.SUBSCRIPTION_OK,i,r,s,t,`Topic: ${e.getName()}`);this._emit(g.SUBSCRIPTION_OK,n)}else{const n=new _(g.SUBSCRIPTION_ERROR,i,r,s,t,`Topic: ${e.getName()}`);this._emit(g.SUBSCRIPTION_ERROR,n)}}),n)}}},6663:e=>{function t(e){throw new Error("Test environment will not override build environment")}t.target=()=>t(),e.exports=t},6670:(e,t,n)=>{const{AuthenticationScheme:s,CapabilityType:r,ClientCapabilityType:i,MutableSessionProperty:o,SessionProperties:a}=n(5024),{BaseMessage:c}=n(8668),{Bits:u,Convert:l}=n(9783),{DestinationType:h,DestinationUtil:p}=n(9620),{ErrorSubcode:d,OperationError:_}=n(6706),{Process:E,StringUtils:g,Version:T}=n(968),{SMFClientCtrlMessageType:S}=n(9640),{SMFClientCtrlParam:m,SMFClientCtrlAuthType:f}=n(9685),{SMFHeader:I}=n(9731),{SMFParameter:R}=n(1123),{SMFProtocol:C}=n(5052),{LOG_TRACE:A}=n(2694),{get:O,set:N}=u,{int8ToStr:y,strToInt8:P,int16ToStr:D,int32ToStr:b,strToInt16:M,strToInt32:v}=l,{nullTerminate:w,stripNullTerminate:L}=g,{validateAndEncode:U}=p,F=[r.JNDI,r.COMPRESSION,r.GUARANTEED_MESSAGE_CONSUME,r.TEMPORARY_ENDPOINT,r.GUARANTEED_MESSAGE_PUBLISH,r.GUARANTEED_MESSAGE_BROWSE,r.ENDPOINT_MGMT,r.SELECTOR,r.ENDPOINT_MESSAGE_TTL,r.QUEUE_SUBSCRIPTIONS,null,r.SUBSCRIPTION_MANAGER,r.MESSAGE_ELIDING,r.TRANSACTED_SESSION,r.NO_LOCAL,r.ACTIVE_CONSUMER_INDICATION,r.PER_TOPIC_SEQUENCE_NUMBERING,r.ENDPOINT_DISCARD_BEHAVIOR,r.CUT_THROUGH,null,r.MESSAGE_REPLAY,r.COMPRESSED_SSL,null,r.SHARED_SUBSCRIPTIONS,r.BR_REPLAY_ERRORID,r.AD_APP_ACK_FAILED,r.VAR_LEN_EXT_PARAM],x=new Map([[i.UNBIND_ACK,128],[i.BR_ERRORID,64],[i.PQ,32]]);class B extends c{constructor(e=0){super(new I(C.CLIENTCTRL,1)),this.msgType=e,this.version=1}getP2PTopicValue(){const e=this.getParameter(m.P2PTOPIC);return e?L(e.getValue()):null}getVpnNameInUseValue(){const e=this.getParameter(m.MSGVPNNAME);return e?L(e.getValue()):null}getVridInUseValue(){const e=this.getParameter(m.VRIDNAME);return e?L(e.getValue()):null}getUserIdValue(){const e=this.getParameter(m.USERID);return e?L(e.getValue()):null}getRouterCapabilities(){let e=[],t=this.getParameter(m.ROUTER_CAPABILITIES);return t&&(e=B.prmParseCapabilitiesValue(t.getValue(),e)),t=this.getParameter(m.SOFTWAREVERSION),t&&(e[r.PEER_SOFTWARE_VERSION]=L(t.getValue())),t=this.getParameter(m.SOFTWAREDATE),t&&(e[r.PEER_SOFTWARE_DATE]=L(t.getValue())),t=this.getParameter(m.PLATFORM),t&&(e[r.PEER_PLATFORM]=L(t.getValue())),t=this.getParameter(m.PHYSICALROUTERNAME),t&&(e[r.PEER_ROUTER_NAME]=L(t.getValue())),e}static prmGetDtoPriorityValue(e){if(void 0===e.local||void 0===e.network)return!1;let t=0;return t=N(t,e.local,8,8),t=N(t,e.network,0,8),D(t)}static prmParseDtoPriorityValue(e){const t={},n=M(e.substr(0,2));return t.local=O(n,8,8),t.network=O(n,0,8),t}static prmParseCapabilitiesValue(e,t){const n=t;if(!e||!n)return!1;const s=r;let i=0;const o=P(e[i]);let a;++i;for(let t=0;t<o;++t){const s=7&t;0===s&&(a=P(e[i]),++i);const r=F[t];r&&(n[r]=!!O(a,7-s,1))}for(let t=0;i<e.length&&t<500;++t){const t=P(e[i]);i++;const r=v(e.substr(i,4))-5;i+=4;const o=e.substr(i,r);switch(i+=r,t){case 0:n[s.PEER_PORT_SPEED]=4===o.length?v(o):0;break;case 1:n[s.PEER_PORT_TYPE]=1===o.length?P(o):0;break;case 2:n[s.MAX_GUARANTEED_MSG_SIZE]=4===o.length?v(o):0;break;case 3:n[s.MAX_DIRECT_MSG_SIZE]=4===o.length?v(o):0}}return n}static getLogin(e,t,n,r){if(!(e instanceof a))return!1;const o=new B(S.LOGIN),c=o._smfHeader,u=e.authenticationScheme===s.CLIENT_CERTIFICATE;c.pm_corrtag=r,e.password&&!u&&(c.pm_password=e.password),e.userName&&(c.pm_username=e.userName),e.subscriberLocalPriority&&e.subscriberNetworkPriority&&o.addParameter(new R(0,m.DELIVERTOONEPRIORITY,B.prmGetDtoPriorityValue({local:e.subscriberLocalPriority,network:e.subscriberNetworkPriority}))),e.vpnName&&e.vpnName.length>0&&o.addParameter(new R(1,m.MSGVPNNAME,w(e.vpnName))),e.applicationDescription&&e.applicationDescription.length>0&&o.addParameter(new R(0,m.CLIENTDESC,w(e.applicationDescription))),e.userIdentification&&e.userIdentification.length>0&&o.addParameter(new R(0,m.USERID,w(e.userIdentification))),e.authenticationScheme===s.OAUTH2&&(o.addParameter(new R(1,m.AUTHENTICATION_SCHEME,f.OAUTH2)),e.idToken&&(c.pm_oidc_id_token=w(e.idToken)),e.accessToken&&(c.pm_oauth2_access_token=w(e.accessToken)),e.issuerIdentifier&&(c.pm_oauth2_issuer_identifier=w(e.issuerIdentifier))),o.addParameter(new R(0,m.CLIENTNAME,w(e.clientName))),o.addParameter(new R(0,m.PLATFORM,w(`${E.platform} - JS API (${T.mode})`))),e.noLocal&&o.addParameter(new R(0,m.NO_LOCAL,"")),u&&o.addParameter(new R(1,m.AUTHENTICATION_SCHEME,f.CLIENT_CERTIFICATE)),o.addParameter(new R(0,m.SOFTWAREDATE,w(T.formattedDate))),o.addParameter(new R(0,m.SOFTWAREVERSION,w(T.version))),t&&n?o.addParameter(new R(1,m.SSL_DOWNGRADE,"")):t?o.addParameter(new R(1,m.SSL_DOWNGRADE,"")):n&&o.addParameter(new R(1,m.SSL_DOWNGRADE,"\0"));const l=function(e){const t=Math.max.apply(null,e)+1;let n=0;return e.forEach((e=>{n+=x.get(e)})),y(t)+y(n)}([i.UNBIND_ACK,i.BR_ERRORID,i.PQ]);o.addParameter(new R(0,m.CLIENT_CAPABILITIES,l));const h=b(e.keepAliveIntervalInMsecs/1e3);return o.addParameter(new R(0,m.KEEP_ALIVE_INTERVAL,h)),o}static getUpdate(e,t,n){const s=new B(S.UPDATE);if(s.smfHeader.pm_corrtag=n,e===o.CLIENT_DESCRIPTION){const e=(t||"").toString().substr(0,250);s.addParameter(new R(0,m.CLIENTDESC,w(e)))}else if(e===o.CLIENT_NAME){const e=B.validateClientName(t,(e=>new _(`Invalid clientName: ${e}`,d.PARAMETER_OUT_OF_RANGE)));if(e)throw e;s.addParameter(new R(0,m.CLIENTNAME,w(t)))}return s}static validateClientName(e,t){const n=U(h.TOPIC,e,t);return n.error?n.error:n.bytes.length>161?t("Client Name too long (max length: 160)."):null}}e.exports.ClientCtrlMessage=B},6676:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.MessageUserCosType=s.new({COS1:0,COS2:1,COS3:2})},6681:(e,t,n)=>{const{AbstractQueueDescriptor:s,QueueDescriptor:r,QueueDescriptorValidator:i,QueueProperties:o,QueuePropertiesValidator:a,QueueType:c}=n(9631),{APIPropertiesValidators:u}=n(968),{Check:l}=n(802),{MessageConsumerAcknowledgeMode:h}=n(4590),{OperationError:p,ErrorSubcode:d}=n(6706),{ReplayStartLocation:_}=n(9309),{validateInstance:E,valBoolean:g,valInstance:T,valIsMember:S,valNumber:m,valRange:f,valTopicStringOrEmpty:I}=u;function R(e,t){if(t.queueDescriptor.getType()===c.TOPIC_ENDPOINT){if(t.queueDescriptor.isDurable()&&!t.createIfMissing&&!t.topicEndpointSubscription)throw new p("topicEndpointSubscription must be set when queueDescriptor refers to a durable topic endpoint and not allowed to create.",d.PARAMETER_CONFLICT)}else if(t.topicEndpointSubscription)throw new p("topicEndpointSubscription is set, but queueDescriptor refers to a queue that is not of type QueueType.TOPIC_ENDPOINT",d.PARAMETER_CONFLICT)}const C={validate(e,t,n){if(Object.prototype.hasOwnProperty.call(n,"transportAcknowledgeTimeoutInMsecs")&&Object.prototype.hasOwnProperty.call(n,"acknowledgeTimeoutInMsecs"))throw new p(`${e} validation: transportAcknowledgeTimeoutInMsecs and acknowledgeTimeoutInMsecs are mutually exclusive`,d.PARAMETER_CONFLICT);if(Object.prototype.hasOwnProperty.call(n,"transportAcknowledgeThresholdPercentage")&&Object.prototype.hasOwnProperty.call(n,"acknowledgeThreshold"))throw new p(`${e} validation: transportAcknowledgeThresholdPercentage and acknowledgeThreshold are mutually exclusive`,d.PARAMETER_CONFLICT);const u=E.bind(null,e,t);if(!(t.queueDescriptor instanceof s||t.queueDescriptor instanceof r))throw new p(`${e} validation: queue descriptor must be an AbstractQueueDescriptor or a QueueDescriptor`,d.PARAMETER_INVALID_TYPE);if(i.validate(t.queueDescriptor),t.queueProperties){if(t.queueDescriptor.durable&&!t.createIfMissing)throw new p(`${e} validation: queueProperties cannot be set unless queueDescriptor refers to a temporary queue, or createIfMissing is set.`,d.PARAMETER_CONFLICT);if(u("queueProperties",[T,o,"QueueProperties"]),a.validate(t.queueProperties),!t.queueDescriptor.durable&&l.something(t.queueProperties.accessType))throw new p(`${e} validation: queueProperties cannot specify accessType in creation of a temporary queue`,d.PARAMETER_CONFLICT)}if(t.queueDescriptor.type===c.TOPIC_ENDPOINT){if(t.queueDescriptor.durable&&!t.createIfMissing&&!t.topicEndpointSubscription)throw new p(`${e} validation: topicEndpointSubscription must be set for durable topic endpoints unless creation is allowed.`,d.PARAMETER_CONFLICT)}else if(t.topicEndpointSubscription)throw new p(`${e} validation: topicEndpointSubscription cannot be set unless descriptor.type is TOPIC_ENDPOINT`,d.PARAMETER_CONFLICT);if(u("connectTimeoutInMsecs",[m],[f,50,Number.MAX_VALUE]),u("connectAttempts",[m],[f,1,Number.MAX_VALUE]),u("topicEndpointSubscription",[R],[I]),u("acknowledgeMode",[S,h,"MessageConsumerAcknowledgeMode"]),u("transportAcknowledgeTimeoutInMsecs",[m],[f,20,1500]),u("transportAcknowledgeThresholdPercentage",[m],[f,1,75]),u("activeIndicationEnabled",[g]),u("noLocal",[g]),u("windowSize",[m],[f,1,255]),u("reconnectIntervalInMsecs",[m],[f,50,Number.MAX_VALUE]),t.replayStartLocation&&!(t.replayStartLocation instanceof _))throw new p(`${e} validation: replayStartLocation must be an instance of ReplayStartLocation`,d.PARAMETER_INVALID_TYPE)}};e.exports.MessageConsumerPropertiesValidator=C},6682:(e,t,n)=>{const s=n(6247),r=n(5024),i=n(3450),o=n(5747),{ErrorSubcode:a,OperationError:c}=n(6706),{LOG_TRACE:u,LOG_DEBUG:l,LOG_INFO:h,LOG_WARN:p}=n(2694),{SDTField:d,SDTFieldType:_,SDTStreamContainer:E}=n(769),{CacheCBInfo:g}=n(6686),{CacheContext:T}=n(2969),{CacheGetResult:S}=n(4494),{CacheLiveDataAction:m}=n(4253),{CacheRequest:f}=n(8847),{CacheRequestResult:I}=n(185),{CacheRequestType:R}=n(5011),{CacheReturnCode:C}=n(429),{CacheReturnSubcode:A}=n(7651),{CacheSessionProperties:O}=n(7330),{CacheSessionSubscribeInfo:N}=n(8460),{Destination:y,Topic:P}=n(9620),{CACHE_REQUEST_PREFIX:D}=T,b=()=>{};class M{constructor(e,t,n){M._validateProps(e);const s=new O(e.cacheName,e.maxAgeSec,e.maxMessages,e.timeoutMsec);Object.assign(this,{_outstandingRequests:{},_outstandingIDs:{},_disposed:!1,_nextMessageCallbackInfo:null,_nextSessionEventCallbackInfo:null,_properties:s,_session:t,_sessionIF:n}),this._connectToSession(t)}_connectToSession(e){this._nextSessionEventCallbackInfo=e.getEventCBInfo(),this._nextMessageCallbackInfo=e.getMessageCBInfo(),e.setMessageCBInfo(new r.MessageRxCBInfo(((e,t)=>{this._handleMessage(t)}),this)),e.setEventCBInfo(this._createCompoundEventCB(this._nextSessionEventCallbackInfo))}_createCompoundEventCB(e){return new r.SessionEventCBInfo(((t,n,s,r)=>{this._handleSessionEvent(e,t,n,s,r)}),null)}_handleSessionEvent(e,t,n){if(!this._processSessionEvent(t,n))return;const s=e.userObject;s?e.sessionEventCBFunction(t,n,s):e.sessionEventCBFunction(t,n)}_sendToNextDelegate(e){const t=this._nextMessageCallbackInfo.userObject;t?this._nextMessageCallbackInfo.messageRxCBFunction(this._session,e,t):this._nextMessageCallbackInfo.messageRxCBFunction(this._session,e)}_processSessionEvent(e,t){switch(t.sessionEventCode){case r.SessionEventCode.SUBSCRIPTION_ERROR:case r.SessionEventCode.SUBSCRIPTION_OK:return this._checkSubscriptionStatus(t);case r.SessionEventCode.DOWN_ERROR:return this.dispose(),!0;default:return t.sessionEventCode,!0}}_checkSubscriptionStatus(e){if(null===e.correlationKey||void 0===e.correlationKey||!(e.correlationKey instanceof N)||e.correlationKey.cacheSession!==this)return!0;const t=this._getOutstandingRequest(e.correlationKey.correlationID);return t?e.sessionEventCode===r.SessionEventCode.SUBSCRIPTION_OK?(this._handleSubscriptionSuccess(t,e.correlationKey.topic),!1):(this._handleSubscriptionError(t,e),!1):(p(`No request found for subscription success on ${e.correlationKey.topic}`),!0)}_handleSubscriptionSuccess(e){const t=e;t.subscriptionWaiting=null,this._startCacheRequest(t)}_handleSubscriptionError(e){this._terminateRequest(e,C.FAIL,A.SUBSCRIPTION_ERROR)}_checkRequestCompletion(e){if(e.childRequests.length)return void e.childRequests.length;if(e.subscriptionWaiting)return;if(null!==e.timeoutHandle&&!e.replyReceived)return;if(e.parentRequest){const t=e.parentRequest;return e.cancel(),this._unregisterRequest(e),void this._checkRequestCompletion(t)}let t,n;if(e.isSuspect)t=C.INCOMPLETE,n=A.SUSPECT_DATA;else if(e.dataReceived)t=C.OK,n=e.liveDataFulfilled?A.LIVE_DATA_FULFILL:A.REQUEST_COMPLETE;else{if(!e.replyReceived)throw new Error("Sanity: should never happen");t=C.INCOMPLETE,n=A.NO_DATA}this._terminateRequest(e,t,n)}_sendSeeOther(e,t){const n=t.clusterNameStream.getNext().getValue(),s=new f(this,R.GET_MSG_REQUEST,e.requestID,new g(b,null),e.liveDataAction,e.topic,n);e.addChild(s),this._registerRequest(s),s.startRequestTimeout(M._handleCacheRequestTimeout,this._properties.timeoutMsec),this._startCacheRequest(s,null,null,!0)}_sendGetNext(e,t){const n=new f(this,R.GET_NEXT_MSG_REQUEST,e.requestID,new g(b,null),e.liveDataAction,e.topic,e.cacheName);e.addChild(n),this._registerRequest(n),n.startRequestTimeout(M._handleCacheRequestTimeout,this._properties.timeoutMsec),this._startCacheRequest(n,t.sessionID,t.replyTo)}_handleMessage(e){const t=e.getCorrelationId(),n=null==t?null:this._outstandingRequests[t];if(!n)return void(this._relevantLiveData(e)&&this._sendToNextDelegate(e));n.clearRequestTimeout();const s=e.getSdtContainer(),r=s&&s.getValue();if(r||(h(`Invalid message format for cache response: no SDT container (${s}) or stream (${r})`),this._terminateRequest(n,C.FAIL,A.ERROR_RESPONSE)),this._incStat(o.RX_REPLY_MSG_RECVED),n.replyReceived=!0,n.getRootRequest().liveDataFulfilled)return this._incStat(o.CACHE_REQUEST_FULFILL_DISCARD_RESPONSE),void this._checkRequestCompletion(n);if(r)try{const t=new S;t.readFromStream(r),t.replyTo=e.getReplyTo(),t.responseString&&t.responseString,7!==t.responseCode&&"Invalid Session"!=t.responseString||(h(`Cluster response indicates invalid session: ${t.responseString} code: ${t.responseCode}`),this._terminateRequest(n,C.FAIL,A.INVALID_SESSION)),n.isSuspect=n.isSuspect||t.isSuspect;const s=M._decodeMessageStream(n,t);if(this._incStat(o.RX_CACHE_MSG,s.length),t.hasMore&&this._sendGetNext(n,t),t.clusterNameStream)for(;t.clusterNameStream.hasNext();)this._sendSeeOther(n,t);s&&s.forEach((e=>{this._sendToNextDelegate(e)})),this._checkRequestCompletion(n)}catch(e){h(`Invalid message format for cache response: ${e.stack}`),this._terminateRequest(n,C.FAIL,A.ERROR_RESPONSE)}else h("Invalid cache response did not fulfill request. Skipping response processing")}_relevantLiveData(e){return!e.getCorrelationId()||!e.getCorrelationId().startsWith(D)||this._nextMessageCallbackInfo.userObject instanceof M?Object.keys(this._outstandingRequests).every((t=>this._performLiveDataAction(this._outstandingRequests[t],e))):(p("DROP: Dropping CRQ reply due to no remaining Cache Session processors on message callback chain"),this._incStat(o.RX_REPLY_MSG_DISCARD),!1)}_performLiveDataAction(e,t){const n=e;switch(n.dataReceived=!0,n.liveDataAction){case m.QUEUE:return n.queuedLiveData.push(t),!1;case m.FULFILL:return n.liveDataFulfilled||this._fulfillRequest(n),!0;default:return!0}}_fulfillRequest(e){const t=e;t.liveDataFulfilled=!0,this._trackCompletionStats(C.OK,A.LIVE_DATA_FULFILL),setTimeout((()=>{M._notifyCallback(t,C.OK,A.LIVE_DATA_FULFILL,t.getTopic(),null)}),0)}dispose(){Object.keys(this._outstandingRequests).map((e=>this._outstandingRequests[e])).filter((e=>e instanceof f)).forEach((e=>{this._terminateRequest(e,C.INCOMPLETE,A.CACHE_SESSION_DISPOSED)})),this._outstandingRequests=[],this._session.setEventCBInfo(this._nextSessionEventCallbackInfo),this._session.setMessageCBInfo(this._nextMessageCallbackInfo),this._disposed=!0}getProperties(){return this._properties}sendCacheRequest(e,t,n,s,r){if(5!==arguments.length)throw new c(`sendCacheRequest() invoked with an illegal argument count of ${arguments.length}`);if("boolean"!=typeof n)throw new c("Invalid subscribe flag argument, should be a boolean but was "+typeof n);if("number"!=typeof e||Number.isNaN(e))throw new c("Invalid requestID",a.PARAMETER_INVALID_TYPE,null);if(this._outstandingIDs[e])throw new c("Request already in progress with this requestID");if(!(t instanceof y))throw new c("Invalid topic",a.PARAMETER_INVALID_TYPE,typeof t);if(t.validate(),s!==m.FLOW_THRU&&s!==m.FULFILL&&s!==m.QUEUE)throw new c("Invalid live data action",a.PARAMETER_OUT_OF_RANGE);if(t.isWildcarded()&&s!==m.FLOW_THRU)throw new c("Wildcarded topic not supported for this live data action",a.PARAMETER_CONFLICT);if(!(r instanceof g))throw new c("Callback info was not an instance of CacheCBInfo");if(this._disposed)return void M._notifyCallbackError(r,e,C.FAIL,A.CACHE_SESSION_DISPOSED,t,"Cache request failed: the cache session is disposed.");if(this._session._disposed)return void M._notifyCallbackError(r,e,C.FAIL,A.INVALID_SESSION,t,"Cache request failed: the session is disposed.");const i=new f(this,R.GET_MSG_REQUEST,e,r,s,t,this._properties.cacheName),o=Object.keys(this._outstandingRequests).filter((e=>this._outstandingRequests[e].topic.getName()===t.getName()));if(o.length){const e=s!==m.FLOW_THRU?o:o.filter((e=>this._outstandingRequests[e].liveDataAction!==m.FLOW_THRU));if(e.length){const t=this._outstandingRequests[e[0]];return p(`Existing request ${t} conflicts. Rejecting request ${i}`),this._registerRequest(i),void this._terminateRequest(i,C.FAIL,A.REQUEST_ALREADY_IN_PROGRESS)}}if(this._registerRequest(i),i.startRequestTimeout(M._handleCacheRequestTimeout,this._properties.timeoutMsec),n){const e=new N(i.correlationID,t,this);return i._subscriptionWaiting=e,void this._session.subscribe(t,!0,e)}this._startCacheRequest(i)}_handleCacheRequestFailed(e,t,n){this._terminateRequest(n.getRequestID(),C.FAIL,A.ERROR_RESPONSE)}_registerRequest(e){this._outstandingRequests[e.correlationID]=e,e.parentRequest||(this._outstandingIDs[e.requestID]=e)}_getOutstandingRequest(e){return this._outstandingRequests[e]}_startCacheRequest(e,t,n,r){const i=new s.Message;i.setCorrelationId(e.correlationID),n?i.setDestination(n):i.setDestination(P.createFromName(this._properties.cachePrefix+e.cacheName)),i.setReplyTo(P.createFromName(this._session.getSessionProperties().p2pInboxInUse)),i.setDeliverToOne(e.cacheMessageType===R.GET_MSG_REQUEST);const a=new E;a.addField(_.UINT32,e.cacheMessageType),a.addField(_.UINT32,f.VERSION),a.addField(_.STRING,e.topic.getName()),a.addField(_.UINT32,f.REPLY_SIZE_LIMIT),"number"==typeof t&&a.addField(_.UINT32,t),a.addField(_.UINT32,this._properties.maxMessages),a.addField(_.UINT32,this._properties.maxAgeSec),e.cacheMessageType===R.GET_MSG_REQUEST&&a.addField(_.BOOL,this._properties.includeOtherClusters&&!r),a.addField(_.BOOL,!1),e.cacheMessageType===R.GET_MSG_REQUEST&&a.addField(_.UINT32,Math.round(this._properties.timeoutMsec/1e3)),i.setSdtContainer(d.create(_.STREAM,a));try{this._session.send(i),e.parentRequest||this._incStat(o.CACHE_REQUEST_SENT)}catch(t){h(`Failed to send request: ${t.message}`),this._terminateRequest(e,C.FAIL,A.ERROR_RESPONSE,t)}}_incStat(e,t){this._session&&(this._sessionIF?this._sessionIF.incStat(e,t):h("Can't log stat: session statistics not available"))}_unregisterRequest(e){delete this._outstandingRequests[e.correlationID],delete this._outstandingIDs[e.requestID]}_trackCompletionStats(e,t){switch(e){case C.OK:this._incStat(o.CACHE_REQUEST_OK_RESPONSE),t===A.LIVE_DATA_FULFILL&&this._incStat(o.CACHE_REQUEST_LIVE_DATA_FULFILL);break;case C.INCOMPLETE:this._incStat(o.CACHE_REQUEST_INCOMPLETE_RESPONSE);break;case C.FAIL:this._incStat(o.CACHE_REQUEST_FAIL_RESPONSE);break;default:throw new Error("Sanity: no return code supplied")}}_terminateRequest(e,t,n,s){const r=e.getRootRequest();if(!this._outstandingRequests[r.correlationID])return;const i=r.cbInfo;if(!i)return void p(`No callback info provided for ${r}. Cannot notify`);if(!i.getCallback())return void p(`No callback provided for ${r}. Cannot notify`);const o=r.getTopic();o||p(`No topic provided for ${r}`),r.queuedLiveData.forEach((e=>this._sendToNextDelegate(e))),r.cancel(),this._unregisterRequest(r),r.liveDataFulfilled||(this._trackCompletionStats(t,n),M._notifyCallback(r,t,n,o,s))}static _decodeMessageStream(e,t){if(!t.messageStream)return[];const n=[],r=e;for(;t.messageStream.hasNext();){r.dataReceived=!0;const e=t.messageStream.getNext().getValue(),o=i.Codec.Decode.decodeCompoundMessage(e,0);if(!o)continue;const a=t.isSuspect?s.MessageCacheStatus.SUSPECT:s.MessageCacheStatus.CACHED;o._setCacheStatus(a),o._setCacheRequestID(r.requestID),n.push(o)}return n.length,n}static _handleCacheRequestTimeout(e){const t=e.cacheSession;t._getOutstandingRequest(e.correlationID)?(h(`Request ${e} timed out`),t._terminateRequest(e.getRootRequest(),C.INCOMPLETE,A.REQUEST_TIMEOUT)):h(`Timeout for ${e} was not unregistered. Ignoring`)}static _notifyCallback(e,t,n,s,r){const i=e.cbInfo;i.getCallback()(e.requestID,new I(t,n,s,r),i.getUserObject())}static _notifyCallbackError(e,t,n,s,r,i){e.getCallback()(t,new I(n,s,r,i),e.getUserObject())}static _validateProps(e){if("string"!=typeof e.cacheName)throw new c("Invalid parameter type for cacheName",a.PARAMETER_INVALID_TYPE);if(P.createFromName(e.cacheName).isWildcarded())throw new c(`Invalid cacheName '${e.cacheName}'. The cacheName cannot be wildcarded`,a.PARAMETER_OUT_OF_RANGE);if("number"!=typeof e.maxAgeSec)throw new c("Invalid parameter type for maxAgeSec",a.PARAMETER_INVALID_TYPE);if(e.maxAgeSec<0)throw new c("Invalid value for maxAgeSec; must be >= 0",a.PARAMETER_OUT_OF_RANGE);if("number"!=typeof e.maxMessages)throw new c("Invalid parameter type for maxMessages",a.PARAMETER_INVALID_TYPE);if(e.maxMessages<0)throw new c("Invalid value for maxMessages; must be >= 0",a.PARAMETER_OUT_OF_RANGE);if("number"!=typeof e.timeoutMsec)throw new c("Invalid parameter type for timeoutMsec",a.PARAMETER_INVALID_TYPE);if(e.timeoutMsec<3e3)throw new c("Invalid value for timeoutMsec; must be >= 3000",a.PARAMETER_OUT_OF_RANGE)}}e.exports.CacheSession=M},6686:e=>{e.exports.CacheCBInfo=class{constructor(e,t){this.cacheCBFunction=e,this.userObject=t}getCallback(){return this.cacheCBFunction}getUserObject(){return this.userObject}}},6706:(e,t,n)=>{const{ErrorResponseSubcodeMapper:s}=n(5424),{ErrorSubcode:r}=n(5129),{NotImplementedError:i}=n(5749),{OperationError:o}=n(5192),{RequestError:a}=n(8916),{RequestEventCode:c}=n(8309),{SolaceError:u}=n(3922);e.exports.ErrorResponseSubcodeMapper=s,e.exports.ErrorSubcode=r,e.exports.NotImplementedError=i,e.exports.OperationError=o,e.exports.RequestError=a,e.exports.RequestEventCode=c,e.exports.SolaceError=u},6811:(e,t,n)=>{const{MessageConsumerEventName:s}=n(6934);function r(e){return`MessageConsumerEventName.${s.describe(e)}`}e.exports={MessageDispatcher:class{constructor({emitter:e,autoAck:t,logger:n}={}){Object.assign(this,{emitter:e,queue:[],dispatch:!0,formatEventName:r,logger:n}),this._dispatchOne=t?this._dispatchOneAutoAck:this._dispatchOneBare,this.emitter.setOnFirstDirectListener(this._onFirstMessageListener.bind(this)),this._availableListener=!0}start(){this.dispatch=!0,this._flush()}stop(){this.dispatch=!1}get length(){return this.queue.length}push(e){const{LOG_TRACE:t}=this.logger;this.queue.push(e),this.dispatch?this._flush():e.getGuaranteedMessageId()}_onFirstMessageListener(){const{LOG_DEBUG:e}=this.logger;this._availableListener||(this.queue.length,this.dispatch,this._availableListener=!0),this._flush()}_flush(){const{LOG_DEBUG:e}=this.logger;for(;this.queue.length&&this.dispatch&&this.emitter.directListenerCount()>0;)this._dispatchOne(this.queue.shift());this.queue.length&&this.dispatch&&0===this.emitter.directListenerCount()&&this._availableListener&&(this._availableListener=!1)}_dispatchOneAutoAck(e){const{LOG_WARN:t}=this.logger;let n=null;if(n=this._dispatchOneBare(e),n)t(`Suppressing message acknowledgement for message ${e.getGuaranteedMessageId()} because client threw exception from listener`,n);else{if(e.isAcknowledged)return void t(`Consumer configured to auto-acknowledge messages, but message ${e.getGuaranteedMessageId()} was application acknowledged`);e._autoAcknowledge()}}_dispatchOneBare(e){const{LOG_WARN:t}=this.logger;let n;0===this.listenerCount&&t(`No listeners to dispatch message ${e.getGuaranteedMessageId()}`);try{this.emitter.emitDirect(e)}catch(t){n=this.emitter.formatErrorEvent(t,s.MESSAGE,e),this.emitter.emit("error",n)}return n}}}},6934:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.MessageConsumerEventName=s.new({UP:"MessageConsumerEventName_up",DOWN:"MessageConsumerEventName_down",ACTIVE:"MessageConsumerEventName_active",INACTIVE:"MessageConsumerEventName_inactive",DOWN_ERROR:"MessageConsumerEventName_downError",RECONNECTING:"MessageConsumerEventName_reconnecting",RECONNECTED:"MessageConsumerEventName_reconnected",CONNECT_FAILED_ERROR:"MessageConsumerEventName_connectFailedError",GM_DISABLED:"MessageConsumerEventName_GMDisabled",DISPOSED:"MessageConsumerEventName_disposed",MESSAGE:"MessageConsumerEventName_message",SUBSCRIPTION_OK:"MessageConsumerEventName_ok",SUBSCRIPTION_ERROR:"MessageConsumerEventName_error"})},6986:(e,t,n)=>{const{LogFormatter:s}=n(2694),{Baggage:r}=n(9486),{TraceContext:i}=n(5873),{TraceContextSetter:o}=n(7317);e.exports.MessageTracingSupport=class{constructor(){}getTraceContextSetter(){return null!=this._traceContextSetter&&null!=this._traceContextSetter||(this._traceContextSetter=new o),this._traceContextSetter}getTransportContext(){return this._transportContext}_setTransportContext(e){null!=e&&(this._transportContext=new i(e))}getCreationContext(){return this._creationContext}_setCreationContext(e){null!=e&&(this._creationContext=new i(e))}getBaggage(){return this._baggage}_setBaggage(e){this._baggage=e}}},7007:e=>{"use strict";var t,n="object"==typeof Reflect?Reflect:null,s=n&&"function"==typeof n.apply?n.apply:function(e,t,n){return Function.prototype.apply.call(e,t,n)};t=n&&"function"==typeof n.ownKeys?n.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var r=Number.isNaN||function(e){return e!=e};function i(){i.init.call(this)}e.exports=i,e.exports.once=function(e,t){return new Promise((function(n,s){function r(n){e.removeListener(t,i),s(n)}function i(){"function"==typeof e.removeListener&&e.removeListener("error",r),n([].slice.call(arguments))}E(e,t,i,{once:!0}),"error"!==t&&function(e,t){"function"==typeof e.on&&E(e,"error",t,{once:!0})}(e,r)}))},i.EventEmitter=i,i.prototype._events=void 0,i.prototype._eventsCount=0,i.prototype._maxListeners=void 0;var o=10;function a(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function c(e){return void 0===e._maxListeners?i.defaultMaxListeners:e._maxListeners}function u(e,t,n,s){var r,i,o,u;if(a(n),void 0===(i=e._events)?(i=e._events=Object.create(null),e._eventsCount=0):(void 0!==i.newListener&&(e.emit("newListener",t,n.listener?n.listener:n),i=e._events),o=i[t]),void 0===o)o=i[t]=n,++e._eventsCount;else if("function"==typeof o?o=i[t]=s?[n,o]:[o,n]:s?o.unshift(n):o.push(n),(r=c(e))>0&&o.length>r&&!o.warned){o.warned=!0;var l=new Error("Possible EventEmitter memory leak detected. "+o.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");l.name="MaxListenersExceededWarning",l.emitter=e,l.type=t,l.count=o.length,u=l,console&&console.warn&&console.warn(u)}return e}function l(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function h(e,t,n){var s={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},r=l.bind(s);return r.listener=n,s.wrapFn=r,r}function p(e,t,n){var s=e._events;if(void 0===s)return[];var r=s[t];return void 0===r?[]:"function"==typeof r?n?[r.listener||r]:[r]:n?function(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}(r):_(r,r.length)}function d(e){var t=this._events;if(void 0!==t){var n=t[e];if("function"==typeof n)return 1;if(void 0!==n)return n.length}return 0}function _(e,t){for(var n=new Array(t),s=0;s<t;++s)n[s]=e[s];return n}function E(e,t,n,s){if("function"==typeof e.on)s.once?e.once(t,n):e.on(t,n);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function r(i){s.once&&e.removeEventListener(t,r),n(i)}))}}Object.defineProperty(i,"defaultMaxListeners",{enumerable:!0,get:function(){return o},set:function(e){if("number"!=typeof e||e<0||r(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");o=e}}),i.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},i.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||r(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},i.prototype.getMaxListeners=function(){return c(this)},i.prototype.emit=function(e){for(var t=[],n=1;n<arguments.length;n++)t.push(arguments[n]);var r="error"===e,i=this._events;if(void 0!==i)r=r&&void 0===i.error;else if(!r)return!1;if(r){var o;if(t.length>0&&(o=t[0]),o instanceof Error)throw o;var a=new Error("Unhandled error."+(o?" ("+o.message+")":""));throw a.context=o,a}var c=i[e];if(void 0===c)return!1;if("function"==typeof c)s(c,this,t);else{var u=c.length,l=_(c,u);for(n=0;n<u;++n)s(l[n],this,t)}return!0},i.prototype.addListener=function(e,t){return u(this,e,t,!1)},i.prototype.on=i.prototype.addListener,i.prototype.prependListener=function(e,t){return u(this,e,t,!0)},i.prototype.once=function(e,t){return a(t),this.on(e,h(this,e,t)),this},i.prototype.prependOnceListener=function(e,t){return a(t),this.prependListener(e,h(this,e,t)),this},i.prototype.removeListener=function(e,t){var n,s,r,i,o;if(a(t),void 0===(s=this._events))return this;if(void 0===(n=s[e]))return this;if(n===t||n.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete s[e],s.removeListener&&this.emit("removeListener",e,n.listener||t));else if("function"!=typeof n){for(r=-1,i=n.length-1;i>=0;i--)if(n[i]===t||n[i].listener===t){o=n[i].listener,r=i;break}if(r<0)return this;0===r?n.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(n,r),1===n.length&&(s[e]=n[0]),void 0!==s.removeListener&&this.emit("removeListener",e,o||t)}return this},i.prototype.off=i.prototype.removeListener,i.prototype.removeAllListeners=function(e){var t,n,s;if(void 0===(n=this._events))return this;if(void 0===n.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete n[e]),this;if(0===arguments.length){var r,i=Object.keys(n);for(s=0;s<i.length;++s)"removeListener"!==(r=i[s])&&this.removeAllListeners(r);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=n[e]))this.removeListener(e,t);else if(void 0!==t)for(s=t.length-1;s>=0;s--)this.removeListener(e,t[s]);return this},i.prototype.listeners=function(e){return p(this,e,!0)},i.prototype.rawListeners=function(e){return p(this,e,!1)},i.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):d.call(e,t)},i.prototype.listenerCount=d,i.prototype.eventNames=function(){return this._eventsCount>0?t(this._events):[]}},7105:e=>{e.exports.OutstandingDataRequest=class{constructor(e,t,n,s,r){this.correlationId=e,this.timer=t,this.replyReceivedCBFunction=n,this.reqFailedCBFunction=s,this.userObject=r}}},7113:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.MutableSessionProperty=s.new({CLIENT_NAME:1,CLIENT_DESCRIPTION:2})},7250:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.SMFAdProtocolMessageType=s.new({OPENPUBFLOW:0,CLIENTACK:3,BIND:4,UNBIND:5,UNSUBSCRIBE:6,CLOSEPUBFLOW:7,CREATE:8,DELETE:9,TRANSACTIONCTRL:11,FLOWCHANGEUPDATE:12,XACTRL:14,CLIENTNACK:15})},7317:(e,t,n)=>{const{Parameter:s}=n(802),{Convert:r}=n(9783),i=n(8287).hp,{isNumber:o,isBoolean:a,isString:c,isStringOrNothing:u}=s,{uint8ArrayToString:l}=r;class h{constructor(){this._traceId=null,this._spanId=null,this._isSampled=!1,this._traceState=null,this._version=1}clone(){const e=new h;return e._setSpanId(this._spanId),e._setTraceId(this._traceId),e._setSampled(this._isSampled),e._setTraceState(this._traceState),e._setVersion(this._version),e}static get TRACE_ID_BYTES_LENGTH(){return 16}static get SPAN_ID_BYTES_LENGTH(){return 8}get version(){return this._version||1}setVersion(e){this._setVersion(o("version",e))}_setVersion(e){this._version=e}get traceId(){return this._traceId}setTraceId(e){this._setTraceId(c("traceId",e))}_setTraceId(e){this._traceId=e}get spanId(){return this._spanId}setSpanId(e){this._setSpanId(c("spanId",e))}_setSpanId(e){this._spanId=e}get isSampled(){return this._isSampled||!1}setSampled(e){this._setSampled(a("isSampled",e))}_setSampled(e){this._isSampled=e}get traceState(){return this._traceState}setTraceState(e){this._setTraceState(u("traceState",e))}_setTraceState(e){this._traceState=e}static fromTraceContext(e){if(null==e)return null;let t=null;if(i.isBuffer(e)?t=e:"string"==typeof e&&(t=i.from(e,"latin1")),!t||t.length<32)return null;try{const e=new Uint8Array(t).buffer;let n=0;const s=new h,r=e.slice(n,n+1);let i=new DataView(r,0,1).getUint8(n);const o=i>>4;s.setVersion(o);const a=4==(15&i);s.setSampled(a),n++;const c=e.slice(n,n+16),u=l(c,"hex");s.setTraceId(u),n+=h.TRACE_ID_BYTES_LENGTH;const p=e.slice(n,n+8),d=l(p,"hex");s.setSpanId(d),n+=h.SPAN_ID_BYTES_LENGTH,n++,n+=4;const _=e.slice(n,n+2),E=new DataView(_,0,_.byteLength).getUint16(0,!1);if(n+=2,E>0){const t=e.slice(n,n+E),r=l(t);s.setTraceState(r)}return s}catch(e){return null}}}e.exports.TraceContextSetter=h},7330:e=>{e.exports.CacheSessionProperties=class{constructor(e,t,n,s){this.cacheName=e,this.maxAgeSec=t||0,this.maxMessages=null==n?1:n,this.timeoutMsec=s||1e4,this.includeOtherClusters=!0,this.cachePrefix="#P2P/CACHEINST/"}getCacheName(){return this.cacheName}setCacheName(e){this.cacheName=e}getMaxMessageAgeSec(){return this.maxAgeSec}setMaxMessageAgeSec(e){this.maxAgeSec=e}getMaxMessages(){return this.maxMessages}setMaxMessages(e){this.maxMessages=e}getTimeoutMsec(){return this.timeoutMsec}setTimeoutMsec(e){this.timeoutMsec=e}}},7339:(e,t,n)=>{const{EntryPoint:s}=n(5921),{ExitPoint:r}=n(5027),{StateContext:i}=n(1493);e.exports.State=class extends i{constructor(e,t=null){super(e);const n=e.parentContext;Object.assign(this.impl,{parentContext:n,reactions:{},entryPoints:{},exitPoints:{},ancestorList:[...n.getAncestorList(),this],handleUnhandledEvent:e=>n.handleEvent?n.handleEvent(e):n.impl.handleUnhandledEvent(e)}),n&&(this.log=n.log.bind(this)),Object.keys(t||{}).forEach((e=>{const n=t[e];this[e]="function"==typeof n?n.bind(this):n})),this.setLogPadding(" ".repeat(this.impl.ancestorList.length))}reaction(e,t){if(!e)throw new Error("No event name for reaction");if(!t)throw new Error(`No reaction function for reaction ${e}`);return this.log(`Adding reaction to ${this} for event ${e}`),this.impl.reactions[e]&&this.log(`Replacing reaction ${this.impl.reactions[e]} with ${t}`),this.impl.reactions[e]=t.bind(this),this}entryPoint(e,t){if(!e)throw new Error("No entry point name for entry point");if(!t)throw new Error(`No reaction function for entry point ${e}`);return this.log(`Adding entryPoint ${e} to ${this}`),this.impl.entryPoints[e]?(this.log(`EntryPoint ${e} already exists in ${this}`),this):(this.impl.entryPoints[e]=new s({state:this,entryPointName:e,func:t}),this)}exitPoint(e,t){if(!e)throw new Error("No exit point name for entry point");if(!t)throw new Error(`No reaction function for exit point ${e}`);return this.log(`Adding exitPoint ${e} to ${this}`),this.impl.exitPoints[e]?(this.log(`ExitPoint ${e} already exists in  ${this}`),this):(this.impl.exitPoints[e]=new r({state:this,exitPointName:e,func:t}),this)}getEntryPointDestState(e){return void 0===this.impl.entryPoints[e]?(this.log(`${this}: EntryPoint ${e} does not exist.`),this):this.impl.entryPoints[e].getDestState()}getExitPointDestState(e){return void 0===this.impl.exitPoints[e]?(this.log(`${this}: ExitPoint ${e} does not exist.`),this):this.impl.exitPoints[e].getDestState()}entry(e){return this.impl.appEntryFunc&&this.log(`Replacing entry function ${this.impl.appEntryFunc} with ${e}`),this.impl.appEntryFunc=e.bind(this),this}exit(e){return this.impl.appExitFunc&&this.log(`Replacing exit function ${this.impl.appExitFunc} with ${e}`),this.impl.appExitFunc=e.bind(this),this}externalTransitionTo(e,t){return new i.ReactionResult({caller:this,destState:e,action:t,external:!0})}transitionToEntryPoint(e,t,n){return new i.ReactionResult({caller:this,destState:e.getEntryPointDestState(t),action:n})}transitionToExitPoint(e,t,n){return new i.ReactionResult({caller:this,destState:e.getExitPointDestState(t),action:n})}eventUnhandled(){return new i.ReactionResult({caller:this})}internalTransition(e){return new i.ReactionResult({caller:this,destState:this.getStateMachine().getCurrentState(),action:e})}terminate(e){return new i.ReactionResult({caller:this,destState:this.getStateMachine().getFinalState(),action:e})}getParent(){return this.impl.parentContext}onEntry(){this.log(`Entering: ${this}`),this.impl.appEntryFunc&&this.impl.appEntryFunc()}onExit(){this.log(`Exiting: ${this}`),this.impl.appExitFunc&&this.impl.appExitFunc()}handleEvent(e){this.log(`Process: ${e}`);const t=this.impl.reactions[e.getName()];if(t){const n=t(e);if(n||this.log(`Reaction returned undefined: ${e} in ${this}`),n.destState)return this.log(`Handled: ${e}`),n;this.log(`Unhandled: ${e} in ${this}`)}else this.log(`No reaction: ${e} in ${this}`);return this.impl.handleUnhandledEvent(e)}}},7366:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.MessageCacheStatus=s.new({LIVE:0,CACHED:1,SUSPECT:2})},7368:(e,t,n)=>{var s=n(2195);const{ErrorSubcode:r}=n(6706),{Hex:i}=n(9783),{TransportSessionEventCode:o}=n(3427),{formatHexString:a}=i;e.exports.TransportSessionEvent=class{constructor(e,t,n,s,r){this._transportEventCode=e,this._infoStr=t,this._responseCode=n,this._errorSubcode=s,this._sid=r}getTransportEventCode(){return this._transportEventCode}get transportEventCode(){return this._transportEventCode}getInfoStr(){return this.infoStr}get infoStr(){return this._infoStr}getResponseCode(){return this.responseCode}get responseCode(){return this._responseCode}getSubcode(){return this.errorSubcode}get errorSubcode(){return this._errorSubcode}getSessionId(){return this.sessionId}get sessionId(){return this._sid}inspect(){return{transportEventCode:o.describe(this.transportEventCode),infoStr:this.infoStr,responseCode:this.responseCode,errorSubcode:r.describe(this.errorSubcode),sid:this.sid&&a(this.sid)||"N/A"}}toString(){return s(this)}}},7385:(e,t,n)=>{const s=n(4386),{Convert:r}=n(9783),{SDTFieldType:i}=n(7849),{validateSdtField:o}=n(530),{anythingToBuffer:a}=r,{ProfileBinding:c}=s;class u{constructor(e=i.NULLTYPE,t=null){const n=o(e,t);if(null!==n)throw n;this._type=e,e===i.BYTEARRAY?this._value=a(t):this._value=t,this._error=void 0}getType(){return this._type}getValue(){if(void 0!==this._error)throw this._error;return this.getValueNoThrow()}getValueNoThrow(){return void 0!==this._error?this._error:this._type===i.BYTEARRAY&&c.value.byteArrayAsString?this._value.toString("latin1"):this._value}setError(e){this._error=e}toString(){return`[SDTField type:${this._type} value:${this._value}]`}static create(e,t){return new u(e,t)}}e.exports.SDTField=u},7405:(e,t,n)=>{const{assert:s}=n(7444),{hostListDNSFilter:r}=n(4838),{LogFormatter:i}=n(2694),{parseURL:o}=n(968);function a(e){return Array.isArray(e)?e.map((e=>o(e))):a(e.split(/[,;]/))}class c{constructor(e={url:null,waitTime:0}){Object.assign(this,e)}}e.exports.HostList=class{constructor({url:e,connectRetries:t,reconnectRetries:n,connectRetriesPerHost:r,reconnectRetryWaitInMsecs:o}={}){Object.assign(this,{hosts:a(e).map((e=>e.href)),connectTryCount:-1===t?Number.POSITIVE_INFINITY:t+1,reconnectTryCount:-1===n?Number.POSITIVE_INFINITY:n,connectTryCountPerHost:-1===r?Number.POSITIVE_INFINITY:r+1,reconnectRetryWaitInMsecs:o,_mutableState:{},logger:new i("[host-list]")}),this.reset(),s(this.hosts.length>=1),s(this.connectTryCount>=1),s(this.reconnectTryCount>=0),s(this.connectTryCountPerHost>=1)}resolveHosts(e){const{LOG_TRACE:t,LOG_WARN:n}=this.logger;r(this.hosts,((t,r)=>{if(t)return e(t);s(r.length===this.hosts.length,"Resolve did not return a result for all hosts");let i=0;return r.forEach((e=>{e.address&&++i,e.resolved&&(e.address?(e.address,e.url):n("DNS resolve FAILED:",e.error.code,`${e.error.syscall}('${e.error.hostname}')`,"for",e.url))})),e(0===i?"All hosts failed DNS resolution":null)}))}reset(e={wasConnected:!1,disconnected:!1}){Object.assign(this._mutableState,{wasConnected:e.wasConnected,disconnected:e.disconnected,hostPointer:0,hostTries:0,listTries:1,exhausted:!1,lastHostInfo:new c})}getNextHost(){const{LOG_TRACE:e}=this.logger,t=this._mutableState,n=t.wasConnected,r=t.lastHostInfo;s(r,"Next host request with no prior host info -- did you call reset()?");try{if(t.disconnected)return null;s(!t.exhausted,"Next host request after host list exhausted");const e=Object.assign({hosts:this.hosts,hostTriesMax:this.connectTryCountPerHost,listTriesMax:n?this.reconnectTryCount:this.connectTryCount});if(++t.hostTries,t.hostTries>e.hostTriesMax?(t.hostTries,r.url,++t.hostPointer,t.hostPointer>=e.hosts.length?(++t.listTries,t.listTries>e.listTriesMax?(e.listTriesMax,t.exhausted=!0):(t.listTries,e.listTriesMax,t.hostPointer=0,t.hostTries=1)):t.hostTries=1):(t.hostTries,e.hostTriesMax),t.exhausted)return null;const i=e.hosts[t.hostPointer];s(i,`No host at the host pointer! ${e.hosts}[${t.hostPointer}]`);const o=null===r.url,a=r.url!==i,u=r.url!==i&&0===t.hostPointer,l=o||a&&!u?0:this.reconnectRetryWaitInMsecs,h=new c({url:i,waitTime:l});return t.lastHostInfo=h,h.url}finally{}}get connectWaitTimeInMsecs(){return s(this._mutableState.lastHostInfo.url,"Getting connectWaitTimeInMsecs having never called getNextHostInfo"),this._mutableState.lastHostInfo.waitTime}currentHostToString(){const e=this._mutableState,t=e.wasConnected,n=Object.assign({hosts:this.hosts,hostTriesMax:this.connectTryCountPerHost,listTriesMax:t?this.reconnectTryCount:this.connectTryCount}),s=e.hostPointer+1;return`host '${e.lastHostInfo.url}' (host ${s} of ${n.hosts.length})(host connection attempt ${e.hostTries} of ${n.hostTriesMax})(total ${t?"reconnection":"connection"} attempt ${e.listTries} of ${n.listTriesMax})`}}},7414:(e,t,n)=>{const{FsmEvent:s}=n(7818),{State:r}=n(7339),{StateMachine:i}=n(3671);e.exports.FsmEvent=s,e.exports.State=r,e.exports.StateMachine=i},7434:(e,t,n)=>{const{BaseChecks:s}=n(2843);function r(e){return{then:e?e=>e():(e,t)=>t()}}const i=(()=>{const e=Object.assign({},s);return e.when=e=>r(e),e.unless=e=>r(!e),e})();e.exports.Check=i},7440:(e,t,n)=>{const{Base64:s,Convert:r}=n(9783),{XHRFactory:i}=n(3124),{encode:o}=s,{stringToUint8Array:a}=r;function c(e,t,n){e.responseType="arraybuffer",e.overrideMimeType(`${n}; charset=x-user-defined`),e.setRequestHeader("Content-Type",`${n}; charset=x-user-defined`),e.send(a(t))}function u(e,t,n){e.overrideMimeType(`${n}; charset=x-user-defined`),e.setRequestHeader("Content-Type",`${n}; charset=x-user-defined`),e.send(a(t).buffer)}function l(e,t,n,s){e.setRequestHeader("Content-Type",`${n}; charset=x-user-defined`),e.send(null==t?t:o(t),s)}const h="undefined"!=typeof window&&window.Uint8Array&&window.Blob?i.create(!0).responseType?c:u:l;e.exports.sendXhrBinary=h,e.exports.sendXhrText=l},7444:(e,t,n)=>{const{ArrayOperations:s}=n(6284),{assert:r}=n(5796),{BidiMap:i}=n(5447),{Enum:o}=n(8963),{Iterator:a}=n(112),{Lazy:c}=n(2128),{makeMap:u}=n(2515),{Mixin:l}=n(825),{Resolver:h}=n(4396),{SetOperations:p}=n(7999);e.exports.assert=r,e.exports.ArrayOperations=s,e.exports.BidiMap=i,e.exports.Enum=o,e.exports.Iterator=a,e.exports.Lazy=c,e.exports.makeIterator=a.makeIterator,e.exports.makeMap=u,e.exports.Mixin=l,e.exports.mixin=l.mixin,e.exports.Resolver=h,e.exports.resolve=h.resolve,e.exports.SetOperations=p},7449:(e,t,n)=>{const{ErrorSubcode:s,OperationError:r}=n(6706),{SDTField:i}=n(7385);e.exports.SDTMapContainer=class{constructor(){this._map=[]}getKeys(){return Object.keys(this._map)}getField(e){return this._map[e]}deleteField(e){delete this._map[e]}addField(e,t,n=void 0){if(t instanceof i)this._map[e]=t;else{if(void 0===n)throw new r("Invalid parameters to addField: expected SDTField, or type and value",s.PARAMETER_CONFLICT);this._map[e]=i.create(t,n)}}}},7526:(e,t)=>{"use strict";t.byteLength=function(e){var t=a(e),n=t[0],s=t[1];return 3*(n+s)/4-s},t.toByteArray=function(e){var t,n,i=a(e),o=i[0],c=i[1],u=new r(function(e,t,n){return 3*(t+n)/4-n}(0,o,c)),l=0,h=c>0?o-4:o;for(n=0;n<h;n+=4)t=s[e.charCodeAt(n)]<<18|s[e.charCodeAt(n+1)]<<12|s[e.charCodeAt(n+2)]<<6|s[e.charCodeAt(n+3)],u[l++]=t>>16&255,u[l++]=t>>8&255,u[l++]=255&t;return 2===c&&(t=s[e.charCodeAt(n)]<<2|s[e.charCodeAt(n+1)]>>4,u[l++]=255&t),1===c&&(t=s[e.charCodeAt(n)]<<10|s[e.charCodeAt(n+1)]<<4|s[e.charCodeAt(n+2)]>>2,u[l++]=t>>8&255,u[l++]=255&t),u},t.fromByteArray=function(e){for(var t,s=e.length,r=s%3,i=[],o=16383,a=0,u=s-r;a<u;a+=o)i.push(c(e,a,a+o>u?u:a+o));return 1===r?(t=e[s-1],i.push(n[t>>2]+n[t<<4&63]+"==")):2===r&&(t=(e[s-2]<<8)+e[s-1],i.push(n[t>>10]+n[t>>4&63]+n[t<<2&63]+"=")),i.join("")};for(var n=[],s=[],r="undefined"!=typeof Uint8Array?Uint8Array:Array,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0;o<64;++o)n[o]=i[o],s[i.charCodeAt(o)]=o;function a(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=e.indexOf("=");return-1===n&&(n=t),[n,n===t?0:4-n%4]}function c(e,t,s){for(var r,i,o=[],a=t;a<s;a+=3)r=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),o.push(n[(i=r)>>18&63]+n[i>>12&63]+n[i>>6&63]+n[63&i]);return o.join("")}s["-".charCodeAt(0)]=62,s["_".charCodeAt(0)]=63},7579:e=>{const t={int48ToStr:function(e){let t=e;const n=[];for(let e=0;e<6;e++){const e=t%256;t=Math.floor(t/256),n.push(String.fromCharCode(e))}return n.reverse(),n.join("")}};e.exports.EncodeInteger=t},7603:e=>{e.exports.P2PUtil={getP2PInboxTopic:e=>`${e}/_`,getP2PTopicSubscription:e=>`${e}/>`}},7623:(e,t,n)=>{const{ContentSummaryType:s}=n(8283),{Decode:r}=n(1261),{Encode:i}=n(4741),{ParamParse:o}=n(760),{ParseSMF:a}=n(8103),{Transport:c}=n(2318);e.exports.ContentSummaryType=s,e.exports.Encode=i,e.exports.Decode=r,e.exports.ParamParse=o,e.exports.ParseSMF=a,e.exports.Transport=c},7625:()=>{},7651:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.CacheReturnSubcode=s.new({REQUEST_COMPLETE:0,LIVE_DATA_FULFILL:1,ERROR_RESPONSE:2,INVALID_SESSION:3,REQUEST_TIMEOUT:4,REQUEST_ALREADY_IN_PROGRESS:5,NO_DATA:6,SUSPECT_DATA:7,CACHE_SESSION_DISPOSED:8,SUBSCRIPTION_ERROR:9})},7671:(e,t,n)=>{const{Enum:s,assert:r}=n(7444),{MessageOutcome:i}=n(6247),{LOG_DEBUG:o,LOG_ERROR:a}=n(2694),c=s.new({UNACKED:"UNACKED",ACKED_NOT_SENT:"ACKED_NOT_SENT",ACKED_SENT:"ACKED_SENT"});class u{constructor(e,t,n){e?(this.exists=!0,this.id=e,this.key=e.toString(),this.state=t||c.UNACKED,this.settlementOutcome=n||i.ACCEPTED):this.exists=!1}set(e,t,n=void 0){this.exists=!0,this.id=e,this.key=e.toString(),this.state=t||c.UNACKED,this.settlementOutcome=void 0!==n?n:null}clear(){this.exists=!1,this.id=null,this.key=null,this.state=null,this.settlementOutcome=null}}Object.assign(e.exports,{ApplicationAckState:c,ApplicationAck:u,ApplicationAckRingBuffer:class{constructor(e){r(e>=2),this._size=e,this._insertIndex=0,this._buffer=Array(e).fill(null).map((()=>new u)),this._index=new Map}reset(){this._insertIndex=0,this._buffer.forEach((e=>{e.exists=!1})),this._index.clear()}insert(e,t){r(t),r(e);const n=this._size,s=this._buffer,i=this._index,o=this._insertIndex;r(!s[o].exists,"Invariant not enforced (before): insert index not empty");const u=s[o];u.set(e,c.UNACKED),i.has(u.key)&&a(`Duplicate ID: ${i.get(u.key)} insertIndex: ${o}`),i.set(u.key,o);const l=s[(o+1)%n];let h;try{h=t(l.exists?l:null)}finally{this._insertIndex=(o+1)%n,l.exists&&(i.delete(l.key),l.clear())}return r(!s[this._insertIndex].exists,"Invariant not enforced (after): insert index not empty"),h}get length(){return this._index.size}front(){if(0===this.length)return null;const e=this._buffer,t=this._insertIndex,n=this._size,s=(t+1)%n;if(e[s].exists)return e[s];for(let t=s,r=s+n-1;t<=r;++t){const s=e[t%n];if(s.exists)return s}return r(0===this._index.size,"#front() failed so buffer must be empty"),null}forEach(e){if(0===this.length)return;const t=this._buffer,n=this._size;let s=0;for(let r=this._insertIndex+1,i=this._insertIndex+n;r<=i;++r){const i=t[r%n];i.exists&&e(i,s++,this)}r(s>0,"Not empty but did not dispatch")}updateAckState(e,t,n=void 0){const s=e.toString();r(this._index.has(s),"Ack key not found");const o=this._buffer[this._index.get(s)];r(o,"Ack key has no entry"),o.state=t,null!=n?o.settlementOutcome=n:t===c.ACKED_NOT_SENT&&(o.settlementOutcome=i.ACCEPTED)}has(e){const t=e.toString();return this._index.has(t)}}})},7750:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.SMFParameterType=s.new({PADDING:0,PUBLISHER_ID:1,PUBLISHER_MSGID:2,MESSAGEPRIORITY:3,USERDATA:4,USERNAME:6,PASSWORD:7,RESPONSE:8,SUB_ID_LIST:10,GENERIC_ATTACHMENT:11,BINARY_ATTACHMENT:12,DELIVERY_MODE:16,ASSURED_MESSAGE_ID:17,ASSURED_PREVMESSAGE_ID:18,ASSURED_REDELIVERED_FLAG:19,MESSAGE_CONTENT_SUMMARY:22,ASSURED_FLOWID:23,TR_TOPICNAME:24,AD_FLOWREDELIVERED_FLAG:25,AD_TIMETOLIVE:28,AD_TOPICSEQUENCE_NUMBER:30,EXTENDED_TYPE_STREAM:31,AD_ACK_MESSAGE_ID:41,AD_SPOOLER_UNIQUE_ID:44,AD_REPL_MATE_ACK_MSGID:45,AD_REDELIVERY_COUNT:46,LIGHT_CORRELATION:0,LIGHT_TOPIC_NAME_OFFSET:1,LIGHT_QUEUE_NAME_OFFSET:2,LIGHT_ACK_IMMEDIATELY:3}),e.exports.SMFExtendedParameterType=s.new({OAUTH2_ISSUER_IDENTIFIER:47,OIDC_ID_TOKEN:48,OAUTH2_ACCESS_TOKEN:49,PARTITION_KEY_HASH:53,TS_TRANSPORT_CONTEXT:54})},7753:(e,t,n)=>{e.exports.Long=n(5017)},7818:(e,t,n)=>{const{FsmObject:s}=n(6031);e.exports.FsmEvent=class extends s{}},7847:(e,t,n)=>{const s=n(4386),{SolclientFactory:r,SolclientFactoryProfiles:i,SolclientFactoryProperties:o}=s,{Long:a}=n(9783),{Destination:c,DestinationType:u,Topic:l}=n(9620),{ErrorSubcode:h,NotImplementedError:p,OperationError:d,RequestError:_,RequestEventCode:E}=n(6706),{makeIterator:g}=n(7444),{ConsoleLogImpl:T,LogImpl:S,LogLevel:m}=n(2694),{Message:f,MessageCacheStatus:I,MessageDeliveryModeType:R,MessageDumpFlag:C,MessageOutcome:A,MessageType:O,MessageUserCosType:N,ReplicationGroupMessageId:y}=n(6247),{MessageConsumer:P,MessageConsumerAcknowledgeMode:D,MessageConsumerEventName:b,MessageConsumerProperties:M,QueueBrowser:v,QueueBrowserEventName:w,QueueBrowserProperties:L}=n(178),{ReplayStartLocation:U,ReplayStartLocationBeginning:F}=n(9309),{MessagePublisherAcknowledgeMode:x,MessagePublisherProperties:B}=n(5898),{Baggage:G,TraceContext:k,TraceContextSetter:W}=n(2288),{AbstractQueueDescriptor:$,QueueAccessType:q,QueueDescriptor:V,QueueDiscardBehavior:H,QueuePermissions:Y,QueueProperties:Q,QueueType:X,EndpointNameComplaint:K}=n(9631),{SDTField:j,SDTFieldType:z,SDTMapContainer:Z,SDTStreamContainer:J,SDTUnsupportedValueError:ee,SDTValueErrorSubcode:te}=n(769),{AuthenticationScheme:ne,CapabilityType:se,MessageRxCBInfo:re,MutableSessionProperty:ie,Session:oe,SessionEvent:ae,SessionEventCBInfo:ce,SessionEventCode:ue,SessionProperties:le,SessionState:he,SslDowngrade:pe}=n(5024),{CacheCBInfo:de,CacheLiveDataAction:_e,CacheRequestResult:Ee,CacheReturnCode:ge,CacheReturnSubcode:Te,CacheSession:Se,CacheSessionProperties:me}=n(2689),{StatType:fe}=n(5747),{TransportError:Ie,TransportProtocol:Re}=n(8205),{Version:Ce}=n(968),Ae={AbstractQueueDescriptor:$,AuthenticationScheme:ne,Baggage:G,CacheCBInfo:de,CacheLiveDataAction:_e,CacheRequestResult:Ee,CacheReturnCode:ge,CacheReturnSubcode:Te,CacheSession:Se,CacheSessionProperties:me,CapabilityType:se,ConsoleLogImpl:T,Destination:c,DestinationType:u,ErrorSubcode:h,LogImpl:S,LogLevel:m,Long:a,Message:f,MessageCacheStatus:I,MessageConsumer:P,MessageConsumerAcknowledgeMode:D,MessageConsumerEventName:b,MessageConsumerProperties:M,MessageDeliveryModeType:R,MessageDumpFlag:C,MessageOutcome:A,MessagePublisherAcknowledgeMode:x,MessagePublisherProperties:B,MessageRxCBInfo:re,MessageType:O,MessageUserCosType:N,MutableSessionProperty:ie,NotImplementedError:p,OperationError:d,QueueAccessType:q,QueueBrowser:v,QueueBrowserEventName:w,QueueBrowserProperties:L,QueueDescriptor:V,QueueDiscardBehavior:H,QueuePermissions:Y,QueueProperties:Q,QueueType:X,EndpointNameComplaint:K,ReplayStartLocation:U,ReplayStartLocationBeginning:F,ReplicationGroupMessageId:y,RequestError:_,RequestEventCode:E,SDTField:j,SDTFieldType:z,SDTMapContainer:Z,SDTStreamContainer:J,SDTUnsupportedValueError:ee,SDTValueErrorSubcode:te,Session:oe,SessionEvent:ae,SessionEventCBInfo:ce,SessionEventCode:ue,SessionProperties:le,SessionState:he,SolclientFactory:r,SolclientFactoryProfiles:i,SolclientFactoryProperties:o,SslDowngrade:pe,StatType:fe,Topic:l,TraceContext:k,TraceContextSetter:W,TransportError:Ie,TransportProtocol:Re,Version:Ce,makeIterator:g,_internal:n(1081)};Object.assign(e.exports,Ae)},7849:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.SDTFieldType=s.new({BOOL:0,UINT8:1,INT8:2,UINT16:3,INT16:4,UINT32:5,INT32:6,UINT64:7,INT64:8,WCHAR:9,STRING:10,BYTEARRAY:11,FLOATTYPE:12,DOUBLETYPE:13,MAP:14,STREAM:15,DESTINATION:16,NULLTYPE:17,UNKNOWN:18,SMF_MESSAGE:19})},7909:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.CacheGetResultCode=s.new({INVALID:0,OK:1})},7999:e=>{function t(e,t){return t.forEach((t=>{e.add(t)})),e}e.exports={SetOperations:{inplaceDifference:function(e,t){return t.forEach((t=>{e.delete(t)})),e},inplaceIntersection:function(e,t){return e.forEach((n=>{t.has(n)||e.delete(n)})),e},inplaceUnion:t,isSuperset:function(e,t){return Array.from(t).every((t=>e.has(t)))},difference:function(e,t){return new Set(Array.from(e).filter((e=>!t.has(e))))},intersection:function(e,t){return new Set(Array.from(e).filter((e=>t.has(e))))},union:function(e,n){return t(new Set(e),n)}}}},8076:(e,t,n)=>{const s=n(5017),{ErrorSubcode:r,OperationError:i}=n(6706),o=n(8287).hp,a=String.fromCharCode(0,0),c=String.fromCharCode(0,0,0),u=String.fromCharCode(0,0,0,0),l=8192,h=32768;function p(e){const t=e.length,n=new ArrayBuffer(t),s=new Uint8Array(n,0,t);for(let n=0;n<t;n++)s[n]=e.charCodeAt(n);return s}function d(e){if(0===e)return u;if(e>0){if(e<256)return c+String.fromCharCode(e);if(e<65536)return a+String.fromCharCode(e>>8)+String.fromCharCode(255&e)}return String.fromCharCode(e>>24&255)+String.fromCharCode(e>>16&255)+String.fromCharCode(e>>8&255)+String.fromCharCode(255&e)}function _(e){return 16777216*e.charCodeAt(0)+(e.charCodeAt(1)<<16)+(e.charCodeAt(2)<<8)+e.charCodeAt(3)}const E={arrayBufferToString:function(e){if(!e)return"";const t=e.byteLength,n=new Uint8Array(e);if(t<h)return String.fromCharCode.apply(null,n);let s=0,r="";for(;s<t;)r+=String.fromCharCode.apply(null,n.subarray(s,s+h)),s+=h;return r},stringToArrayBuffer:function(e){return p(e).buffer},uint8ArrayToString:function(e,t=void 0){const n=e.byteLength,s=new Uint8Array(o.from(e));let r="";for(let e=0;e<n;e++)t&&"hex"===t.toLowerCase()?r+=s[e].toString(16).padStart(2,"0"):r+=String.fromCharCode(255&s[e]);return r},stringToUint8Array:p,hexStringToUint8Array:function(e){return null==e?new Uint8Array:Uint8Array.from(o.from(e,"hex"))},int8ToStr:function(e){return String.fromCharCode(255&e)},strToInt8:function(e){return 255&e.charCodeAt(0)},int16ToStr:function(e){return String.fromCharCode(e>>8&255)+String.fromCharCode(255&e)},strToInt16:function(e){return(e.charCodeAt(0)<<8)+e.charCodeAt(1)},int24ToStr:function(e){return String.fromCharCode(e>>16&255)+String.fromCharCode(e>>8&255)+String.fromCharCode(255&e)},strToInt24:function(e){return(e.charCodeAt(0)<<16)+(e.charCodeAt(1)<<8)+e.charCodeAt(2)},int32ToStr:d,strToInt32:function(e){return(e.charCodeAt(0)<<24)+(e.charCodeAt(1)<<16)+(e.charCodeAt(2)<<8)+e.charCodeAt(3)},strToUInt32:_,int64ToStr:function(e){if("number"!=typeof e)return d(e.high)+d(e.low);if(e>=0){if(e<256)return u+c+String.fromCharCode(e);if(e<65536)return u+a+String.fromCharCode(e>>8)+String.fromCharCode(255&e);if(e<4294967296)return u+(String.fromCharCode(e>>24&255)+String.fromCharCode(e>>16&255)+String.fromCharCode(e>>8&255)+String.fromCharCode(255&e))}return String.fromCharCode(e>>56&255)+String.fromCharCode(e>>48&255)+String.fromCharCode(e>>40&255)+String.fromCharCode(e>>32&255)+String.fromCharCode(e>>24&255)+String.fromCharCode(e>>16&255)+String.fromCharCode(e>>8&255)+String.fromCharCode(255&e)},strToUInt64:function(e){return s.fromBits(_(e.substr(4,4)),_(e.substr(0,4)),!0)},byteArrayToStr:function(e){const t=e.length;if(t<l)return String.fromCharCode.apply(null,e);let n=0,s="";for(;n<t;)s+=String.fromCharCode.apply(null,e.slice(n,n+l)),n+=l;return s},strToByteArray:function(e){const t=[];let n;for(n=0;n<e.length;n++)t[n]=e.charCodeAt(n);return t},strToHexArray:function(e){return Array.prototype.map.call(e.split(""),(function(e){return e.charCodeAt(0).toString(16)}))},ucs2ToUtf8:function(e){return unescape(encodeURIComponent(e))},utf8ToUcs2:function(e){return decodeURIComponent(escape(e))},anythingToBuffer:function(e){if(o.isBuffer(e))return e;if("string"==typeof e)return o.from(e,"latin1");if(e instanceof ArrayBuffer)return o.from(e);if(e.buffer instanceof ArrayBuffer&&"number"==typeof e.byteLength&&"number"==typeof e.byteOffset)return 0===e.byteOffset&&e.byteLength===e.buffer.byteLength?o.from(e.buffer):o.from(e.buffer,e.byteOffset,e.byteLength);throw new i("Parameter value failed validation",r.PARAMETER_OUT_OF_RANGE,"Expecting Buffer/Uint8Array, also accepting string, ArrayBuffer, any TypedArray, or DataView.")}};e.exports.Convert=E},8103:(e,t,n)=>{const s=n(5017),{LOG_DEBUG:r,LOG_ERROR:i,LOG_INFO:o,LOG_TRACE:a}=n(2694),{Base64:c,Bits:u}=n(9783),{ParamParse:l}=n(760),{SMFHeader:h}=n(8247),{SMFParameterType:p,SMFExtendedParameterType:d}=n(7750),{decode:_}=c,{get:E}=u;function g(e,t){return!(e.length-t<12)}function T(e,t){if(!g(e,t))return!1;const n=7&e.readUInt8(t);return 3===n||(i(`Invalid smf version in smf header, version=${n}`),!1)}function S(e,t,n,r){let o=n;for(;o<n+r;){if(o+2>n+r){i("Extended parameter stream had padding inside.");break}const a=t.readUInt8(o),c=t.readUInt8(o+1),u=E(a,7,1),l=E(a,4,3),h=(E(a,0,4)<<8)+c;o+=2;const _={0:0,1:1,2:2,3:4,4:8};let g=0;if(Object.prototype.hasOwnProperty.call(_,l))g=_[l];else if(5===l)g=t.readUInt8(o)-3,o++;else{if(6!==l)return i(`Invalid length mode ${l} in Extended Parameter type ${h}`),!1;g=t.readUInt16BE(o)-4,o+=2}switch(h){case p.AD_REDELIVERY_COUNT:e.pm_ad_redeliveryCount=t.readUInt32BE(o);break;case p.AD_SPOOLER_UNIQUE_ID:e.pm_ad_spooler_unique_id=s.fromBits(t.readUInt32BE(o+4),t.readUInt32BE(o),!0);break;case p.AD_ACK_MESSAGE_ID:e.pm_ad_local_spooler_message_id=s.fromBits(t.readUInt32BE(o+4),t.readUInt32BE(o),!0);break;case p.AD_REPL_MATE_ACK_MSGID:e.pm_ad_replication_mate_ack_message_id=s.fromBits(t.readUInt32BE(o+4),t.readUInt32BE(o),!0);break;case d.TS_TRANSPORT_CONTEXT:g>=32&&(e.pm_ts_transport_context=t.toString("latin1",o,o+g));break;default:0===u||(e.discardMessage=!0)}o+=g}return o>n+r&&i(`Last extended parameter ran beyond extended stream length by ${o-(n+r)}.`),!0}const m={isSMFHeaderAvailable:g,isSMFHeaderValid:T,isSMFAvailable:function(e,t){if(!T(e,t))return!1;const n=e.length-t;return e.readUInt32BE(t+8)<=n},parseSMFAt:function(e,t,n=!1){if(!T(e,t))return null;let r=t;const a=e.readUInt32BE(r),c=e.readUInt32BE(r+4),u=e.readUInt32BE(r+8),d=new h;d.smf_di=E(a,31,1),d.smf_elidingEligible=E(a,30,1),d.smf_dto=E(a,29,1),d.smf_adf=E(a,28,1),d.smf_deadMessageQueueEligible=E(a,27,1),d.smf_version=E(a,24,3),d.smf_uh=E(a,22,2),d.smf_protocol=E(a,16,6),d.smf_priority=E(a,12,4),d.smf_ttl=E(a,0,8);const g=u-c;if(g<0)return i("SMF parse error: lost framing"),null;if(d.setMessageSizes(c,g),n)return d;r+=12;const m=t+c;for(;r<m;){const t=e.readUInt8(r);++r;const n=E(t,6,2);if(0!==E(t,5,1)){const s=E(t,2,3),o=E(t,0,2)+1,a=o-1;if(o<=0)return i("Invalid lightweight parameter length"),null;switch(s){case p.LIGHT_CORRELATION:d.pm_corrtag=e.readUIntBE(r,3);break;case p.LIGHT_TOPIC_NAME_OFFSET:{const t=l.parseTopicQueueOffsets(e,r);d.pm_queue_offset=t[0],d.pm_queue_len=t[1];break}case p.LIGHT_QUEUE_NAME_OFFSET:{const t=l.parseTopicQueueOffsets(e,r);d.pm_topic_offset=t[0],d.pm_topic_len=t[1];break}case p.LIGHT_ACK_IMMEDIATELY:d.pm_ad_ackimm=!!e.readUInt8(r);break;default:0===n||(d.discardMessage=!0)}r+=a}else{const a=r,c=E(t,0,5);if(0===c)break;let u,h=e.readUInt8(r);if(r++,0===h?(h=e.readUInt32BE(r),r+=4,u=h-6):u=h-2,h<=0)return i(`Invalid regular parameter length ${h}/${u} with suspect type ${p.describe(c)} at parameter at position ${a}`),null;switch(c){case p.PUBLISHER_ID:break;case p.PUBLISHER_MSGID:d.pm_ad_publishermsgid=s.fromBits(e.readUInt32BE(r+4),e.readUInt32BE(r),!0);break;case p.MESSAGEPRIORITY:d.pm_msg_priority=e.readUInt8(r);break;case p.USERDATA:d.pm_userdata=e.toString("latin1",r,r+u);break;case p.USERNAME:d.pm_username=_(e.toString("latin1",r,r+u));break;case p.PASSWORD:d.pm_password=_(e.toString("latin1",r,r+u));break;case p.RESPONSE:{const t=l.parseResponseParam(e,r,u);d.pm_respcode=t[0],d.pm_respstr=t[1];break}case p.SUB_ID_LIST:case p.GENERIC_ATTACHMENT:case p.BINARY_ATTACHMENT:o("Skipping deprecated parameter type");break;case p.DELIVERY_MODE:d.smf_adf&&(d.pm_deliverymode=l.parseDeliveryMode(e,r));break;case p.ASSURED_MESSAGE_ID:d.pm_ad_msgid=s.fromBits(e.readUInt32BE(r+4),e.readUInt32BE(r),!0);break;case p.ASSURED_PREVMESSAGE_ID:d.pm_ad_prevmsgid=s.fromBits(e.readUInt32BE(r+4),e.readUInt32BE(r),!0);break;case p.ASSURED_REDELIVERED_FLAG:d.pm_ad_redelflag=!0;break;case p.AD_TIMETOLIVE:d.pm_ad_ttl=s.fromBits(e.readUInt32BE(r+4),e.readUInt32BE(r),!0);break;case p.AD_TOPICSEQUENCE_NUMBER:d.pm_ad_topicSequenceNumber=s.fromBits(e.readUInt32BE(r+4),e.readUInt32BE(r),!0);break;case p.MESSAGE_CONTENT_SUMMARY:{const t=l.parseContentSummary(e,r,u);if(!t)return i(`Invalid message content summary at ${r}, len ${u}`),!1;d.pm_content_summary=t;break}case p.ASSURED_FLOWID:d.pm_ad_flowid=e.readUInt32BE(r);break;case p.TR_TOPICNAME:d.pm_tr_topicname_bytes=e.toString("latin1",r,r+u);break;case p.AD_FLOWREDELIVERED_FLAG:d.pm_ad_flowredelflag=!0;break;case p.EXTENDED_TYPE_STREAM:if(!S(d,e,r,u))return null;break;default:0===n||(d.discardMessage=!0)}r+=u}}return d}};e.exports.ParseSMF=m},8165:e=>{e.exports.CorrelatedRequest=class{constructor(e,t,n,s){this.correlationTag=e,this.timer=t,this.correlationKey=n,this.respRecvdCallback=s}}},8188:e=>{e.exports.TransportClientStats=class{constructor(){this.bytesWritten=0,this.msgWritten=0}}},8205:(e,t,n)=>{const{SMFClient:s}=n(3175),{TransportCapabilities:r}=n(9984),{TransportError:i}=n(2680),{TransportFactory:o}=n(4723),{TransportProtocol:a}=n(9072),{TransportReturnCode:c}=n(9944),{TransportSessionEventCode:u}=n(3427),{TransportSessionStates:l}=n(3304);e.exports.SMFClient=s,e.exports.TransportCapabilities=r,e.exports.TransportError=i,e.exports.TransportFactory=o,e.exports.TransportProtocol=a,e.exports.TransportReturnCode=c,e.exports.TransportSessionEventCode=u,e.exports.TransportSessionStates=l},8229:(e,t,n)=>{var s=n(2195);const{ErrorSubcode:r,OperationError:i,RequestError:o,RequestEventCode:a}=n(6706),{SessionEventCode:c}=n(6334);function u(e){return class extends e{constructor(e,t,n,s=void 0,r=0,i=void 0,o=void 0){super(...e),this._sessionEventCode=t,this._infoStr=n,this._responseCode=s,this._errorSubcode=r,this._correlationKey=i,this._reason=o}get sessionEventCode(){return this._sessionEventCode}get infoStr(){return this._infoStr}get responseCode(){return this._responseCode}get errorSubcode(){return this.subcode||this._errorSubcode}get errorSubCode(){return this.errorSubcode}get correlationKey(){return this._correlationKey}get reason(){return this._reason}set reason(e){this._reason=e}inspect(){return Object.assign(super.inspect||{},{sessionEventCode:c.describe(this.sessionEventCode),infoStr:this.infoStr,responseCode:this.responseCode,errorSubcode:r.describe(this.errorSubcode),correlationKey:this.correlationKey?this.correlationKey.toString():null,reason:this.reason?this.reason:null})}toString(){return s(this)}}}const l={[c.CONNECT_FAILED_ERROR]:i,[c.DOWN_ERROR]:i,[c.GUARANTEED_MESSAGE_PUBLISHER_DOWN]:i,[c.PROPERTY_UPDATE_ERROR]:o,[c.REJECTED_MESSAGE_ERROR]:o,[c.SUBSCRIPTION_ERROR]:o,[c.UNSUBSCRIBE_TE_TOPIC_ERROR]:i,[a.REQUEST_ABORTED]:o,[a.REQUEST_TIMEOUT]:o},h=(new Map).set(Object,(()=>[])).set(i,((e,t,n,s,r,i)=>[t,s,i])).set(o,((e,t,n,s,r,i)=>[t,e,r,i])),p=new Map,d=u(Object);d.build=function(e,t,n=void 0,s=0,r=void 0,i=void 0){const o=l[e]||Object;return new((()=>{let e=p.get(o);return e||(e=u(o),p.set(o,e),e)})())((h.get(o)||(()=>[]))(e,t,n,s,r,i),e,t,n,s,r,i)},e.exports.SessionEvent=d},8247:(e,t,n)=>{const{AdProtocolMessage:s}=n(9812),{BinaryMetaBlock:r}=n(818),{ClientCtrlMessage:i}=n(6670),{KeepAliveMessage:o}=n(3188),{SMFHeader:a}=n(9731),{SMFParameter:c}=n(1123),{SMFUH:u}=n(8379),{SMPMessage:l}=n(2989),{TransportSMFMessage:h}=n(4527);e.exports.AdProtocolMessage=s,e.exports.BinaryMetaBlock=r,e.exports.ClientCtrlMessage=i,e.exports.KeepAliveMessage=o,e.exports.SMFHeader=a,e.exports.SMFParameter=c,e.exports.SMFUH=u,e.exports.SMPMessage=l,e.exports.TransportSMFMessage=h},8283:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.ContentSummaryType=s.new({XML_META:0,XML_PAYLOAD:1,BINARY_ATTACHMENT:2,CID_LIST:3,BINARY_METADATA:4})},8287:(e,t,n)=>{"use strict";const s=n(7526),r=n(251),i="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;t.hp=c,t.IS=50;const o=2147483647;function a(e){if(e>o)throw new RangeError('The value "'+e+'" is invalid for option "size"');const t=new Uint8Array(e);return Object.setPrototypeOf(t,c.prototype),t}function c(e,t,n){if("number"==typeof e){if("string"==typeof t)throw new TypeError('The "string" argument must be of type string. Received type number');return h(e)}return u(e,t,n)}function u(e,t,n){if("string"==typeof e)return function(e,t){if("string"==typeof t&&""!==t||(t="utf8"),!c.isEncoding(t))throw new TypeError("Unknown encoding: "+t);const n=0|E(e,t);let s=a(n);const r=s.write(e,t);return r!==n&&(s=s.slice(0,r)),s}(e,t);if(ArrayBuffer.isView(e))return function(e){if(K(e,Uint8Array)){const t=new Uint8Array(e);return d(t.buffer,t.byteOffset,t.byteLength)}return p(e)}(e);if(null==e)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(K(e,ArrayBuffer)||e&&K(e.buffer,ArrayBuffer))return d(e,t,n);if("undefined"!=typeof SharedArrayBuffer&&(K(e,SharedArrayBuffer)||e&&K(e.buffer,SharedArrayBuffer)))return d(e,t,n);if("number"==typeof e)throw new TypeError('The "value" argument must not be of type number. Received type number');const s=e.valueOf&&e.valueOf();if(null!=s&&s!==e)return c.from(s,t,n);const r=function(e){if(c.isBuffer(e)){const t=0|_(e.length),n=a(t);return 0===n.length||e.copy(n,0,0,t),n}return void 0!==e.length?"number"!=typeof e.length||j(e.length)?a(0):p(e):"Buffer"===e.type&&Array.isArray(e.data)?p(e.data):void 0}(e);if(r)return r;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return c.from(e[Symbol.toPrimitive]("string"),t,n);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function l(e){if("number"!=typeof e)throw new TypeError('"size" argument must be of type number');if(e<0)throw new RangeError('The value "'+e+'" is invalid for option "size"')}function h(e){return l(e),a(e<0?0:0|_(e))}function p(e){const t=e.length<0?0:0|_(e.length),n=a(t);for(let s=0;s<t;s+=1)n[s]=255&e[s];return n}function d(e,t,n){if(t<0||e.byteLength<t)throw new RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(n||0))throw new RangeError('"length" is outside of buffer bounds');let s;return s=void 0===t&&void 0===n?new Uint8Array(e):void 0===n?new Uint8Array(e,t):new Uint8Array(e,t,n),Object.setPrototypeOf(s,c.prototype),s}function _(e){if(e>=o)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+o.toString(16)+" bytes");return 0|e}function E(e,t){if(c.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||K(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);const n=e.length,s=arguments.length>2&&!0===arguments[2];if(!s&&0===n)return 0;let r=!1;for(;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":return Y(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return Q(e).length;default:if(r)return s?-1:Y(e).length;t=(""+t).toLowerCase(),r=!0}}function g(e,t,n){let s=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if((n>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return b(this,t,n);case"utf8":case"utf-8":return N(this,t,n);case"ascii":return P(this,t,n);case"latin1":case"binary":return D(this,t,n);case"base64":return O(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return M(this,t,n);default:if(s)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),s=!0}}function T(e,t,n){const s=e[t];e[t]=e[n],e[n]=s}function S(e,t,n,s,r){if(0===e.length)return-1;if("string"==typeof n?(s=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),j(n=+n)&&(n=r?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(r)return-1;n=e.length-1}else if(n<0){if(!r)return-1;n=0}if("string"==typeof t&&(t=c.from(t,s)),c.isBuffer(t))return 0===t.length?-1:m(e,t,n,s,r);if("number"==typeof t)return t&=255,"function"==typeof Uint8Array.prototype.indexOf?r?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):m(e,[t],n,s,r);throw new TypeError("val must be string, number or Buffer")}function m(e,t,n,s,r){let i,o=1,a=e.length,c=t.length;if(void 0!==s&&("ucs2"===(s=String(s).toLowerCase())||"ucs-2"===s||"utf16le"===s||"utf-16le"===s)){if(e.length<2||t.length<2)return-1;o=2,a/=2,c/=2,n/=2}function u(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(r){let s=-1;for(i=n;i<a;i++)if(u(e,i)===u(t,-1===s?0:i-s)){if(-1===s&&(s=i),i-s+1===c)return s*o}else-1!==s&&(i-=i-s),s=-1}else for(n+c>a&&(n=a-c),i=n;i>=0;i--){let n=!0;for(let s=0;s<c;s++)if(u(e,i+s)!==u(t,s)){n=!1;break}if(n)return i}return-1}function f(e,t,n,s){n=Number(n)||0;const r=e.length-n;s?(s=Number(s))>r&&(s=r):s=r;const i=t.length;let o;for(s>i/2&&(s=i/2),o=0;o<s;++o){const s=parseInt(t.substr(2*o,2),16);if(j(s))return o;e[n+o]=s}return o}function I(e,t,n,s){return X(Y(t,e.length-n),e,n,s)}function R(e,t,n,s){return X(function(e){const t=[];for(let n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}(t),e,n,s)}function C(e,t,n,s){return X(Q(t),e,n,s)}function A(e,t,n,s){return X(function(e,t){let n,s,r;const i=[];for(let o=0;o<e.length&&!((t-=2)<0);++o)n=e.charCodeAt(o),s=n>>8,r=n%256,i.push(r),i.push(s);return i}(t,e.length-n),e,n,s)}function O(e,t,n){return 0===t&&n===e.length?s.fromByteArray(e):s.fromByteArray(e.slice(t,n))}function N(e,t,n){n=Math.min(e.length,n);const s=[];let r=t;for(;r<n;){const t=e[r];let i=null,o=t>239?4:t>223?3:t>191?2:1;if(r+o<=n){let n,s,a,c;switch(o){case 1:t<128&&(i=t);break;case 2:n=e[r+1],128==(192&n)&&(c=(31&t)<<6|63&n,c>127&&(i=c));break;case 3:n=e[r+1],s=e[r+2],128==(192&n)&&128==(192&s)&&(c=(15&t)<<12|(63&n)<<6|63&s,c>2047&&(c<55296||c>57343)&&(i=c));break;case 4:n=e[r+1],s=e[r+2],a=e[r+3],128==(192&n)&&128==(192&s)&&128==(192&a)&&(c=(15&t)<<18|(63&n)<<12|(63&s)<<6|63&a,c>65535&&c<1114112&&(i=c))}}null===i?(i=65533,o=1):i>65535&&(i-=65536,s.push(i>>>10&1023|55296),i=56320|1023&i),s.push(i),r+=o}return function(e){const t=e.length;if(t<=y)return String.fromCharCode.apply(String,e);let n="",s=0;for(;s<t;)n+=String.fromCharCode.apply(String,e.slice(s,s+=y));return n}(s)}c.TYPED_ARRAY_SUPPORT=function(){try{const e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),c.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(c.prototype,"parent",{enumerable:!0,get:function(){if(c.isBuffer(this))return this.buffer}}),Object.defineProperty(c.prototype,"offset",{enumerable:!0,get:function(){if(c.isBuffer(this))return this.byteOffset}}),c.poolSize=8192,c.from=function(e,t,n){return u(e,t,n)},Object.setPrototypeOf(c.prototype,Uint8Array.prototype),Object.setPrototypeOf(c,Uint8Array),c.alloc=function(e,t,n){return function(e,t,n){return l(e),e<=0?a(e):void 0!==t?"string"==typeof n?a(e).fill(t,n):a(e).fill(t):a(e)}(e,t,n)},c.allocUnsafe=function(e){return h(e)},c.allocUnsafeSlow=function(e){return h(e)},c.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==c.prototype},c.compare=function(e,t){if(K(e,Uint8Array)&&(e=c.from(e,e.offset,e.byteLength)),K(t,Uint8Array)&&(t=c.from(t,t.offset,t.byteLength)),!c.isBuffer(e)||!c.isBuffer(t))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;let n=e.length,s=t.length;for(let r=0,i=Math.min(n,s);r<i;++r)if(e[r]!==t[r]){n=e[r],s=t[r];break}return n<s?-1:s<n?1:0},c.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},c.concat=function(e,t){if(!Array.isArray(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return c.alloc(0);let n;if(void 0===t)for(t=0,n=0;n<e.length;++n)t+=e[n].length;const s=c.allocUnsafe(t);let r=0;for(n=0;n<e.length;++n){let t=e[n];if(K(t,Uint8Array))r+t.length>s.length?(c.isBuffer(t)||(t=c.from(t)),t.copy(s,r)):Uint8Array.prototype.set.call(s,t,r);else{if(!c.isBuffer(t))throw new TypeError('"list" argument must be an Array of Buffers');t.copy(s,r)}r+=t.length}return s},c.byteLength=E,c.prototype._isBuffer=!0,c.prototype.swap16=function(){const e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let t=0;t<e;t+=2)T(this,t,t+1);return this},c.prototype.swap32=function(){const e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let t=0;t<e;t+=4)T(this,t,t+3),T(this,t+1,t+2);return this},c.prototype.swap64=function(){const e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let t=0;t<e;t+=8)T(this,t,t+7),T(this,t+1,t+6),T(this,t+2,t+5),T(this,t+3,t+4);return this},c.prototype.toString=function(){const e=this.length;return 0===e?"":0===arguments.length?N(this,0,e):g.apply(this,arguments)},c.prototype.toLocaleString=c.prototype.toString,c.prototype.equals=function(e){if(!c.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===c.compare(this,e)},c.prototype.inspect=function(){let e="";const n=t.IS;return e=this.toString("hex",0,n).replace(/(.{2})/g,"$1 ").trim(),this.length>n&&(e+=" ... "),"<Buffer "+e+">"},i&&(c.prototype[i]=c.prototype.inspect),c.prototype.compare=function(e,t,n,s,r){if(K(e,Uint8Array)&&(e=c.from(e,e.offset,e.byteLength)),!c.isBuffer(e))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===n&&(n=e?e.length:0),void 0===s&&(s=0),void 0===r&&(r=this.length),t<0||n>e.length||s<0||r>this.length)throw new RangeError("out of range index");if(s>=r&&t>=n)return 0;if(s>=r)return-1;if(t>=n)return 1;if(this===e)return 0;let i=(r>>>=0)-(s>>>=0),o=(n>>>=0)-(t>>>=0);const a=Math.min(i,o),u=this.slice(s,r),l=e.slice(t,n);for(let e=0;e<a;++e)if(u[e]!==l[e]){i=u[e],o=l[e];break}return i<o?-1:o<i?1:0},c.prototype.includes=function(e,t,n){return-1!==this.indexOf(e,t,n)},c.prototype.indexOf=function(e,t,n){return S(this,e,t,n,!0)},c.prototype.lastIndexOf=function(e,t,n){return S(this,e,t,n,!1)},c.prototype.write=function(e,t,n,s){if(void 0===t)s="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)s=t,n=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t>>>=0,isFinite(n)?(n>>>=0,void 0===s&&(s="utf8")):(s=n,n=void 0)}const r=this.length-t;if((void 0===n||n>r)&&(n=r),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");s||(s="utf8");let i=!1;for(;;)switch(s){case"hex":return f(this,e,t,n);case"utf8":case"utf-8":return I(this,e,t,n);case"ascii":case"latin1":case"binary":return R(this,e,t,n);case"base64":return C(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return A(this,e,t,n);default:if(i)throw new TypeError("Unknown encoding: "+s);s=(""+s).toLowerCase(),i=!0}},c.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};const y=4096;function P(e,t,n){let s="";n=Math.min(e.length,n);for(let r=t;r<n;++r)s+=String.fromCharCode(127&e[r]);return s}function D(e,t,n){let s="";n=Math.min(e.length,n);for(let r=t;r<n;++r)s+=String.fromCharCode(e[r]);return s}function b(e,t,n){const s=e.length;(!t||t<0)&&(t=0),(!n||n<0||n>s)&&(n=s);let r="";for(let s=t;s<n;++s)r+=z[e[s]];return r}function M(e,t,n){const s=e.slice(t,n);let r="";for(let e=0;e<s.length-1;e+=2)r+=String.fromCharCode(s[e]+256*s[e+1]);return r}function v(e,t,n){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>n)throw new RangeError("Trying to access beyond buffer length")}function w(e,t,n,s,r,i){if(!c.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>r||t<i)throw new RangeError('"value" argument is out of bounds');if(n+s>e.length)throw new RangeError("Index out of range")}function L(e,t,n,s,r){$(t,s,r,e,n,7);let i=Number(t&BigInt(4294967295));e[n++]=i,i>>=8,e[n++]=i,i>>=8,e[n++]=i,i>>=8,e[n++]=i;let o=Number(t>>BigInt(32)&BigInt(4294967295));return e[n++]=o,o>>=8,e[n++]=o,o>>=8,e[n++]=o,o>>=8,e[n++]=o,n}function U(e,t,n,s,r){$(t,s,r,e,n,7);let i=Number(t&BigInt(4294967295));e[n+7]=i,i>>=8,e[n+6]=i,i>>=8,e[n+5]=i,i>>=8,e[n+4]=i;let o=Number(t>>BigInt(32)&BigInt(4294967295));return e[n+3]=o,o>>=8,e[n+2]=o,o>>=8,e[n+1]=o,o>>=8,e[n]=o,n+8}function F(e,t,n,s,r,i){if(n+s>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function x(e,t,n,s,i){return t=+t,n>>>=0,i||F(e,0,n,4),r.write(e,t,n,s,23,4),n+4}function B(e,t,n,s,i){return t=+t,n>>>=0,i||F(e,0,n,8),r.write(e,t,n,s,52,8),n+8}c.prototype.slice=function(e,t){const n=this.length;(e=~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),(t=void 0===t?n:~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e);const s=this.subarray(e,t);return Object.setPrototypeOf(s,c.prototype),s},c.prototype.readUintLE=c.prototype.readUIntLE=function(e,t,n){e>>>=0,t>>>=0,n||v(e,t,this.length);let s=this[e],r=1,i=0;for(;++i<t&&(r*=256);)s+=this[e+i]*r;return s},c.prototype.readUintBE=c.prototype.readUIntBE=function(e,t,n){e>>>=0,t>>>=0,n||v(e,t,this.length);let s=this[e+--t],r=1;for(;t>0&&(r*=256);)s+=this[e+--t]*r;return s},c.prototype.readUint8=c.prototype.readUInt8=function(e,t){return e>>>=0,t||v(e,1,this.length),this[e]},c.prototype.readUint16LE=c.prototype.readUInt16LE=function(e,t){return e>>>=0,t||v(e,2,this.length),this[e]|this[e+1]<<8},c.prototype.readUint16BE=c.prototype.readUInt16BE=function(e,t){return e>>>=0,t||v(e,2,this.length),this[e]<<8|this[e+1]},c.prototype.readUint32LE=c.prototype.readUInt32LE=function(e,t){return e>>>=0,t||v(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},c.prototype.readUint32BE=c.prototype.readUInt32BE=function(e,t){return e>>>=0,t||v(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},c.prototype.readBigUInt64LE=Z((function(e){q(e>>>=0,"offset");const t=this[e],n=this[e+7];void 0!==t&&void 0!==n||V(e,this.length-8);const s=t+256*this[++e]+65536*this[++e]+this[++e]*2**24,r=this[++e]+256*this[++e]+65536*this[++e]+n*2**24;return BigInt(s)+(BigInt(r)<<BigInt(32))})),c.prototype.readBigUInt64BE=Z((function(e){q(e>>>=0,"offset");const t=this[e],n=this[e+7];void 0!==t&&void 0!==n||V(e,this.length-8);const s=t*2**24+65536*this[++e]+256*this[++e]+this[++e],r=this[++e]*2**24+65536*this[++e]+256*this[++e]+n;return(BigInt(s)<<BigInt(32))+BigInt(r)})),c.prototype.readIntLE=function(e,t,n){e>>>=0,t>>>=0,n||v(e,t,this.length);let s=this[e],r=1,i=0;for(;++i<t&&(r*=256);)s+=this[e+i]*r;return r*=128,s>=r&&(s-=Math.pow(2,8*t)),s},c.prototype.readIntBE=function(e,t,n){e>>>=0,t>>>=0,n||v(e,t,this.length);let s=t,r=1,i=this[e+--s];for(;s>0&&(r*=256);)i+=this[e+--s]*r;return r*=128,i>=r&&(i-=Math.pow(2,8*t)),i},c.prototype.readInt8=function(e,t){return e>>>=0,t||v(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},c.prototype.readInt16LE=function(e,t){e>>>=0,t||v(e,2,this.length);const n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},c.prototype.readInt16BE=function(e,t){e>>>=0,t||v(e,2,this.length);const n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},c.prototype.readInt32LE=function(e,t){return e>>>=0,t||v(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},c.prototype.readInt32BE=function(e,t){return e>>>=0,t||v(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},c.prototype.readBigInt64LE=Z((function(e){q(e>>>=0,"offset");const t=this[e],n=this[e+7];void 0!==t&&void 0!==n||V(e,this.length-8);const s=this[e+4]+256*this[e+5]+65536*this[e+6]+(n<<24);return(BigInt(s)<<BigInt(32))+BigInt(t+256*this[++e]+65536*this[++e]+this[++e]*2**24)})),c.prototype.readBigInt64BE=Z((function(e){q(e>>>=0,"offset");const t=this[e],n=this[e+7];void 0!==t&&void 0!==n||V(e,this.length-8);const s=(t<<24)+65536*this[++e]+256*this[++e]+this[++e];return(BigInt(s)<<BigInt(32))+BigInt(this[++e]*2**24+65536*this[++e]+256*this[++e]+n)})),c.prototype.readFloatLE=function(e,t){return e>>>=0,t||v(e,4,this.length),r.read(this,e,!0,23,4)},c.prototype.readFloatBE=function(e,t){return e>>>=0,t||v(e,4,this.length),r.read(this,e,!1,23,4)},c.prototype.readDoubleLE=function(e,t){return e>>>=0,t||v(e,8,this.length),r.read(this,e,!0,52,8)},c.prototype.readDoubleBE=function(e,t){return e>>>=0,t||v(e,8,this.length),r.read(this,e,!1,52,8)},c.prototype.writeUintLE=c.prototype.writeUIntLE=function(e,t,n,s){e=+e,t>>>=0,n>>>=0,s||w(this,e,t,n,Math.pow(2,8*n)-1,0);let r=1,i=0;for(this[t]=255&e;++i<n&&(r*=256);)this[t+i]=e/r&255;return t+n},c.prototype.writeUintBE=c.prototype.writeUIntBE=function(e,t,n,s){e=+e,t>>>=0,n>>>=0,s||w(this,e,t,n,Math.pow(2,8*n)-1,0);let r=n-1,i=1;for(this[t+r]=255&e;--r>=0&&(i*=256);)this[t+r]=e/i&255;return t+n},c.prototype.writeUint8=c.prototype.writeUInt8=function(e,t,n){return e=+e,t>>>=0,n||w(this,e,t,1,255,0),this[t]=255&e,t+1},c.prototype.writeUint16LE=c.prototype.writeUInt16LE=function(e,t,n){return e=+e,t>>>=0,n||w(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},c.prototype.writeUint16BE=c.prototype.writeUInt16BE=function(e,t,n){return e=+e,t>>>=0,n||w(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},c.prototype.writeUint32LE=c.prototype.writeUInt32LE=function(e,t,n){return e=+e,t>>>=0,n||w(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},c.prototype.writeUint32BE=c.prototype.writeUInt32BE=function(e,t,n){return e=+e,t>>>=0,n||w(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},c.prototype.writeBigUInt64LE=Z((function(e,t=0){return L(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))})),c.prototype.writeBigUInt64BE=Z((function(e,t=0){return U(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))})),c.prototype.writeIntLE=function(e,t,n,s){if(e=+e,t>>>=0,!s){const s=Math.pow(2,8*n-1);w(this,e,t,n,s-1,-s)}let r=0,i=1,o=0;for(this[t]=255&e;++r<n&&(i*=256);)e<0&&0===o&&0!==this[t+r-1]&&(o=1),this[t+r]=(e/i|0)-o&255;return t+n},c.prototype.writeIntBE=function(e,t,n,s){if(e=+e,t>>>=0,!s){const s=Math.pow(2,8*n-1);w(this,e,t,n,s-1,-s)}let r=n-1,i=1,o=0;for(this[t+r]=255&e;--r>=0&&(i*=256);)e<0&&0===o&&0!==this[t+r+1]&&(o=1),this[t+r]=(e/i|0)-o&255;return t+n},c.prototype.writeInt8=function(e,t,n){return e=+e,t>>>=0,n||w(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},c.prototype.writeInt16LE=function(e,t,n){return e=+e,t>>>=0,n||w(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},c.prototype.writeInt16BE=function(e,t,n){return e=+e,t>>>=0,n||w(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},c.prototype.writeInt32LE=function(e,t,n){return e=+e,t>>>=0,n||w(this,e,t,4,2147483647,-2147483648),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},c.prototype.writeInt32BE=function(e,t,n){return e=+e,t>>>=0,n||w(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},c.prototype.writeBigInt64LE=Z((function(e,t=0){return L(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),c.prototype.writeBigInt64BE=Z((function(e,t=0){return U(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),c.prototype.writeFloatLE=function(e,t,n){return x(this,e,t,!0,n)},c.prototype.writeFloatBE=function(e,t,n){return x(this,e,t,!1,n)},c.prototype.writeDoubleLE=function(e,t,n){return B(this,e,t,!0,n)},c.prototype.writeDoubleBE=function(e,t,n){return B(this,e,t,!1,n)},c.prototype.copy=function(e,t,n,s){if(!c.isBuffer(e))throw new TypeError("argument should be a Buffer");if(n||(n=0),s||0===s||(s=this.length),t>=e.length&&(t=e.length),t||(t=0),s>0&&s<n&&(s=n),s===n)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("Index out of range");if(s<0)throw new RangeError("sourceEnd out of bounds");s>this.length&&(s=this.length),e.length-t<s-n&&(s=e.length-t+n);const r=s-n;return this===e&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(t,n,s):Uint8Array.prototype.set.call(e,this.subarray(n,s),t),r},c.prototype.fill=function(e,t,n,s){if("string"==typeof e){if("string"==typeof t?(s=t,t=0,n=this.length):"string"==typeof n&&(s=n,n=this.length),void 0!==s&&"string"!=typeof s)throw new TypeError("encoding must be a string");if("string"==typeof s&&!c.isEncoding(s))throw new TypeError("Unknown encoding: "+s);if(1===e.length){const t=e.charCodeAt(0);("utf8"===s&&t<128||"latin1"===s)&&(e=t)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;let r;if(t>>>=0,n=void 0===n?this.length:n>>>0,e||(e=0),"number"==typeof e)for(r=t;r<n;++r)this[r]=e;else{const i=c.isBuffer(e)?e:c.from(e,s),o=i.length;if(0===o)throw new TypeError('The value "'+e+'" is invalid for argument "value"');for(r=0;r<n-t;++r)this[r+t]=i[r%o]}return this};const G={};function k(e,t,n){G[e]=class extends n{constructor(){super(),Object.defineProperty(this,"message",{value:t.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${e}]`,this.stack,delete this.name}get code(){return e}set code(e){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:e,writable:!0})}toString(){return`${this.name} [${e}]: ${this.message}`}}}function W(e){let t="",n=e.length;const s="-"===e[0]?1:0;for(;n>=s+4;n-=3)t=`_${e.slice(n-3,n)}${t}`;return`${e.slice(0,n)}${t}`}function $(e,t,n,s,r,i){if(e>n||e<t){const s="bigint"==typeof t?"n":"";let r;throw r=i>3?0===t||t===BigInt(0)?`>= 0${s} and < 2${s} ** ${8*(i+1)}${s}`:`>= -(2${s} ** ${8*(i+1)-1}${s}) and < 2 ** ${8*(i+1)-1}${s}`:`>= ${t}${s} and <= ${n}${s}`,new G.ERR_OUT_OF_RANGE("value",r,e)}!function(e,t,n){q(t,"offset"),void 0!==e[t]&&void 0!==e[t+n]||V(t,e.length-(n+1))}(s,r,i)}function q(e,t){if("number"!=typeof e)throw new G.ERR_INVALID_ARG_TYPE(t,"number",e)}function V(e,t,n){if(Math.floor(e)!==e)throw q(e,n),new G.ERR_OUT_OF_RANGE(n||"offset","an integer",e);if(t<0)throw new G.ERR_BUFFER_OUT_OF_BOUNDS;throw new G.ERR_OUT_OF_RANGE(n||"offset",`>= ${n?1:0} and <= ${t}`,e)}k("ERR_BUFFER_OUT_OF_BOUNDS",(function(e){return e?`${e} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"}),RangeError),k("ERR_INVALID_ARG_TYPE",(function(e,t){return`The "${e}" argument must be of type number. Received type ${typeof t}`}),TypeError),k("ERR_OUT_OF_RANGE",(function(e,t,n){let s=`The value of "${e}" is out of range.`,r=n;return Number.isInteger(n)&&Math.abs(n)>2**32?r=W(String(n)):"bigint"==typeof n&&(r=String(n),(n>BigInt(2)**BigInt(32)||n<-(BigInt(2)**BigInt(32)))&&(r=W(r)),r+="n"),s+=` It must be ${t}. Received ${r}`,s}),RangeError);const H=/[^+/0-9A-Za-z-_]/g;function Y(e,t){let n;t=t||1/0;const s=e.length;let r=null;const i=[];for(let o=0;o<s;++o){if(n=e.charCodeAt(o),n>55295&&n<57344){if(!r){if(n>56319){(t-=3)>-1&&i.push(239,191,189);continue}if(o+1===s){(t-=3)>-1&&i.push(239,191,189);continue}r=n;continue}if(n<56320){(t-=3)>-1&&i.push(239,191,189),r=n;continue}n=65536+(r-55296<<10|n-56320)}else r&&(t-=3)>-1&&i.push(239,191,189);if(r=null,n<128){if((t-=1)<0)break;i.push(n)}else if(n<2048){if((t-=2)<0)break;i.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;i.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;i.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return i}function Q(e){return s.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(H,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function X(e,t,n,s){let r;for(r=0;r<s&&!(r+n>=t.length||r>=e.length);++r)t[r+n]=e[r];return r}function K(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}function j(e){return e!=e}const z=function(){const e="0123456789abcdef",t=new Array(256);for(let n=0;n<16;++n){const s=16*n;for(let r=0;r<16;++r)t[s+r]=e[n]+e[r]}return t}();function Z(e){return"undefined"==typeof BigInt?J:e}function J(){throw new Error("BigInt not supported")}},8293:(e,t,n)=>{const{APIProperties:s}=n(968),{DestinationType:r}=n(9620),{OperationError:i,ErrorSubcode:o}=n(6706),{QueueType:a}=n(6228),c={durable:!0,type:void 0},u={[a.QUEUE]:"queue",[a.TOPIC_ENDPOINT]:"topic endpoint"},l={[r.TOPIC]:null,[r.QUEUE]:a.QUEUE,[r.TEMPORARY_QUEUE]:a.QUEUE};e.exports.AbstractQueueDescriptor=class extends s{constructor(e){super(c,function(e){if(e&&e.name&&e.type&&r.values.includes(e.type)){if(!l[e.type])throw new i(`Cannot create a descriptor from a ${r.describe(e.type)} destination`,o.PARAMETER_CONFLICT);return{name:e.name,type:a.QUEUE,durable:e.type!==r.TEMPORARY_QUEUE}}return e}(e))}getType(){return this._type}get type(){return this.getType()}set type(e){this._type=e}isDurable(){return this._durable}get durable(){return this.isDurable()}set durable(e){this._durable=e}inspect(){return{type:this.type,durable:this.durable}}toString(){return(this.isDurable()?"":"non")+"-durable "+`${u[this.getType()]}`}}},8309:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.RequestEventCode=s.new({REQUEST_ABORTED:8,REQUEST_TIMEOUT:9})},8335:(e,t,n)=>{const s=n(617),{Destination:r}=n(5136),{DestinationType:i}=n(8805);class o extends r{constructor(e){"object"==typeof e?super({type:i.TOPIC,name:e.name,bytes:e.bytes,offset:e.offset,isValidated:e.isValidated,isWildcarded:e.isWildcarded,subscriptionInfo:e.subscriptionInfo}):super(e,i.TOPIC)}inspect(){return`[Topic ${this.getName()}]`}static createFromName(e){const t=s.DestinationUtil.validateAndEncode(i.TOPIC,e);if(t.error)throw t.error;return new o({name:e,isValidated:!0,bytes:t.bytes,offset:t.offset,isWildcarded:t.isWildcarded,subscriptionInfo:t.subscriptionInfo})}}e.exports.Topic=o},8348:(e,t,n)=>{const s=(()=>{const e=[];for(let t=0;t<256;++t)e[t]=t<33||t>126?".":String.fromCharCode(t);return e})(),r={formatDumpBytes:function(e,t,r){const{StringBuffer:i,StringUtils:o}=n(968),{isEmpty:a,padLeft:c,padRight:u}=o;if(a(e))return null;const l=new i,h=new i,p=new i;let d=0;for(let n=0,i=e.length;n<i;++n){const i=e.charCodeAt(n);p.append(c(i.toString(16),2,"0")," "),h.append(s[i]||"."),d++,8===d&&p.append("   "),16!==d&&n!==e.length-1||(r>0&&l.append(u("",r," ")),l.append(u(p.toString(),54," ")),t&&l.append(h),l.append("\n"),p.clear(),h.clear(),d=0)}return l.toString()},parseSMFStream:function(e){const{Codec:{Decode:{decodeCompoundMessage:t}}}=n(3450),{LOG_WARN:s,LOG_ERROR:r}=n(2694);if(null===e)return void r("data null in debugParseSmfStream");let i=0;for(s(`parseSMFStream(): Starting parse, length ${e.length}`);i<e.length;){const n=t(e,i),r=n?n.smfHeader:null;if(!n||!r)return s("parseSMFStream(): couldn't decode message."),void s(`Position: ${i} length: ${e.length}`);s(`>> Pos(${i}) Protocol ${r.smf_protocol}, Length: ${r.messageLength}`),i+=r.messageLength}}};e.exports.Debug=r},8352:(e,t,n)=>{const{StatType:s}=n(1737);e.exports={StatsByMode:{STAT_TX_BYMODE_MSGS:[s.TX_DIRECT_MSGS,s.TX_PERSISTENT_MSGS,s.TX_NONPERSISTENT_MSGS],STAT_TX_BYMODE_BYTES:[s.TX_DIRECT_BYTES,s.TX_PERSISTENT_BYTES,s.TX_NONPERSISTENT_BYTES],STAT_TX_BYMODE_REDELIVERED:[s.TX_DIRECT_MSGS,s.TX_PERSISTENT_REDELIVERED,s.TX_NONPERSISTENT_REDELIVERED],STAT_TX_BYMODE_BYTES_REDELIVERED:[s.TX_DIRECT_BYTES,s.TX_PERSISTENT_BYTES_REDELIVERED,s.TX_NONPERSISTENT_BYTES_REDELIVERED],STAT_RX_BYMODE_MSGS:[s.RX_DIRECT_MSGS,s.RX_PERSISTENT_MSGS,s.RX_NONPERSISTENT_MSGS],STAT_RX_BYMODE_BYTES:[s.RX_DIRECT_BYTES,s.RX_PERSISTENT_BYTES,s.RX_NONPERSISTENT_BYTES]}}},8379:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.SMFUH=s.new({IGNORE:0,REJECT:2})},8460:e=>{e.exports.CacheSessionSubscribeInfo=class{constructor(e,t,n){Object.assign(this,{correlationID:e,topic:t,cacheSession:n})}}},8496:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.QueueBrowserEventName=s.new({UP:"QueueBrowserEventName_up",DOWN:"QueueBrowserEventName_down",DOWN_ERROR:"QueueBrowserEventName_downError",CONNECT_FAILED_ERROR:"QueueBrowserEventName_connectFailedError",GM_DISABLED:"QueueBrowserEventName_GMDisabled",DISPOSED:"QueueBrowserEventName_disposed",MESSAGE:"QueueBrowserEventName_message"})},8605:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.SDTDataTypes=s.new({Null:0,Boolean:1,Integer:2,UnsignedInteger:3,Float:4,Char:5,ByteArray:6,String:7,Destination:8,SMFMessage:9,Map:10,Stream:11})},8668:e=>{e.exports.BaseMessage=class{constructor(e=null,t=[]){this._smfHeader=e,this._parameters=t}addParameter(e){this._parameters[e.getType()]=e}getParameter(e){return this._parameters[e]}getParameterArray(){return this._parameters}get smfHeader(){return this._smfHeader}set smfHeader(e){this._smfHeader=e}getResponse(){const e=this.smfHeader;return e&&e.pm_respcode&&e.pm_respstr?{responseCode:e.pm_respcode,responseString:e.pm_respstr}:null}}},8770:(e,t,n)=>{const{ErrorSubcode:s,NotImplementedError:r,OperationError:i}=n(6706),{EventEmitter:o}=n(3385),{FlowOperation:a}=n(2326),{LogFormatter:c}=n(2694),{PrivateFlowEventName:u}=n(9747),{Stats:l}=n(5747),h=[a.DISPOSE,a.GET_STATS,a.GET_PROPERTIES,a.RESET_STATS];e.exports.Flow=class extends o{constructor(e,t,n){const s=Object.assign({},n);s.emits=(s.emits||[]).concat(u.values),super(s);const r=t(this),i=this;this.logger=new c(((...e)=>[`[session=${r.sessionIdHex}]`,`[flow=${i.flowIdDec}]`,...e])),this.log=this.logger.wrap(this.log,this),this._disposing=!1,this._disposed=!1,this._userDisconnected=!0,this._properties=e,this._sessionInterface=r,this._stats=new l(r),this._privateEventEmitter=new o(s)}_emit(e,...t){this._privateEventEmitter.emit(e,...t),this.emit(e,...t)}_on(e,t){this._privateEventEmitter.on(e,t)}_once(e,t){this._privateEventEmitter.once(e,t)}_removeListener(e,t){this._privateEventEmitter.removeListener(e,t)}clearStats(){const{LOG_TRACE:e}=this.logger;this._operationCheck(a.RESET_STATS),this._stats.resetStats()}connect(){const{LOG_DEBUG:e}=this.logger;this.userDisconnected=!1,this._operationCheck(a.CONNECT)}dispose(){const{LOG_TRACE:e,LOG_DEBUG:t}=this.logger;if(this._disposed||this._disposing)return;this._operationCheck(a.DISPOSE),this._disposing=!0;const n=()=>{this._disposed=!0,this._properties=null,this._userDisconnected=!0,this._emit(this.getDisposedEvent()),this.disableEmitter(),this._privateEventEmitter.disableEmitter()};if(this._fsm._midDispatch){const e=()=>{this._fsm.terminateFsm(),n()};setTimeout((()=>e()),0)}else n()}disconnect(){this._operationCheck(a.DISCONNECT);const{LOG_DEBUG:e}=this.logger;this.userDisconnected=!0}_disconnectSession(){this._operationCheck(a.DISCONNECT)}getDisposedEvent(){throw new r("Abstract method")}getProperties(){return this._operationCheck(a.GET_PROPERTIES),this._properties.clone()}getStat(e){return this._operationCheck(a.GET_STATS),this._stats.getStat(e)}handleUncorrelatedControlMessage(e){throw new r("Guaranteed Message Connection does not implement a control message handler",e)}incStat(e,t){this._stats.incStat(e,t)}processFSMEvent(e){this._fsm.processEvent(e)}inspect(){return{flowId:this.flowIdDec}}toString(){return this.inspect()}get canAck(){return!this.disposed}get disposed(){return this._disposed}get flowIdDec(){return this.flowId||"(N/A)"}get flowId(){return new r("Flow does not implement ID accessor")}get session(){return this._session}get userDisconnected(){return this._userDisconnected}set userDisconnected(e){this._userDisconnected=e}_operationCheck(e){const{LOG_TRACE:t}=this.logger;if(a.describe(e),this._disposed)throw new i("Operation is invalid for Message Consumer in disposed state",s.INVALID_OPERATION);if(h.some((t=>t===e)))return!0;if(e===a.DISCONNECT&&this._isDisconnected())throw new i("Operation is invalid for Message Consumer in disconnected state",s.INVALID_OPERATION)}_isDisconnected(){throw new r("Flow#_isDisconnected not implemented")}}},8805:(e,t,n)=>{const{Enum:s}=n(7444),r={TOPIC:"topic",QUEUE:"queue",TEMPORARY_QUEUE:"temporary_queue"};e.exports.DestinationType=s.new(r),e.exports.DestinationType._setCanonical({TOPIC:r.TOPIC,QUEUE:r.QUEUE,TEMPORARY_QUEUE:r.TEMPORARY_QUEUE})},8821:e=>{e.exports.ArrayUtils={flatten:function e(t){return t.reduce(((t,n)=>t.concat(Array.isArray(n)?e(n):n)),[])},includes:function(e,t){return e.some((e=>e===t))}}},8847:(e,t,n)=>{const{CacheContext:s}=n(2969),{LOG_DEBUG:r}=n(2694),{CACHE_REQUEST_PREFIX:i}=s;class o{constructor(e,t,n,r,o,a,c){this.cacheSession=e,this.cacheMessageType=t,this.requestID=n,this.cbInfo=r,this.liveDataAction=o,this.topic=a,this.cacheName=c,this.subscriptionWaiting=null,this.replyReceived=!1,this.dataReceived=!1,this.isSuspect=!1,this.correlationID=`${i}${s.cacheRequestCorrelationId++}`,this.childRequests=[],this.parentRequest=null,this.queuedLiveData=[],this.liveDataFulfilled=!1,this.timeoutHandle=null}getRootRequest(){return this.parentRequest?this.parentRequest.getRootRequest():this}addChild(e){if(!(e instanceof o))throw new Error(`Invalid child ${e}`);if(e===this)throw new Error("Constructing circular child reference");const t=e;t.parentRequest=this,this.childRequests.push(t)}removeChild(e){if(e===this)throw new Error("Attempting to deconstruct invalid circular child reference");const t=e,n=this.childRequests.indexOf(t);this.childRequests.splice(n,1),t.parentRequest=null}collapse(){const e=this.parentRequest;e.isSuspect=e.isSuspect||this.isSuspect,e.dataReceived=e.dataReceived||this.dataReceived,e.removeChild(this)}cancel(){for(this.parentRequest&&this.collapse();this.childRequests.length;){const e=this.childRequests.shift();e.childRequests&&e.cancel(),this.removeChild(e)}this.clearRequestTimeout()}getRequestID(){return this.requestID}getCBInfo(){return this.cbInfo}getTopic(){return this.topic}getLiveDataAction(){return this.liveDataAction}startRequestTimeout(e,t){this.timeoutHandle=setTimeout((()=>{e(this)}),t)}clearRequestTimeout(){null!==this.timeoutHandle&&void 0!==this.timeoutHandle&&(clearTimeout(this.timeoutHandle),this.timeoutHandle=null)}toString(){return`CacheRequest[correlationID=${this.correlationID},requestID=${this.requestID},cacheName=${this.cacheName},topic=${this.topic.getName()}]`}}o.VERSION=1,o.DEFAULT_REPLY_SIZE_LIMIT=1e6,o.REPLY_SIZE_LIMIT=o.DEFAULT_REPLY_SIZE_LIMIT,e.exports.CacheRequest=o},8858:e=>{e.exports=function(e){let t=!1;if(!e.forceJURL)try{const e=new URL("b","http://a");e.pathname="c%20d",t="http://a/c%20d"===e.href}catch(e){}if(t)return;const n=Object.create(null);n.ftp=21,n.file=0,n.gopher=70,n.http=80,n.https=443,n.ws=80,n.wss=443;const s=Object.create(null);function r(){this._scheme="",this._schemeData="",this._username="",this._password=null,this._host="",this._port="",this._path=[],this._query="",this._fragment="",this._isInvalid=!1,this._isRelative=!1}function i(e){return void 0!==n[e]}function o(){r.call(this),this._isInvalid=!0}function a(e){return""===e&&o.call(this),e.toLowerCase()}function c(e){const t=e.charCodeAt(0);return t>32&&t<127&&-1===[34,35,60,62,63,96].indexOf(t)?e:encodeURIComponent(e)}function u(e){const t=e.charCodeAt(0);return t>32&&t<127&&-1===[34,35,60,62,96].indexOf(t)?e:encodeURIComponent(e)}let l;s["%2e"]=".",s[".%2e"]="..",s["%2e."]="..",s["%2e%2e"]="..";const h=/[a-zA-Z]/,p=/[a-zA-Z0-9+\-.]/;function d(e,t,r){const d=[];function _(e){d.push(e)}let E=t||"scheme start",g=0,T="",S=!1,m=!1;e:for(;(e[g-1]!==l||0===g)&&!this._isInvalid;){const d=e[g];switch(E){case"scheme start":if(!d||!h.test(d)){if(t){_("Invalid scheme.");break e}T="",E="no scheme";continue}T+=d.toLowerCase(),E="scheme";break;case"scheme":if(d&&p.test(d))T+=d.toLowerCase();else{if(":"!==d){if(t){if(l===d)break e;_(`Code point not allowed in scheme: ${d}`);break e}T="",g=0,E="no scheme";continue}if(this._scheme=T,T="",t)break e;i(this._scheme)&&(this._isRelative=!0),E="file"===this._scheme?"relative":this._isRelative&&r&&r._scheme===this._scheme?"relative or authority":this._isRelative?"authority first slash":"scheme data"}break;case"scheme data":"?"===d?(this._query="?",E="query"):"#"===d?(this._fragment="#",E="fragment"):l!==d&&"\t"!==d&&"\n"!==d&&"\r"!==d&&(this._schemeData+=c(d));break;case"no scheme":if(r&&i(r._scheme)){E="relative";continue}_("Missing scheme."),o.call(this);break;case"relative or authority":if("/"!==d||"/"!==e[g+1]){_(`Expected /, got: ${d}`),E="relative";continue}E="authority ignore slashes";break;case"relative":if(this._isRelative=!0,"file"!==this._scheme&&(this._scheme=r._scheme),l===d){this._host=r._host,this._port=r._port,this._path=r._path.slice(),this._query=r._query,this._username=r._username,this._password=r._password;break e}if("/"===d||"\\"===d)"\\"===d&&_("\\ is an invalid code point."),E="relative slash";else if("?"===d)this._host=r._host,this._port=r._port,this._path=r._path.slice(),this._query="?",this._username=r._username,this._password=r._password,E="query";else{if("#"!==d){const t=e[g+1],n=e[g+2];("file"!==this._scheme||!h.test(d)||":"!==t&&"|"!==t||l!==n&&"/"!==n&&"\\"!==n&&"?"!==n&&"#"!==n)&&(this._host=r._host,this._port=r._port,this._username=r._username,this._password=r._password,this._path=r._path.slice(),this._path.pop()),E="relative path";continue}this._host=r._host,this._port=r._port,this._path=r._path.slice(),this._query=r._query,this._fragment="#",this._username=r._username,this._password=r._password,E="fragment"}break;case"relative slash":if("/"!==d&&"\\"!==d){"file"!==this._scheme&&(this._host=r._host,this._port=r._port,this._username=r._username,this._password=r._password),E="relative path";continue}"\\"===d&&_("\\ is an invalid code point."),E="file"===this._scheme?"file host":"authority ignore slashes";break;case"authority first slash":if("/"!==d){_(`Expected '/', got: ${d}`),E="authority ignore slashes";continue}E="authority second slash";break;case"authority second slash":if(E="authority ignore slashes","/"!==d){_(`Expected '/', got: ${d}`);continue}break;case"authority ignore slashes":if("/"!==d&&"\\"!==d){E="authority";continue}_(`Expected authority, got: ${d}`);break;case"authority":if("@"===d){S&&(_("@ already seen."),T+="%40"),S=!0;for(let e=0;e<T.length;e++){const t=T[e];if("\t"===t||"\n"===t||"\r"===t){_("Invalid whitespace in authority.");continue}if(":"===t&&null===this._password){this._password="";continue}const n=c(t);null!==this._password?this._password+=n:this._username+=n}T=""}else{if(l===d||"/"===d||"\\"===d||"?"===d||"#"===d){g-=T.length,T="",E="host";continue}T+=d}break;case"file host":if(l===d||"/"===d||"\\"===d||"?"===d||"#"===d){2!==T.length||!h.test(T[0])||":"!==T[1]&&"|"!==T[1]?(0===T.length||(this._host=a.call(this,T),T=""),E="relative path start"):E="relative path";continue}"\t"===d||"\n"===d||"\r"===d?_("Invalid whitespace in file host."):T+=d;break;case"host":case"hostname":if(":"!==d||m){if(l===d||"/"===d||"\\"===d||"?"===d||"#"===d){if(this._host=a.call(this,T),T="",E="relative path start",t)break e;continue}"\t"!==d&&"\n"!==d&&"\r"!==d?("["===d?m=!0:"]"===d&&(m=!1),T+=d):_(`Invalid code point in host/hostname: ${d}`)}else if(this._host=a.call(this,T),T="",E="port","hostname"===t)break e;break;case"port":if(/[0-9]/.test(d))T+=d;else{if(l===d||"/"===d||"\\"===d||"?"===d||"#"===d||t){if(""!==T){const e=parseInt(T,10);e!==n[this._scheme]&&(this._port=`${e}`),T=""}if(t)break e;E="relative path start";continue}"\t"===d||"\n"===d||"\r"===d?_(`Invalid code point in port: ${d}`):o.call(this)}break;case"relative path start":if("\\"===d&&_("'\\' not allowed in path."),E="relative path","/"!==d&&"\\"!==d)continue;break;case"relative path":if(l!==d&&"/"!==d&&"\\"!==d&&(t||"?"!==d&&"#"!==d))"\t"!==d&&"\n"!==d&&"\r"!==d&&(T+=c(d));else{"\\"===d&&_("\\ not allowed in relative path.");const e=s[T.toLowerCase()];e&&(T=e),".."===T?(this._path.pop(),"/"!==d&&"\\"!==d&&this._path.push("")):"."===T&&"/"!==d&&"\\"!==d?this._path.push(""):"."!==T&&("file"===this._scheme&&0===this._path.length&&2===T.length&&h.test(T[0])&&"|"===T[1]&&(T=`${T[0]}:`),this._path.push(T)),T="","?"===d?(this._query="?",E="query"):"#"===d&&(this._fragment="#",E="fragment")}break;case"query":t||"#"!==d?l!==d&&"\t"!==d&&"\n"!==d&&"\r"!==d&&(this._query+=u(d)):(this._fragment="#",E="fragment");break;case"fragment":l!==d&&"\t"!==d&&"\n"!==d&&"\r"!==d&&(this._fragment+=d)}g++}}function _(e,t){void 0===t||t instanceof _||(t=new _(String(t))),e=String(e),this._url=e,r.call(this);const n=e.replace(/^[ \t\r\n\f]+|[ \t\r\n\f]+$/g,"");d.call(this,n,null,t)}_.prototype={toString(){return this.href},get href(){if(this._isInvalid)return this._url;let e="";return""===this._username&&null===this._password||(e=this._username+(null!==this._password?`:${this._password}`:"")+"@"),this.protocol+(this._isRelative?`//${e}${this.host}`:"")+this.pathname+this._query+this._fragment},set href(e){r.call(this),d.call(this,e)},get protocol(){return`${this._scheme}:`},set protocol(e){this._isInvalid||d.call(this,`${e}:`,"scheme start")},get host(){return this._isInvalid?"":this._port?`${this._host}:${this._port}`:this._host},set host(e){!this._isInvalid&&this._isRelative&&d.call(this,e,"host")},get hostname(){return this._host},set hostname(e){!this._isInvalid&&this._isRelative&&d.call(this,e,"hostname")},get port(){return this._port},set port(e){!this._isInvalid&&this._isRelative&&d.call(this,e,"port")},get pathname(){return this._isInvalid?"":this._isRelative?`/${this._path.join("/")}`:this._schemeData},set pathname(e){!this._isInvalid&&this._isRelative&&(this._path=[],d.call(this,e,"relative path start"))},get search(){return this._isInvalid||!this._query||"?"===this._query?"":this._query},set search(e){!this._isInvalid&&this._isRelative&&(this._query="?","?"===e[0]&&(e=e.slice(1)),d.call(this,e,"query"))},get hash(){return this._isInvalid||!this._fragment||"#"===this._fragment?"":this._fragment},set hash(e){this._isInvalid||(this._fragment="#","#"===e[0]&&(e=e.slice(1)),d.call(this,e,"fragment"))},get origin(){if(this._isInvalid||!this._scheme)return"";switch(this._scheme){case"data":case"file":case"javascript":case"mailto":return"null"}const e=this.host;return e?`${this._scheme}://${e}`:""}};const E=e.URL;E&&(_.createObjectURL=function(...e){return E.createObjectURL(...e)},_.revokeObjectURL=function(e){E.revokeObjectURL(e)}),e.URL=_}},8860:(e,t,n)=>{const{Flow:s}=n(8770),{FlowOperation:r}=n(2326),{PrivateFlowEventName:i}=n(9747);e.exports.Flow=s,e.exports.FlowOperation=r,e.exports.PrivateFlowEventName=i},8892:(e,t,n)=>{const s=n(199),r=n(3739),{SDTFieldType:i,SDTMapContainer:o,SDTStreamContainer:a,SDTUnsupportedValueError:c,SDTValueErrorSubcode:u}=n(769),{Check:l}=n(802),{ErrorSubcode:h,OperationError:p}=n(6706),{MessageDumpFlag:d}=n(3901),{StringBuffer:_,StringUtils:E}=n(968),g={get dumpProviders(){const e=r.MessageDumpStandardProvider;return Object.keys(e).map((t=>e[t]))}},T={getOutOfRangeValue:e=>"string"==typeof e?`<out of range>\n${s.Debug.formatDumpBytes(e)}`:`<out of range>\n${s.Debug.formatDumpBytes(e.toString("latin1"))}`,getValue(e){let t=null;try{return t=e.getValue(),t}catch(e){if(e instanceof c){if(e.getSubcode()===u.VALUE_OUTSIDE_SUPPORTED_RANGE)return this.getOutOfRangeValue(e.getSourceData())}else if(e instanceof p&&e.subcode===h.PARAMETER_INVALID_TYPE)return"<invalid type>";throw e}},printMap(e,t){if(l.nothing(e)||!(e instanceof o))return null;const n=[],r=E.padRight("",t," ");return e.getKeys().sort().forEach((o=>{const a=e.getField(o),c=a.getType(),u=this.getValue(a);let l;switch(c){case i.MAP:l=`\n${this.printMap(u,t+2)}`;break;case i.STREAM:l=`\n${this.printStream(u,t+2)}`;break;case i.BYTEARRAY:l=s.Debug.formatDumpBytes(u.toString("latin1"),!1,0),null!==l&&"\n"===l.substr(-1)&&(l=l.substring(0,l.length-1));break;default:l=null!==u?u.toString():null}n.push(`${r}Key '${o}' (${i.nameOf(c)}): ${l}`)})),n.join("\n")},printStream(e,t){if(l.nothing(e)||!(e instanceof a))return null;e.rewind();const n=[],r=E.padRight("",t," ");for(;e.hasNext();){const o=e.getNext(),a=o.getType(),c=this.getValue(o);let u;switch(a){case i.MAP:u=`\n${this.printMap(c,t+2)}`;break;case i.STREAM:u=`\n${this.printStream(c,t+2)}`;break;case i.BYTEARRAY:u=s.Debug.formatDumpBytes(c.toString("latin1"),!1,0),null!==u&&"\n"===u.substr(-1)&&(u=u.substring(0,u.length-1));break;case i.DESTINATION:u=c.toString();break;default:u=null!==c?c.toString():null}n.push(`${r}(${i.nameOf(a)}): ${u}`)}return e.rewind(),n.join("\n")},countItems(e){if(l.nothing(e)||!(e instanceof a))return 0;e.rewind();let t=0;for(;e.hasNext();)e.getNext(),t++;return e.rewind(),t},formatDate:e=>new Date(e).toString(),dump(e,t,n,s){const r=new _;let i="\n",o=!1,a=40;return null!=n&&"string"==typeof n&&(i=n),null!=s&&"number"==typeof s&&(a=s),g.dumpProviders.forEach(((n,s)=>{const[c,u,l,h]=n(e,t);u&&(o&&r.append(i),null===l||0===l.length?r.append(c):(r.append(E.padRight(`${c}:`,a," ")),r.append(l)),null!==h&&t&d.MSGDUMP_FULL&&(r.append("\n"),0!==h.indexOf("  ")&&r.append("  "),r.append(h),"\n"!==h.substr(-1)&&s<g.dumpProviders.length-1&&r.append("\n")),o=!0)})),r.toString()}};e.exports.MessageDumpUtil=T},8906:(e,t,n)=>{const{ParseInteger:s}=n(9148),{autoDecodeVarLengthNumber:r}=s,i={parseFieldHeader:function(e,t){let n=t;const s=e.readUInt8(n),i=(252&s)>>2,o=1+(3&s);n++;const a=r(e,n,o);return n+=o,[i,a,a-(1+o),n-t]}};e.exports.ParseFieldHeader=i},8916:(e,t,n)=>{var s=n(2195);const{ErrorSubcode:r}=n(5129),{OperationError:i}=n(5192),{RequestEventCode:o}=n(8309),a={[o.REQUEST_ABORTED]:r.SESSION_NOT_CONNECTED,[o.REQUEST_TIMEOUT]:r.TIMEOUT};e.exports.RequestError=class extends i{constructor(e,t,n,s){super(e,a[t],s),this.name="RequestError",this._eventCode=t,this._correlationKey=n}get requestEventCode(){return this._requestEventCode}get errorSubcode(){return super.subcode}inspect(){const e=super.inspect?super.inspect():{};return Object.assign(e,{requestEventCode:o.describe(this.requestEventCode),infoStr:this.infoStr,correlationKey:this.correlationKey})}toString(){return s(this)}}},8926:e=>{function t(e){if("number"!=typeof e)return"";const t=e.toString(16);return t.length<2?`0${t}`:t}const n={formatHexString:function(e){return"number"==typeof e?`0x${t(e)}`:"object"==typeof e&&Array.isArray(e)?e.map(t).join():"string"==typeof e?Array.prototype.map.call(e,((n,s)=>t(e.charCodeAt(s)))).join(""):null}};e.exports.Hex=n},8960:(e,t,n)=>{const{assert:s}=n(7444),{MessageConsumer:r}=n(6598),{MessageConsumerEventName:i}=n(6934);e.exports.ConsumerFlows=class{constructor(){this._allFlows=new Set,this._reconnectingFlows=new Set,this._flowsById={}}add(e){if(s(e instanceof r,"Flow was not a consumer"),this._allFlows.has(e))return e;const t=()=>{this._flowsById[e.flowId]=e},n=()=>{const t=e.flowId;this._allFlows.delete(e),this._reconnectingFlows.delete(e),this._flowsById[t]===e&&delete this._flowsById[t]},o=()=>{this._reconnectingFlows.delete(e)};return e._on(i.UP,t),e._on(i.RECONNECTED,(()=>{t(),this._allFlows.add(e),this._reconnectingFlows.delete(e)})),e._on(i.DISPOSED,n),e._on(i.RECONNECTING,(()=>{n(),this._reconnectingFlows.add(e)})),e._on(i.DOWN,o),e._on(i.DOWN_ERROR,o),this._allFlows.add(e),e}get flows(){return Array.from(this._allFlows)}get reconnectingFlows(){return Array.from(this._reconnectingFlows)}getFlowById(e){return this._flowsById[e]}disposeAll(){this._allFlows.forEach((e=>e.dispose()))}}},8963:e=>{function t(e,t,n){const s=t.filter((t=>e[t]===n));return s.length?s[0]:null}class n{constructor(e){Object.defineProperties(this,{_canonical:{value:null,enumerable:!1,writable:!0,configurable:!0}}),this._setCanonical(e)}_setCanonical(e,t=!1){this._canonical=Object.assign({},e),Object.keys(this).forEach((e=>{const t=Object.getOwnPropertyDescriptor(this,e);void 0!==t.value&&Object.defineProperty(this,e,{enumerable:!1,writable:!0,configurable:!0,value:t.value})})),Object.keys(e).forEach((n=>{Object.defineProperty(this,n,{enumerable:!0,writable:!t,configurable:!t,value:e[n]})}))}describe(e,n="<none>",s="<unknown>"){if(null==e)return n;const r=t(this._canonical,Object.keys(this._canonical||{}),e)||s;return r===e?r:`${r} (${e})`}nameOf(e){return t(this._canonical,Object.keys(this._canonical||{}),e)}get names(){return Object.keys(this._canonical||{})}get values(){return e=this._canonical,t=Object.keys(this._canonical||{}),Array.from(new Set(t.map((t=>e[t]))));var e,t}get isEnum(){return!0}static nameOf(e,t){return e.nameOf(t)}static values(e){return e.values()}static new(e){return new n(e)}static ofStrings(e){const t={};return e.forEach((e=>{t[e]=e})),n.new(t)}static ofNumbers(e){const t={};return e.forEach(((e,n)=>{t[e]=n})),n.new(t)}}e.exports.Enum=n},8976:(e,t,n)=>{const{AbstractQueueDescriptor:s}=n(8293),{Destination:r}=n(9620),i={name:void 0,durable:!0};class o extends s{constructor(e){super(e instanceof r?{name:e.name,type:e.type}:Object.assign({},i,e))}getName(){return this._name}get name(){return this.getName()}set name(e){this._name=e}inspect(){return{name:this.name,type:this.type,durable:this.durable}}toString(){return`${super.toString()} '${this.getName()||""}'`}static createFromSpec(e){return e.name?new o(e):s(e)}}e.exports.QueueDescriptor=o},9005:(e,t,n)=>{const{HTTPConnection:s}=n(6350),{HTTPTransportSession:r}=n(2712);e.exports.HTTPConnection=s,e.exports.HTTPTransportSession=r},9035:(e,t,n)=>{const s=n(3450),{ApplicationAck:r,ApplicationAckRingBuffer:i,ApplicationAckState:o}=n(7671),{assert:a}=n(7444),{CapabilityType:c}=n(5024),{ConsumerFSMEvent:u}=n(946),{ConsumerFSMEventNames:l}=n(1699),{ConsumerStateNames:h}=n(3561),{DestinationFromNetwork:p,DestinationType:d,Queue:_,Topic:E}=n(9620),{ErrorResponseSubcodeMapper:g,ErrorSubcode:T,OperationError:S}=n(6706),{LogFormatter:m}=n(2694),{Long:f}=n(9783),{MessageConsumerAcknowledgeMode:I}=n(4590),{MessageConsumerEventName:R}=n(6934),{MessageDispatcher:C}=n(6811),{PrivateFlowEventName:A}=n(8860),{QueueAccessType:O,QueueDescriptor:N,QueuePermissions:y,QueueProperties:P,QueueType:D}=n(9631),{MessageOutcome:b,RgmidFactory:M}=n(6247),{State:v,StateMachine:w}=n(7414),{Stats:L,StatType:U}=n(5747),{Timer:F}=n(3385),{TransportAcks:x,TransportAckResult:B}=n(1382),G=new r(f.UZERO,o.ACKED_SENT,b.ACCEPTED);e.exports.ConsumerFSM=class extends w{constructor({name:e,consumer:t,sessionInterface:n,properties:r}={}){super({name:e});const i=this,o=this.logger=new m((function(...e){return[`[session=${n.sessionIdHex}]`,`[message-consumer-fsm=${t.flowIdDec}]`,...e]})),{LOG_TRACE:d,LOG_DEBUG:_,LOG_INFO:E,LOG_WARN:D,LOG_ERROR:M}=o;this.log=o.wrap(this.log,this);const w=r.acknowledgeMode===I.AUTO;this._consumer=t,this._sessionInterface=n,this._acknowledgeTimeoutInMsecs=r.acknowledgeTimeoutInMsecs,this._acknowledgeThreshold=r.acknowledgeThreshold,this._localPreferredWindowSize=r.windowSize,this._localMaxWindowSize=r.windowSize,this._hasAutoAckSupport=w,this._messageDispatch=new C({emitter:t,autoAck:w,logger:o}),this._stats=new L,this._resetRemoteConnectionState(),this._resetLocalConnectionState(),this._midDispatch=!1,this._replayStartLocation=r.replayStartLocation,this._errorCausingReconnect=null;const x="EMIT",B="DISPATCH",G="NO_DISPATCH";let k=[];function W(e){const t={};k.forEach(((n,s,r)=>{function i(e,n){if(void 0===t[e])return;const i=t[e];t[e]=void 0,r[i]=null,r[s]=null}if(!(s<e)&&n)switch(n.type){case x:switch(n.data){case R.UP:case R.ACTIVE:case R.RECONNECTED:t[n.data]=s;break;case R.DOWN:case R.DOWN_ERROR:i(R.UP,n.data);break;case R.INACTIVE:i(R.ACTIVE,n.data);break;default:M(`Unexpected event in post-event action: ${n.data}`)}break;case B:t[n.type]=s;break;case G:i(B,n.type)}}))}function $(){let e,n=0;for(e=0;e<k.length;++e){n<k.length&&(W(e),n=k.length);const s=k[e];if(s)switch(s.type){case x:void 0!==s.error?t._emit(s.data,s.error):t._emit(s.data);break;case G:break;case B:this.requestStartDispatchFSM();break;default:M(`Unhandled post event action type: ${s.type}`)}}k=[]}function q(e,t,n){let s;s=void 0!==t?{type:e,data:t,error:n}:{type:e},k.push(s),k.length,1===k.length&&i.setPostEventAction($)}function V(e){a(e instanceof S),t._emit(R.CONNECT_FAILED_ERROR,e)}function H(e){let t=null;const n=e;if(n&&n.length>0&&n.some((e=>e===b.FAILED||b.REJECTED))&&!i._sessionInterface.isCapable(c.AD_APP_ACK_FAILED)){const e=`Session.capabilitySettlementOutcomeNotSupported: [ ${n.map((e=>b.nameOf(e))).join(", ")} ]`;D(e),t=new S(e,T.INVALID_OPERATION)}return t}function Y(e){let t=null;return void 0===i._replayStartLocation||i._sessionInterface.isCapable(c.MESSAGE_REPLAY)?r.topicEndpointSubscription?t=function(e){let t=null;if(e){const n=e.getSubscriptionInfo();n&&(n.isShare||n.isNoExport)&&!i._sessionInterface.isCapable(c.SHARED_SUBSCRIPTIONS)&&(t=new S("Shared Subscriptions not Supported",T.SHARED_SUBSCRIPTIONS_NOT_SUPPORTED))}return t}(r.topicEndpointSubscription):r.requiredSettlementOutcomes&&(t=H(r.requiredSettlementOutcomes)):t=new S("Message Replay Not Supported",T.REPLAY_NOT_SUPPORTED),t?e.transitionTo(i.Unbound,(()=>V(t))):r.createIfMissing&&r.queueDescriptor&&r.queueDescriptor.durable?e.transitionTo(i.CreateSent):e.transitionTo(i.BindSent)}function Q(e,t=!1){let n=null;return r.requiredSettlementOutcomes&&(n=H(r.requiredSettlementOutcomes)),n?e.transitionTo(i.Unbound,(()=>V(n))):t?e.externalTransitionTo(i.Reconnecting.RBindSent):e.transitionTo(i.Reconnecting.RBindSent)}this._addEventToEmit=(e,t)=>{(r.activeIndicationEnabled||e!==R.INACTIVE&&e!==R.ACTIVE)&&q(x,e,t)},this._requestStartDispatch=()=>{q(B)},this._requestStopDispatch=()=>{this.requestStopDispatchFSM(),q(G)},this.unhandledEventReaction((function(e){switch(e.getName()){case l.VIRTUALROUTER_NAME_CHANGED:return E("VirtualRouter name change: clearing all acknowledgement state and partition group ID, if any"),i._resetRemoteConnectionState(),this;case l.FLOW_UNBOUND:return E("Received unsolicited unbind. Flow may be manually reconnected."),this.transitionToUnbound(R.DOWN_ERROR,e.details);case l.DISPOSE:return i._dispose(),i.getCurrentState().terminate();case l.BIND_RESPONSE:return function(e){const t=n.getCorrelationTag(),i=s.AdProtocolMessage.getCloseMessageConsumer(e.flowId,t);n.sendControl(i),n.enqueueRequest(t,(()=>this.handleAccidentalBind(e)),r.connectTimeoutInMsecs,null,null)}(e.details),this;default:return e.getName(),this.getCurrentState().getName(),this}})),this.initial((function(){return this.transitionTo(i.Unbound,(e=>{E(`Starting ${e.getStateMachine().getName()}`)}))})),this.Unbound=new v({name:h.UNBOUND,parentContext:i},{emitDisabledEvent(){t._emit(R.GM_DISABLED)}}).reaction(l.FLOW_CLOSE,(function(){return this.transitionTo(this)})).reaction(l.SESSION_DOWN,(function(){return this.transitionTo(this)})).reaction(l.SESSION_DISCONNECT,(function(){return this.transitionTo(this)})).reaction(l.FLOW_OPEN,(function(){return this.transitionTo(i.Unbound.AwaitSessionUp)})).reaction(l.SESSION_UP,(function(){return i._sessionInterface.isCapable(c.GUARANTEED_MESSAGE_CONSUME)?this.transitionTo(i.Unbound.AwaitFlowOpen):(D(`Consumer is not supported by router for this client on sessionId 0x${i._sessionInterface.sessionIdHex}`),this.internalTransition((()=>this.emitDisabledEvent())))})).reaction(l.SESSION_UP_NO_AD,(function(){return this.internalTransition((()=>this.emitDisabledEvent()))})).exit((()=>{i._connectAttempts=r.connectAttempts})),this.Unbound.AwaitSessionUp=new v({name:h.UNBOUND_AWAIT_SESSION_UP,parentContext:this.Unbound},{emitBindWaiting(){t._emit(A.BIND_WAITING)}}).entry((function(){this.emitBindWaiting()})).reaction(l.SESSION_DOWN,(function(){return this.internalTransition()})).reaction(l.SESSION_DISCONNECT,(function(){return this.internalTransition()})).reaction(l.SESSION_UP,(function(){return Y(this)})),this.Unbound.AwaitFlowOpen=new v({name:h.UNBOUND_AWAIT_FLOWOPEN,parentContext:this.Unbound}).reaction(l.FLOW_OPEN,(function(){return Y(this)})),this.BindSentExtensions={sendBindRequest(){const e=n.getCorrelationTag(),o=i._transportAcks;i._endpointEnsure();const a=i._endpoint,c=i._subscription,u=s.AdProtocolMessage.getOpenMessageConsumer(r.queueDescriptor,r.queueProperties,a,c,e,r.windowSize,r.noLocal,r.activeIndicationEnabled,o.lastAcked,o.lastReceived,r.browser,i._replayStartLocation,t.endpointErrorId,t.partitionGroupId,r.requiredSettlementOutcomes&&r.requiredSettlementOutcomes.length>0);n.sendControl(u),n.enqueueRequest(e,this.handleBindTimeout.bind(this),r.connectTimeoutInMsecs,null,this.handleBindResponse.bind(this)),r.queueDescriptor,r.queueProperties,r.windowSize,r.noLocal,r.activeIndicationEnabled,o.lastAcked,o.lastReceived,r.browser,i._replayStartLocation,t.endpointErrorId,t.partitionGroupId,r.requiredSettlementOutcomes},cancelBindRequestTimer(){this.bindRequestTimer.cancel()},handleBindTimeout(){E("Bind timeout"),i.processEvent(new u({name:l.BIND_TIMEOUT}))},handleExpectedBind(e){let n=e.getPartitionGroupId();null!=n&&null!=n||(n=void 0,i._clearPartitionGroupId());const s={lastMsgIdAcked:e.getLastMsgIdAcked(),flowId:e.getFlowId(),accessType:(r=e.getAccessType(),void 0===r?O.EXCLUSIVE:r),topicEndpointBytes:e.getTopicEndpointBytes(),grantedPermissions:e.getGrantedPermissions(),allOthersPermissions:e.getAllOthersPermissions(),respectsTTL:e.getRespectsTTL(),activeFlow:e.getActiveFlow(),wantFlowChangeNotify:e.getWantFlowChangeNotify(),discardBehavior:e.getQueueDiscardBehavior(),deliveryCountSent:e.getEndpointDeliveryCountSent(),endpointId:e.getEndpointId(),maxUnackedMessages:e.getMaxUnackedMessages(),endpointErrorId:e.getEndpointErrorId(),spoolerUniqueId:e.getSpoolerUniqueId(),quota:e.getQuota(),maxMsgSize:e.getMaxMsgSize(),maxRedelivery:e.getMaxRedelivery(),partitionGroupId:n};var r;if(Object.assign(t,{accessType:s.accessType,queueDiscardBehavior:s.discardBehavior,deliveryCountSent:s.deliveryCountSent,endpointId:s.endpointId,respectsTTL:s.respectsTTL,flowId:s.flowId,permissions:s.grantedPermissions,wantFlowChangeNotify:s.wantFlowChangeNotify,endpointErrorId:s.endpointErrorId,spoolerUniqueId:s.spoolerUniqueId,partitionGroupId:n}),i._sessionInterface.isCapable(c.BR_REPLAY_ERRORID)&&(t.endpointErrorId=s.endpointErrorId),s.topicEndpointBytes&&s.topicEndpointBytes.length){s.endpoint=p.createDestinationFromBytes(s.topicEndpointBytes),i._endpoint,s.endpoint,i._endpoint=s.endpoint;const e=i._consumer._properties;e.queueDescriptor=new N({name:s.endpoint.name,type:e.queueDescriptor.type,durable:e.queueDescriptor.durable})}const o=i._consumer._properties;o.queueProperties=new P({respectsTTL:s.respectsTTL,permissions:s.allOthersPermissions,quotaMB:s.quota,maxMessageSize:s.maxMsgSize,discardBehavior:s.discardBehavior,maxMessageRedelivery:s.maxRedelivery,accessType:s.accessType}),o.queueProperties.permissions||(o.queueProperties.permissions=y.NONE),Object.assign(i,{_active:s.activeFlow,_remoteWindowSize:s.maxUnackedMessages}),f.UZERO.eq(i._transportAcks.lastAcked)?i._transportAcks.lastAcked=s.lastMsgIdAcked||f.UZERO:i._transportAcks},handleBindResponse(e){if(e.msgType!==s.SMFAdProtocolMessageType.BIND)return E(`Unexpected message type in bind response: ${s.SMFAdProtocolMessageType.describe(e.msgType)}`),i.processEvent(new u({name:l.FLOW_FAILED},new S(`Unexpected bind response: ${s.SMFAdProtocolMessageType.describe(e.msgType)}`,T.PROTOTOCOL_ERROR)));const t=e.smfHeader,n=t.pm_respcode;if(null===n)return this._consumer.incStat(U.RX_DISCARD_SMF_UNKNOWN_ELEMENT),void this._sessionInterface.sessionIdHex;if(200!==n){const e=t.pm_respstr,s=g.getADErrorSubcode(n,e);return E("Flow failed (bind):",n,e,T.describe(s)),i.processEvent(new u({name:l.FLOW_FAILED},new S(e,s,{responseCode:n})))}const r={name:l.BIND_RESPONSE};return i.processEvent(new u(r,e))}},this.BindSent=new v({name:h.BIND_SENT,parentContext:i},this.BindSentExtensions).entry((function(){i._connectAttempts--,this.sendBindRequest(),this.bindRequestTimer=F.newTimeout(r.connectTimeoutInMsecs,this.handleBindTimeout)})).reaction(l.SESSION_DOWN,(function(){return this.transitionTo(i.Unbound.AwaitSessionUp)})).reaction(l.SESSION_DISCONNECT,(function(){return this.transitionTo(i.Unbound.AwaitSessionUp,(()=>i._addEventToEmit(R.DOWN)))})).reaction(l.FLOW_CLOSE,(function(){return this.transitionTo(i.UnbindSent)})).reaction(l.BIND_TIMEOUT,(function(){return i._connectAttempts,i._connectAttempts>0?this.externalTransitionTo(i.BindSent):this.transitionTo(i.Unbound.AwaitFlowOpen,(()=>V(new S("Bind failed due to timeout",T.TIMEOUT))))})).reaction(l.FLOW_FAILED,(function(e){return this.transitionTo(i.Unbound.AwaitFlowOpen,(()=>V(e.details)))})).reaction(l.BIND_RESPONSE,(function(e){return this.handleExpectedBind(e.details),this.transitionTo(i.FlowUp)})).reaction(l.FLOW_UP,(function(){return this.transitionTo(i.FlowUp)})).exit((function(){this.cancelBindRequestTimer()})),this.Reconnecting=new v({name:h.RECONNECTING,parentContext:i}).entry((function(){this._errorCausingReconnect,i._errorCausingReconnect,t._emit(R.RECONNECTING,i._errorCausingReconnect),i._connectAttempts=r.connectAttempts,i.reconnectAttempts=r.reconnectAttempts})).initial((function(){return Q(this,!1)})).reaction(l.SESSION_DISCONNECT,(function(){return this.transitionTo(i.Reconnecting.RAwaitSessionUp)})).reaction(l.SESSION_DOWN,(function(){return this.transitionTo(i.Reconnecting.RAwaitSessionUp)})),this.Reconnecting.RAwaitSessionUp=new v({name:h.RECONNECTING_AWAIT_SESSION_UP,parentContext:this.Reconnecting}).reaction(l.SESSION_UP,(function(){return i._connectAttempts=r.connectAttempts,Q(this,!1)})),this.Reconnecting.RBindSent=new v({name:h.RECONNECTING_BIND_SENT,parentContext:this.Reconnecting},this.BindSentExtensions).entry((function(){i._connectAttempts--,this.sendBindRequest(),this.bindRequestTimer=F.newTimeout(r.connectTimeoutInMsecs,this.handleBindTimeout)})).reaction(l.FLOW_CLOSE,(function(){return this.transitionTo(i.UnbindSent)})).reaction(l.BIND_TIMEOUT,(function(){return i._connectAttempts,i._connectAttempts>0?Q(this,!0):this.transitionTo(i.Unbound.AwaitFlowOpen,(()=>V(new S("Rebind failed due to timeout",T.TIMEOUT))))})).reaction(l.FLOW_FAILED,(function(e){if(i.reconnectAttempts>0||-1===i.reconnectAttempts){if(e&&e.details&&e.details.subcode&&(e.details.subcode===T.QUEUE_SHUTDOWN||e.details.subcode===T.TOPIC_ENDPOINT_SHUTDOWN||e.details.subcode===T.GM_UNAVAILABLE))return i.reconnectAttempts,e.details.subcode,this.transitionTo(i.Reconnecting.RAwaitTimer)}else i.reconnectAttempts;return this.transitionTo(i.Unbound.AwaitFlowOpen,(()=>{return n=e.details,a(n instanceof S),void t._emit(R.DOWN_ERROR,n);var n}))})).reaction(l.BIND_RESPONSE,(function(e){return this.handleExpectedBind(e.details),this.transitionTo(i.FlowUp,(()=>i._addEventToEmit(R.RECONNECTED)))})).reaction(l.FLOW_UP,(function(){return this.transitionTo(i.FlowUp,(()=>i._addEventToEmit(R.RECONNECTED)))})).exit((function(){this.cancelBindRequestTimer()})),this.Reconnecting.RAwaitTimer=new v({name:h.RECONNECTING_AWAIT_TIMER,parentContext:this.Reconnecting},{handleReconnectIntervalTimeout(){i.processEvent(new u({name:l.RECONNECT_INTERVAL_TIMEOUT}))},cancelReconnectIntervalTimer(){this.reconnectIntervalTimer.cancel()}}).entry((function(){i.reconnectAttempts>0&&--i.reconnectAttempts,r.reconnectIntervalInMsecs,i.reconnectAttempts,this.reconnectIntervalTimer=F.newTimeout(r.reconnectIntervalInMsecs,this.handleReconnectIntervalTimeout)})).exit((function(){this.cancelReconnectIntervalTimer()})).reaction(l.RECONNECT_INTERVAL_TIMEOUT,(function(){return i._connectAttempts=r.connectAttempts,Q(this,!1)}));const X=this.FlowUp=new v({name:h.FLOW_UP,parentContext:i}).initial((function(){return this.transitionTo(0===i._active?X.XferInactive:X.Xfer)})).entry((()=>{i._replayStartLocation=void 0,i._errorCausingReconnect?i._errorCausingReconnect=null:i._addEventToEmit(R.UP)})).reaction(l.SESSION_DOWN,(function(){return this.transitionTo(i.Unbound.AwaitSessionUp)})).reaction(l.SESSION_DISCONNECT,(function(){return this.transitionTo(i.Unbound.AwaitSessionUp,(()=>i._addEventToEmit(R.DOWN)))})).reaction(l.FLOW_CLOSE,(function(){return this.transitionTo(i.UnbindSent)})).reaction(l.FLOW_UNBOUND,(e=>i.transitionToUnboundFromUp(r,R.DOWN_ERROR,e.details)));X.Xfer=new v({name:h.FLOW_UP_XFER,parentContext:X}).entry((()=>{i._addEventToEmit(R.ACTIVE),i._sendAcks(!0),i._requestStartDispatch()})).exit((()=>{i._addEventToEmit(R.INACTIVE),i._requestStopDispatch()})).reaction(l.SESSION_DISCONNECT,(function(){return i._sendAcks(!0),this.eventUnhandled()})),X.XferInactive=new v({name:h.FLOW_UP_XFER_INACTIVE,parentContext:X}).reaction(l.FLOW_ACTIVE_IND,(function(){return this.transitionTo(X.Xfer)})),this.UnbindSent=new v({name:h.UNBIND_SENT,parentContext:i},{sendUnbindRequest(){i._endpointClear();try{const e=n.getCorrelationTag(),i=s.AdProtocolMessage.getCloseMessageConsumer(t.flowId,e);n.sendControl(i),n.enqueueRequest(e,(()=>this.handleUnbindTimeout()),r.connectTimeoutInMsecs,null,(e=>this.handleUnbindResponse(e))),E("Sent consumer unbind request with arguments",{flowId:t.flowId,correlationTag:e})}catch(e){E(`Exception in sendUnbindRequest while trying to send unbind request: ${e}`),n.getCurrentStateName(),i.processEvent(new u({name:l.FLOW_UNBOUND}))}},handleUnbindTimeout:()=>(E("Unbind timeout"),i.processEvent(new u({name:l.UNBIND_TIMEOUT}))),handleUnbindResponse(e){e.msgType!==s.SMFAdProtocolMessageType.UNBIND&&E(`Unexpected message type in bind response: ${s.SMFAdProtocolMessageType.describe(e.msgType)}`);const n=e.smfHeader.pm_respcode,r=e.smfHeader.pm_respstr,o=g.getADErrorSubcode(n,r);return t.endpointErrorId=e.getEndpointErrorId(),E("Flow failed (unbind):",n,r,T.describe(o)),i.processEvent(new u({name:l.FLOW_UNBOUND},new S(r,o,n)))}}).entry((function(){this.sendUnbindRequest()})).reaction(l.UNBIND_TIMEOUT,(function(){return this.externalTransitionTo(i.UnbindSent)})).reaction(l.FLOW_UNBOUND,(()=>i.transitionToUnbound(R.DOWN))),this.CreateSent=new v({name:h.CREATE_SENT,parentContext:i},{sendCreateRequest(){const e=n.getCorrelationTag(),t=s.AdProtocolMessage.getCreate(r.queueDescriptor,r.queueProperties,e);n.sendControl(t),n.enqueueRequest(e,this.handleCreateTimeout.bind(this),r.connectTimeoutInMsecs,null,this.handleCreateResponse.bind(this))},handleCreateTimeout(){E("Create timeout"),i.processEvent(new u({name:l.CREATE_TIMEOUT}))},handleCreateResponse(e){if(e.msgType!==s.SMFAdProtocolMessageType.CREATE)return E(`Unexpected message type in create response: ${s.SMFAdProtocolMessageType.describe(e.msgType)}`),i.processEvent(new u({name:l.CREATE_FAILED},new S(`Unexpected create response: ${s.SMFAdProtocolMessageType.describe(e.msgType)}`,T.PROTOTOCOL_ERROR)));const t=e.smfHeader,n=t.pm_respcode;if(200!==n){const s=t.pm_respstr,r=g.getADErrorSubcode(n,s);if(E("Endpoint create failed:",n,s,T.describe(r)),r===T.ENDPOINT_ALREADY_EXISTS){const t={name:l.CREATE_SUCCESS};return i.processEvent(new u(t,e))}return i.processEvent(new u({name:l.CREATE_FAILED},new S(s,r,{responseCode:n})))}const r={name:l.CREATE_SUCCESS};return i.processEvent(new u(r,e))}}).entry((function(){this.sendCreateRequest()})).reaction(l.CREATE_TIMEOUT,(function(){return this.externalTransitionTo(i.BindSent)})).reaction(l.CREATE_SUCCESS,(function(){return this.externalTransitionTo(i.BindSent)})).reaction(l.CREATE_FAILED,(function(e){return this.transitionTo(i.Unbound.AwaitFlowOpen,(()=>V(e.details)))}))}acceptMessage(e){const{LOG_TRACE:t,LOG_DEBUG:n}=this.logger,s=e.getGuaranteedMessageId(),r=s.toString(),i=this._consumer;if(!this._fsmDispatch)return i.incStat(U.RX_DISCARD_NO_MATCHING_CONSUMER),!1;i.deliveryCountSent||e.setDeliveryCount(-1);const c=e._getSpoolerUniqueId();M.INVALID_SUID.eq(c)?e._setSpoolerUniqueId(i.spoolerUniqueId):void 0===i.spoolerUniqueId||M.INVALID_SUID.eq(i.spoolerUniqueId)?void 0!==i.spoolerUniqueId&&!M.INVALID_SUID.eq(i.spoolerUniqueId)||M.INVALID_SUID.eq(c)||e._setSpoolerUniqueId(M.INVALID_SUID):(i.spoolerUniqueId.toString(),c.toString(),i.spoolerUniqueId=c);const u=this._transportAcks,l=u.tryReceive(s,e.getGuaranteedPreviousMessageId()),h=u.acksPending>this.maxPendingAcks;switch(l){case B.OK:break;case B.DUPLICATE:if(i.incStat(U.RX_DISCARD_DUPLICATE),this._applicationAcks.has(s)||this._oldUnacked.has(r))h?this._sendAcks(h):this._setTransportAckTimer();else{const e=new Map;e.set(b.ACCEPTED,[[s,s]]),this._sendAck(e)}return!1;case B.OUT_OF_ORDER:return i.incStat(U.RX_DISCARD_OUT_OF_ORDER),!1;default:return a(!1,"Unhandled transport ack result",l),!1}return this._applicationAcks.insert(s,(t=>{let n=!1;if(t)switch(t.state){case o.UNACKED:this._oldUnacked.add(t.key);break;case o.ACKED_NOT_SENT:n=!0;break;case o.ACKED_SENT:break;default:a(!1,"Unhandled application ack state",o.describe(t.state))}return this._midDispatch=!0,this._messageDispatch.push(e),this._midDispatch=!1,h||n?this._sendAcks(h):this._setTransportAckTimer(),!0}))}applicationAck(e,t=!1){this.applicationSettle(e,b.ACCEPTED,t)}applicationSettle(e,t=b.ACCEPTED,n=!1){const{LOG_TRACE:s}=this.logger,r=e.toString();switch(t){case b.FAILED:this._consumer.incStat(U.RX_SETTLE_FAILED);break;case b.REJECTED:this._consumer.incStat(U.RX_SETTLE_REJECTED);break;case b.ACCEPTED:n||this._consumer.incStat(U.RX_SETTLE_ACCEPTED),this._consumer.incStat(U.RX_ACKED)}if(this._oldUnacked.delete(r)){const n=new Map;return n.set(t,[[e,e]]),void this._sendAck(n)}this._applicationAcks.updateAckState(e,o.ACKED_NOT_SENT,t),t!==b.ACCEPTED?this._sendAcks(!0):this._setTransportAckTimer()}getDestination(){return this._endpointEnsure(),this._destination}isDisconnected(){return!this.getCurrentState()||this.getActiveState(h.UNBOUND)||this.getActiveState(h.UNBOUND_AWAITING_FLOWOPEN)}requestStartDispatchUser(){this._userDispatch=!0,this.applyStartDispatch()}requestStartDispatchFSM(){this._fsmDispatch=!0,this.applyStartDispatch()}applyStartDispatch(){this._userDispatch&&this._fsmDispatch?(this.log(`Starting message dispatch (fsm ${this._fsmDispatch}, user ${this._userDispatch})`),this._messageDispatch.start(),this._localMaxWindowSize=this._localPreferredWindowSize,this._sendAcks(!0)):this.log(`Not starting message dispatch (fsm ${this._fsmDispatch}, user ${this._userDispatch})`)}transitionToUnbound(e,t){const n=this._consumer,{LOG_TRACE:s}=this.logger;return n.userDisconnected,this._clearPartitionGroupId(),this.transitionTo(this.Unbound.AwaitFlowOpen,(()=>this._addEventToEmit(e,t)))}transitionToUnboundFromUp(e,t,n){const r=this._consumer,{LOG_TRACE:i}=this.logger;if(n&&n instanceof S&&n.subcode&&n.subcode===T.REPLAY_STARTED&&(this._transportAcks.reset(),this._applicationAcks.reset()),this._clearPartitionGroupId(),r.endpointErrorId&&this._sessionInterface.isCapable(c.MESSAGE_REPLAY)){r.endpointErrorId;const e=s.AdProtocolMessage.getUnbindAck(r._flowId,r.endpointErrorId,this._transportAcks.lastAcked);this._sessionInterface.sendControl(e)}return(-1===e.reconnectAttempts||e.reconnectAttempts>0)&&n&&n instanceof S&&n.subcode&&(n.subcode===T.REPLAY_STARTED||n.subcode===T.GM_UNAVAILABLE)?(this._errorCausingReconnect=n,this.transitionTo(this.Reconnecting)):this.transitionTo(this.Unbound.AwaitFlowOpen,(()=>this._addEventToEmit(t,n)))}requestStopDispatchUser(){this._userDispatch=!1,this.log(`Stop dispatch user (fsm ${this._fsmDispatch}, user ${this._userDispatch})`),this._messageDispatch.stop()}requestStopDispatchFSM(){this._fsmDispatch=!1,this.log(`Stop dispatch FSM (fsm ${this._fsmDispatch}, user ${this._userDispatch})`),this._sendAcks(!0)}_clearTransportAckTimer(){this._transportAckTimer&&(clearTimeout(this._transportAckTimer),this._transportAckTimer=null)}_dispose(){this._clearTransportAckTimer(),this._endpointClear(),this._destination=void 0,this._unacked=null,this._messageDispatch=null,this._transportAcks=null,this._consumer=null,this._sessionInterface=null}_endpointClear(){this._endpoint=void 0,this._subscription=void 0}_endpointEnsure(){if(this._endpoint)return;const e=this._sessionInterface,t=this._consumer._properties,{queueDescriptor:n}=t;let s,r,i;n.type===D.QUEUE?(s=e.createDestinationFromDescriptor(n),r=new _({name:s.name,type:d.QUEUE,offset:0,bytes:s.bytes.substr(s.offset)}),i=void 0):(r=n.name?e.createDestinationFromDescriptor(n):new E({name:"\0?",offset:0,bytes:"\0"}),i=t.topicEndpointSubscription||e.createTemporaryDestination(d.TOPIC),s=i),Object.assign(this,{_destination:s,_endpoint:r,_subscription:i}),t.queueDescriptor=new N({name:r.name,type:n.type,durable:n.durable})}_resetLocalConnectionState(){Object.assign(this,{_remoteWindowSize:0,_active:void 0,_fsmDispatch:!1,_userDispatch:!0})}_clearPartitionGroupId(){const{LOG_TRACE:e}=this.logger;this._consumer.partitionGroupId=void 0}_resetRemoteConnectionState(){const{LOG_TRACE:e}=this.logger;this._transportAcks=new x,this._applicationAcks=new i(512),this._oldUnacked=new Set,this._consumer.endpointErrorId=void 0,this._consumer.partitionGroupId=void 0}_sendAck(e){const t=s.AdProtocolMessage.getAck(this._consumer.flowId,this._transportAcks.lastReceived,this.windowSize,e);this._sessionInterface.sendControl(t)}_addAckToRanges(e,t=null){const n=e.currentRange,r=e.ackRanges,i=b.values,a=n.length;if(t&&t.state!==o.UNACKED){if(0===a||a>0&&n[a-1].settlementOutcome===t.settlementOutcome)return void n.push(t);if(a>0&&n[a-1].settlementOutcome!==t.settlementOutcome)return r[n[a-1].settlementOutcome].push(n),e.currentRange=[],void e.currentRange.push(t)}a&&r[n[a-1].settlementOutcome].push(n);let c=0;for(let e=0;e<i.length;e++)c+=r[i[e]].length;if(null===t||c===s.AdProtocolMessage.MAX_CLIENT_ACK_RANGES){const t=new Map;let n=!1;for(let e=0;e<i.length;e++)r[i[e]].length>0&&(t.set(i[e],r[i[e]].map((e=>[e[0].id,e[e.length-1].id]))),n=!0);if(n||e.forceTransportAck){const{LOG_TRACE:n}=this.logger;this._sendAck(t);const s=t.get(b.FAILED),r=t.get(b.REJECTED);this._transportAcks.lastReceived,t.has(b.ACCEPTED)&&t.get(b.ACCEPTED).map((e=>`[${e[0]}..${e[1]}]`)),t.has(b.FAILED)&&(s[0][0],s[0][1]),t.has(b.REJECTED)&&(r[0][0],r[0][1]),this._transportAcks.setAcked(),e.forceTransportAck=!1}for(let e=0;e<i.length;e++)r[i[e]].forEach((e=>{e.forEach((e=>{if(e.state!==o.ACKED_SENT)try{this._applicationAcks.updateAckState(e.id,o.ACKED_SENT)}catch(t){const{LOG_ERROR:n}=this.logger;n(`Marking ack ${e.id} as sent failed: ${t}`)}}))}));e.ackRanges=[];for(let t=0;t<i.length;t++)e.ackRanges[i[t]]=[]}e.currentRange=[]}_sendAcks(e=!1){this._clearTransportAckTimer();const t=this._applicationAcks,n=this._transportAcks,s=b.values,r={forceTransportAck:e||n.acksPending>0,ackRanges:[],currentRange:[]};for(let e=0;e<s.length;e++)r.ackRanges[s[e]]=[];const i=this._applicationAcks.front();0===this._oldUnacked.size&&i&&i.state!==o.UNACKED&&this._addAckToRanges(r,G),t.forEach((e=>this._addAckToRanges(r,e))),this._addAckToRanges(r),a(!1===r.forceTransportAck),a(0===r.currentRange.length);for(let e=0;e<s.length;e++)a(0===r.ackRanges[s[e]].length)}_setTransportAckTimer(){this._transportAckTimer||this._consumer.disposed||(this._transportAckTimer=setTimeout((()=>this._sendAcks(!0)),this._acknowledgeTimeoutInMsecs))}get maxWindowSize(){return Math.min(this._localMaxWindowSize,this._remoteWindowSize||Number.POSITIVE_INFINITY)}get windowSize(){return this.maxWindowSize-this._messageDispatch.length}get maxPendingAcks(){return this.windowSize*this._acknowledgeThreshold/100}get hasAutoAckSupport(){return this._hasAutoAckSupport}}},9072:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.TransportProtocol=s.new({HTTP_BASE64:"HTTP_BASE64",HTTP_BINARY:"HTTP_BINARY",HTTP_BINARY_STREAMING:"HTTP_BINARY_STREAMING",WS_BINARY:"WS_BINARY"})},9097:(e,t,n)=>{const s=n(8287).hp;function r(e,t,n){const r=s.allocUnsafe(n);let i,o=0;const a=e[0];for(o+=a.copy(r,0,t,a.length),i=1;i<e.length&&o<n;i++){const t=e[i];o+=t.copy(r,o,0,t.length)}return r}function i(e,t,n){let s=t,r=n;const i=[];let o=0;for(o=0;o<4;o++){for(;e[s].length<=r;)if(r-=e[s].length,s++,s>=e.length)return null;i[o]=e[s].readUInt8(r),r++}return(i[0]<<24)+(i[1]<<16)+(i[2]<<8)+i[3]}class o{constructor(e){let t=[];const n=e;let s=0,a=0;this.peekView=function(e){const n=[];return t.length<1||e>s?null:(t[0].length>=e+a?(n[0]=t[0],n[1]=a):(n[0]=r(t,a,e),n[1]=0),n)},this.readUInt8=function(e){if(e>=s)return null;const n=e+a;if(n<t[0].length)return t[0].readUInt8(n);let r=t[0].length,i=1;for(;r+t[i].length<=n;)r+=t[i].length,i++;return t[i].readUInt8(n-r)},this.readUInt32BE=function(e){if(e+3>=s)return null;const n=e+a;if(n+3<t[0].length)return t[0].readUInt32BE(n);if(n<t[0].length)return i(t,0,n);let r=t[0].length,o=1;for(;r+t[o].length<=n;)r+=t[o].length,o++;return n-r+3<t[o].length?t[o].readUInt32BE(n-r):i(t,o,n-r)},this.put=function(e){const r=o.adaptData(e),i=r.length;return 0===i||!(s+i>=n)&&(t.push(r),s+=i,!0)},this.advance=function(e){if(e<1)return;if(e>=s)return void this.reset();let n=0;for(;n<e;){if(!(t[0].length-a<=e-n)){a+=e-n,s-=e-n;break}{const e=t[0].length-a;t.shift(),n+=e,s-=e,a=0}}},this.reset=function(){t=[],s=0,a=0},this.remaining=function(){return s},this.isEmpty=function(){return 0===s}}static adaptData(e){return e instanceof s?e:s.from(e)}}e.exports.BufferQueue=o,e.exports.concatFrom=r},9148:(e,t,n)=>{const s=n(5017),{SDTField:r}=n(7385),{SDTFieldType:i}=n(7849),{SDTUnsupportedValueError:o}=n(2157),{SDTValueErrorSubcode:a}=n(3268),{create:c}=r,u={autoDecodeVarLengthNumber:function(e,t,n){return!![1,2,3,4].includes(n)&&e.readUIntBE(t,n)},parseIntegerField:function(e,t,n,r){let u=0;switch(r){case 1:return e?(u=t.readInt8(n),c(i.INT8,u)):(u=t.readUInt8(n),c(i.UINT8,u));case 2:return e?(u=t.readInt16BE(n),c(i.INT16,u)):(u=t.readUInt16BE(n),c(i.UINT16,u));case 4:return e?(u=t.readInt32BE(n),c(i.INT32,u)):(u=t.readUInt32BE(n),c(i.UINT32,u));case 8:{let l=null;const h=s.fromBits(t.readUInt32BE(n+4),t.readUInt32BE(n),!e);h.getNumBitsAbs()>48&&(l=new o("Value is not supported",a.VALUE_OUTSIDE_SUPPORTED_RANGE,t.toString("latin1",n,r))),u=h.toNumber();const p=c(e?i.INT64:i.UINT64,u);return l&&p.setError(l),p}default:return null}}};e.exports.ParseInteger=u},9304:(e,t,n)=>{var s=n(2195);const{APIProperties:r}=n(968),{QueueAccessType:i}=n(1851),{QueueDiscardBehavior:o}=n(9449),{QueuePermissions:a}=n(9489),c={permissions:void 0,accessType:void 0,quotaMB:void 0,maxMessageSize:void 0,respectsTTL:void 0,discardBehavior:void 0,maxMessageRedelivery:void 0};e.exports.QueueProperties=class extends r{constructor(e){super(c,e)}get permissions(){return this._permissions}set permissions(e){this._permissions=e}get accessType(){return this._accessType||c.accessType}set accessType(e){this._accessType=e}get quotaMB(){return this._quotaMB}set quotaMB(e){this._quotaMB=e}get maxMessageSize(){return this._maxMessageSize}set maxMessageSize(e){this._maxMessageSize=e}get respectsTTL(){return this._respectsTTL}set respectsTTL(e){this._respectsTTL=e}get discardBehavior(){return this._discardBehavior}set discardBehavior(e){this._discardBehavior=e}get maxMessageRedelivery(){return this._maxMessageRedelivery}set maxMessageRedelivery(e){this._maxMessageRedelivery=e}inspect(){return{permissions:a.describe(this.permissions),accessType:i.describe(this.accessType),quotaMB:this.quotaMB,maxMessageSize:this.maxMessageSize,respectsTTL:this.respectsTTL,discardBehavior:this.discardBehavior?o.describe(this.discardBehavior):void 0,maxMessageRedelivery:this.maxMessageRedelivery}}toString(){return s(this)}}},9309:(e,t,n)=>{const{Parameter:s}=n(802),{ReplayStartLocation:r,ReplayStartType:i}=n(4115),{ReplayStartLocationBeginning:o}=n(608),{ReplayStartLocationDate:a}=n(5439),{SolclientFactory:c}=n(4386);e.exports.ReplayStartLocation=r,e.exports.ReplayStartLocationBeginning=o,e.exports.ReplayStartLocationDate=a,e.exports.ReplayStartType=i,c.createReplayStartLocationBeginning=c.createFactory((()=>new o)),c.createReplayStartLocationDate=c.createFactory((e=>a.createReplayStartLocationDate(s.isInstanceOf("date",e,Date))))},9436:e=>{const t=new Date(Date.parse("Mon Apr 28 2025 14:29:15 GMT-0400 (Eastern Daylight Time)")),n="RELEASE",s=(()=>{const e=e=>e<10?`0${e}`:e,n=t;return`${n.getFullYear()}/${e(n.getMonth()+1)}/${e(n.getDate())} ${e(n.getHours())}:${e(n.getMinutes())}`})(),r=["SolclientJS","10.18.0",n,s].join(", "),i={version:"10.18.0",date:t,formattedDate:s,target:{name:"browser",node:!1,browser:!0},mode:n,debug:!1,release:!0,summary:r,toString:()=>r};e.exports.Version=i},9449:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.QueueDiscardBehavior=s.new({NOTIFY_SENDER_ON:"NOTIFY_SENDER_ON",NOTIFY_SENDER_OFF:"NOTIFY_SENDER_OFF"})},9486:(e,t,n)=>{const{Parameter:{isStringOrNothing:s}}=n(802);e.exports.Baggage=class{getBaggage(){return this._baggage||null}setBaggage(e){this._setBaggage(s("baggage",e))}_setBaggage(e){this._baggage=e}}},9489:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.QueuePermissions=s.new({NONE:"NONE",READ_ONLY:"READ_ONLY",CONSUME:"CONSUME",MODIFY_TOPIC:"MODIFY_TOPIC",DELETE:"DELETE"})},9544:(e,t,n)=>{var s=n(2195);const r=n(4205);e.exports.APIProperties=class{constructor(...e){Object.assign(this,...e)}toString(){return s(this)}clone(){return r(this,!1,1)}}},9549:(e,t,n)=>{const s=n(3884),{SDTStreamContainer:r}=n(5711),i={encodeStream:function(e){const t=[];if(!(e instanceof r))return null;let n=null;for(e.rewind();e.hasNext();)n=e.getNext(),n&&s.EncodeSingleElement.encodeSingleElementToBuf(n,t);return e.rewind(),t.join("")}};e.exports.EncodeStream=i},9556:(e,t,n)=>{const s=n(9005),{StateBase64:r}=n(5113),{StateBinary:i}=n(4207),{StateStreamingAndBinary:o}=n(9580),{StateWebSocketBinary:a}=n(1529),{WebTransport:c}=n(1250),{WebTransportCapabilities:u}=n(261),{HTTPConnection:l,HTTPTransportSession:h}=s;e.exports.HTTPConnection=l,e.exports.HTTPTransportSession=h,e.exports.StateBase64=r,e.exports.StateBinary=i,e.exports.StateStreamingAndBinary=o,e.exports.StateWebSocketBinary=a,e.exports.WebTransport=c,e.exports.WebTransportCapabilities=u},9563:(e,t,n)=>{const{LogImpl:s}=n(1719),r=()=>{};function i(e){const t=new Date,n=" ".repeat(6-e.length);let s=String(t.getFullYear()),r=String(t.getMonth()+1),i=String(t.getDate()),o=String(t.getHours()),a=String(t.getMinutes()),c=String(t.getSeconds()),u=String(t.getMilliseconds());return s="0".repeat(4-s.length)+s,r=r.length<2?`0${r}`:r,i=i.length<2?`0${i}`:i,o=o.length<2?`0${o}`:o,a=a.length<2?`0${a}`:a,c=c.length<2?`0${c}`:c,u=u.length<3?`0${u}`:u,u=u.length<3?`0${u}`:u,[`${s}-${r}-${i} ${o}:${a}:${c}.${u}`,`${e}${n}`]}e.exports.ConsoleLogImpl=class extends s{constructor(e){let t=r,s=r,o=r,a=r,c=r,u=r;const l=e||("undefined"==typeof window?n.g:window).console;l&&(l.log||l.warn)&&(l.log&&void 0!==l.log?(t=Function.prototype.bind.call(l.log,l),s=Function.prototype.bind.call(l.log,l)):l.debug&&"function"==typeof l.debug&&(t=Function.prototype.bind.call(l.debug,l),s=Function.prototype.bind.call(l.debug,l)),o=l.info&&void 0!==l.info?Function.prototype.bind.call(l.info,l):Function.prototype.bind.call(l.log,l),a=l.warn&&void 0!==l.warn?Function.prototype.bind.call(l.warn,l):Function.prototype.bind.call(l.log,l),l.error&&void 0!==l.error?(c=Function.prototype.bind.call(l.error,l),u=Function.prototype.bind.call(l.error,l)):(c=Function.prototype.bind.call(l.log,l),u=Function.prototype.bind.call(l.log,l))),super(((...e)=>{t(...i("TRACE"),...e)}),((...e)=>{s(...i("DEBUG"),...e)}),((...e)=>{o(...i("INFO"),...e)}),((...e)=>{a(...i("WARN"),...e)}),((...e)=>{c(...i("ERROR"),...e)}),((...e)=>{u(...i("FATAL"),...e)}))}}},9564:(e,t,n)=>{var s=n(2195);const r=n(9620),i=n(6247),o=n(4386),{assert:a}=n(7444),{CacheSession:c,CACHE_REQUEST_PREFIX:u}=n(2689),{CapabilityType:l}=n(2484),{Check:h,Parameter:p}=n(802),{DefaultCapabilities:d}=n(6472),{ErrorResponseSubcodeMapper:_,ErrorSubcode:E,OperationError:g,NotImplementedError:T,RequestEventCode:S}=n(6706),{EventEmitter:m}=n(3385),{GlobalContext:f}=n(343),{HostList:I}=n(7405),{LogFormatter:R}=n(2694),{MessageRxCBInfo:C}=n(2299),{MutableSessionProperty:A}=n(7113),{OutstandingDataRequest:O}=n(7105),{P2PUtil:N}=n(7603),{QueueDescriptor:y,QueueProperties:P,QueuePropertiesValidator:D,QueueType:b,explainInvalidEndpointName:M}=n(9631),{SDTField:v,SDTFieldType:w}=n(769),{SessionEvent:L}=n(8229),{ProvisionEvent:U}=n(918),{SessionEventCBInfo:F}=n(5256),{SessionEventCode:x}=n(6334),{SessionEventName:B}=n(6324),{SessionFSM:G}=n(43),{SessionFSMEvent:k}=n(1122),{SessionOperation:W}=n(1663),{SessionProperties:$}=n(9656),{SessionPropertiesValidator:q}=n(9727),{SessionRequestType:V}=n(168),{SessionState:H}=n(3183),{SessionStateName:Y}=n(6057),{Stats:Q,StatType:X}=n(5747),{StringUtils:K}=n(968),{TransportCapabilities:j,TransportProtocol:z,TransportReturnCode:Z}=n(8205);function J(e){return e&&e!==z.HTTP_BINARY_STREAMING&&e!==z.HTTP_BINARY&&e!==z.HTTP_BASE64}function ee(e){return`SessionEventCode.${x.describe(e)}`}const te="#REQ";e.exports.Session=class extends m{constructor(e,t,n){super({emits:x.values,direct:x.MESSAGE,formatEventName:ee});const s=this;this.logger=new R,this.logger.formatter=function(...e){return[`[session=${s._sessionFSM?s._sessionFSM.sessionIdHex:"(N/A)"}]`,...e]};const{LOG_TRACE:r}=this.logger;if(null!=e){const{LOG_DEBUG:e}=this.logger}this.on("error",(e=>{const{LOG_ERROR:t}=s.logger;t(e.info.error)}));const i=new $(e);{const{LOG_DEBUG:e}=this.logger}if(this._messageCallbackInfo=this.wrapMessageCallback(t),this._eventCallbackInfo=this.wrapEventCallback(n),h.empty(i.clientName)&&(i.clientName=f.GenerateClientName()),i._setUserIdentification(f.GenerateUserIdentification()),h.empty(i.applicationDescription)&&(i.applicationDescription=f.GenerateClientDescription()),q.validate(i),h.nothing(i.webTransportProtocolList)){const e=i.transportProtocol,t=function(){const{ProfileBinding:e}=o,t=[];return j.web.webSocket()&&t.push(z.WS_BINARY),e.value.cometEnabled&&(j.web.xhrBinary()&&(j.web.streaming()&&t.push(z.HTTP_BINARY_STREAMING),t.push(z.HTTP_BINARY)),t.push(z.HTTP_BASE64)),t}(),n=e?t.indexOf(e):0;if(n<0)throw new g(`Selected transport protocol ${z.describe(e)} is disabled or invalid for this platform`,E.PARAMETER_CONFLICT);if(i.webTransportProtocolList=t.slice(n),0===i.webTransportProtocolList.length)throw new g(`No usable transport protocol or fallback from ${z.describe(e)}`,E.PARAMETER_CONFLICT);const s=i.webTransportProtocolList.filter((e=>J(e)));if(0===s.length&&(this._adDisabledReason=`Guaranteed messaging not compatible with any available transport protocol: ${i.webTransportProtocolList.map((e=>z.describe(e))).join(", ")}`),i.publisherProperties.enabled){if(this._adDisabledReason)throw new g("Invalid transport protocol(s) for session with Guaranteed Messaging Publisher",E.PARAMETER_CONFLICT,this._adDisabledReason);i.webTransportProtocolList=s}}else{const e=i.webTransportProtocolList;if(!e.every(J)){const t=e.filter((e=>!J(e)));if(this._adDisabledReason=`Guaranteed messaging incompatible with selected transport protocols: ${t.map((e=>z.describe(e))).join(", ")}`,i.publisherProperties.enabled)throw new g("Invalid transport protocol(s) for session with Guaranteed Messaging Publisher",E.PARAMETER_CONFLICT,this._adDisabledReason)}}this._sessionProperties=i,this._sessionStats=new Q,this._hosts=new I(i),this._sessionFSM=new G(this._sessionProperties,this,this._sessionStats,this._hosts),this._sessionFSM.start(),this._sessionFSM.createMessagePublisher(),this._outstandingDataReqs={},this._capabilities=d.createDefaultCapabilities(i),this._seqNum=1}connect(){const{LOG_TRACE:e}=this.logger,t=this.allowOperation(W.CONNECT);if(t)throw new g(t,E.INVALID_OPERATION,null);const n=new k({name:B.CONNECT});this._sessionFSM.processEvent(n)}get canAck(){return[Y.CONNECTING,Y.TRANSPORT_UP,Y.DISCONNECTING].some((e=>!!this._sessionFSM.getActiveState(e)))}disconnect(){const{LOG_TRACE:e}=this.logger,t=this.allowOperation(W.DISCONNECT);if(t)throw new g(t,E.INVALID_OPERATION,null);const n=new k({name:B.DISCONNECT});this._sessionFSM.processEvent(n)}dispose(){const{LOG_TRACE:e}=this.logger;this._disposed||setTimeout((()=>{this._sessionFSM.processEvent(new k({name:B.DISPOSE})),this._sessionFSM.terminateFsm(),this.disableEmitter(),this._disposed=!0}),0)}subscribe(e,t,s,i){const{LOG_TRACE:o}=this.logger;e&&e.toString&&e.toString();const a=this.allowOperation(W.CTRL);if(a)throw new g(a,E.INVALID_OPERATION,null);if(p.isInstanceOf("topic",e,r.Destination),e.validate(),e.getType()!==r.DestinationType.TOPIC)throw new g(`Topic is required for subscribe; ${r.DestinationType.describe(e.getType())}`,E.INVALID_TOPIC_SYNTAX);p.isBooleanOrNothing("requestConfirmation",t),p.isNumberOrNothing("requestTimeout",i),p.isRangeCompareOrNothing("requestTimeout",i,">",0);const c=this._sessionFSM.subscriptionUpdate(e,!!t,s,i,V.ADD_SUBSCRIPTION,!1,((e,n)=>this.handleSubscriptionUpdateResponse(e,n,t))),u=n(8205);if(c===u.TransportReturnCode.NO_SPACE)throw new g("Cannot send subscription request - no space in transport. Please try again later (on CAN_ACCEPT_DATA)",E.INSUFFICIENT_SPACE,u.TransportReturnCode.describe(c));if(c!==u.TransportReturnCode.OK)throw new g("Cannot send subscription request - transport error.",E.COMMUNICATION_ERROR,u.TransportReturnCode.describe(c));return c}updateQueueSubscription(e,t,s,i,o,a){const{LOG_TRACE:c}=this.logger;e&&e.toString&&e.toString(),t&&t.toString&&t.toString();const u=this.allowOperation(W.CTRL);if(u)throw new g(u,E.INVALID_OPERATION,null);if(p.isInstanceOf("topic",e,r.Destination),e.validate(),e.getType()!==r.DestinationType.TOPIC)throw new g(`Topic is required for queue subscribe; ${r.DestinationType.describe(e.getType())}`,E.INVALID_TOPIC_SYNTAX);if(p.isInstanceOf("queue",t,r.Destination),t.validate(),t.getType()!==r.DestinationType.QUEUE&&t.getType()!==r.DestinationType.TEMPORARY_QUEUE)throw new g(`Queue is required for queue subscribe; ${r.DestinationType.describe(t.getType())}`,E.PARAMETER_INVALID_TYPE);p.isNumberOrNothing("requestTimeout",a),p.isRangeCompareOrNothing("requestTimeout",a,">",0);const l=this._sessionFSM.queueSubscriptionUpdate(e,t,a,s,!1,((e,t)=>this.handleQueueSubscriptionUpdateResponse(e,t,o))),h=n(8205);if(l===h.TransportReturnCode.NO_SPACE)throw new g("Cannot send subscription request - no space in transport. Please try again later (on CAN_ACCEPT_DATA)",E.INSUFFICIENT_SPACE,h.TransportReturnCode.describe(l));if(l!==h.TransportReturnCode.OK)throw new g("Cannot send subscription request - transport error.",E.COMMUNICATION_ERROR,h.TransportReturnCode.describe(l));return l}unsubscribe(e,t,s,i){const{LOG_TRACE:o}=this.logger;e&&e.toString&&e.toString();const a=this.allowOperation(W.CTRL);if(a)throw new g(a,E.INVALID_OPERATION,null);if(p.isInstanceOf("topic",e,r.Destination),e.validate(),e.getType()!==r.DestinationType.TOPIC)throw new g(`Topic is required for unsubscribe; ${r.DestinationType.describe(e.getType())}`,E.INVALID_TOPIC_SYNTAX);p.isBooleanOrNothing("requestConfirmation",t),p.isNumberOrNothing("requestTimeout",i),p.isRangeCompareOrNothing("requestTimeout",i,">",0);const c=this._sessionFSM.subscriptionUpdate(e,!!t,s,i,V.REMOVE_SUBSCRIPTION,!1,((e,n)=>this.handleSubscriptionUpdateResponse(e,n,t))),u=n(8205);if(c===u.TransportReturnCode.NO_SPACE)throw new g("Cannot send unsubscribe request - no space in transport. Please try again later (on CAN_ACCEPT_DATA)",E.INSUFFICIENT_SPACE,u.TransportReturnCode.describe(c));if(c!==u.TransportReturnCode.OK)throw new g("Cannot send unsubscribe request - transport error.",E.COMMUNICATION_ERROR,u.TransportReturnCode.describe(c));return c}unsubscribeDurableTopicEndpoint(e){const{LOG_TRACE:t}=this.logger;e&&e.toString&&e.toString();const s=this.allowOperation(W.CTRL);if(s)throw new g(s,E.INVALID_OPERATION,null);const r=this.createDestinationFromDescriptor(y.createFromSpec(e)),i=this._sessionFSM.subscriptionUpdate(r,!0,void 0,void 0,V.REMOVE_DTE_SUBSCRIPTION,!1,((e,t)=>this.handleDTEUnsubscribeResponse(e,t))),o=n(8205);if(i===o.TransportReturnCode.NO_SPACE)throw new g("Cannot send subscription request - no space in transport. Please try again later (on CAN_ACCEPT_DATA)",E.INSUFFICIENT_SPACE,o.TransportReturnCode.describe(i));if(i!==o.TransportReturnCode.OK)throw new g("Cannot send subscription request - transport error.",E.COMMUNICATION_ERROR,o.TransportReturnCode.describe(i));return i}updateProperty(e,t,n,s){const{LOG_TRACE:i}=this.logger,o=this.allowOperation(W.CTRL);if(o)throw new g(o,E.INVALID_OPERATION,null);const{Topic:a}=r;let c;p.isEnumMember("mutableSessionProperty",e,A),p.isNumberOrNothing("requestTimeout",n),p.isRangeCompareOrNothing("requestTimeout",n,">",0);const u=this._sessionFSM.sendUpdateProperty(e,t,s,n,(n=>{const r=n.getResponse();if(200===r.responseCode){if(e===A.CLIENT_DESCRIPTION)this._sessionProperties.applicationDescription=t,c=L.build(x.PROPERTY_UPDATE_OK,r.responseString,r.responseCode,0,s,null),this.sendEvent(c);else if(e===A.CLIENT_NAME){const e=N.getP2PTopicSubscription(this._sessionProperties.p2pInboxBase),r=a.createFromName(e),i=N.getP2PTopicSubscription(n.getP2PTopicValue()),o=a.createFromName(i),u=e=>{const r=e.getResponse();if(200===r.responseCode)this._sessionProperties._setP2pInboxBase(n.getP2PTopicValue()||""),this._sessionProperties._setP2pInboxInUse(N.getP2PInboxTopic(this._sessionProperties.p2pInboxBase)),this._sessionProperties.clientName=t,c=L.build(x.PROPERTY_UPDATE_OK,r.responseString,r.responseCode,0,s,null),this.sendEvent(c);else{const e=_.getErrorSubcode(r.responseCode,r.responseString);e===E.SUBSCRIPTION_ALREADY_PRESENT&&this._sessionProperties.ignoreDuplicateSubscriptionError?(c=L.build(x.PROPERTY_UPDATE_OK,r.responseString,r.responseCode,0,s,null),this.sendEvent(c)):e===E.SUBSCRIPTION_ALREADY_PRESENT||e===E.SUBSCRIPTION_ATTRIBUTES_CONFLICT||e===E.SUBSCRIPTION_INVALID||e===E.SUBSCRIPTION_ACL_DENIED||e===E.SUBSCRIPTION_TOO_MANY?(c=L.build(x.PROPERTY_UPDATE_ERROR,r.responseString,r.responseCode,e,s,null),this.sendEvent(c)):(c=L.build(x.PROPERTY_UPDATE_ERROR,r.responseString,r.responseCode,E.SUBSCRIPTION_ERROR_OTHER,s,null),this.sendEvent(c))}},l=e=>{const t=e.getResponse();if(200===t.responseCode)this._sessionFSM.subscriptionUpdate(o,!0,s,this._sessionProperties.readTimeoutInMsecs,V.ADD_P2PINBOX,!0,u);else{const e=_.getErrorSubcode(t.responseCode,t.responseString);e===E.SUBSCRIPTION_NOT_FOUND&&this._sessionProperties.ignoreSubscriptionNotFoundError?this._sessionFSM.subscriptionUpdate(o,!0,s,this._sessionProperties.readTimeoutInMsecs,V.ADD_P2PINBOX,!0,u):e===E.SUBSCRIPTION_ATTRIBUTES_CONFLICT||e===E.SUBSCRIPTION_INVALID||e===E.SUBSCRIPTION_NOT_FOUND||e===E.SUBSCRIPTION_ACL_DENIED?(c=L.build(x.PROPERTY_UPDATE_ERROR,t.responseString,t.responseCode,e,null,null),this.sendEvent(c)):(c=L.build(x.PROPERTY_UPDATE_ERROR,t.responseString,t.responseCode,E.SUBSCRIPTION_ERROR_OTHER,null,null),this.sendEvent(c))}};this._sessionFSM.subscriptionUpdate(r,!0,s,this._sessionProperties.readTimeoutInMsecs,V.REMOVE_P2PINBOX,!0,l)}}else{const e=_.getErrorSubcode(r.responseCode,r.responseString);c=L.build(x.PROPERTY_UPDATE_ERROR,r.responseString,r.responseCode,e,s,null),this.sendEvent(c)}}));u!==Z.OK&&(c=u===Z.NO_SPACE?L.build(x.PROPERTY_UPDATE_ERROR,"Property update failed - no space in transport",null,E.INSUFFICIENT_SPACE,null,null):L.build(x.PROPERTY_UPDATE_ERROR,"Property update failed",null,E.INVALID_OPERATION,null,null),this.sendEvent(c))}updateAuthenticationOnReconnect(e){const{LOG_TRACE:t}=this.logger,n=["accessToken","idToken"],s=this.allowOperation(W.QUERY_OPERATION);if(s)throw new g(s,E.INVALID_OPERATION,null);if(!e||"object"!=typeof e)throw new g("updateAuthenticationOnReconnect parameter must be a non-empty object.",E.PARAMETER_INVALID_TYPE,null);var r;for(r in e)if(!n.includes(r))throw new g("Invalid property in updateAuthenticationOnReconnect parameter.",E.PARAMETER_CONFLICT,null);const i=this.getSessionProperties();Object.assign(i,e),q.validate(i),this._sessionProperties=i,Object.assign(this._sessionFSM._sessionProperties,e)}send(e){const{LOG_TRACE:t}=this.logger,n=this.allowOperation(W.SEND,e);if(n)throw new g(n,E.INVALID_OPERATION,null);if(p.isInstanceOf("message",e,i.Message),!this.isCapable(l.VAR_LEN_EXT_PARAM)&&(e.clearExtendedVarLenParams(),this._failOnExtendedVariableLengthProperties(e)))throw new g("Broker does not support variable length SMF extended parameters",E.INVALID_OPERATION);this.validateAndSendMessage(e)}_failOnExtendedVariableLengthProperties(e){return!1}sendRequest(e,t=void 0,n=void 0,s=void 0,o=void 0){const{LOG_TRACE:a}=this.logger,c=this.allowOperation(W.SEND,e);if(c)throw new g(c,E.INVALID_OPERATION,null);p.isInstanceOf("message",e,i.Message),p.isNumberOrNothing("timeout",t),p.isRangeCompareOrNothing("timeout",t,">=",100),p.isFunctionOrNothing("replyReceivedCBFunction",n),p.isFunctionOrNothing("requestFailedCBFunction",s);null==e.getCorrelationId()&&e.setCorrelationId(te+f.NextId());if(null==e.getReplyTo()){const t=r.Topic.createFromName(this._sessionProperties.p2pInboxInUse);e.setReplyTo(t)}this.validateAndSendMessage(e),this.enqueueOutstandingDataReq(e.getCorrelationId(),s,t,n,o)}sendReply(e,t){const{LOG_TRACE:n}=this.logger,s=this.allowOperation(W.SEND,t);if(s)throw new g(s,E.INVALID_OPERATION,null);if(p.isInstanceOfOrNothing("messageToReplyTo",e,i.Message),p.isInstanceOf("replyMessage",t,i.Message),t.setAsReplyMessage(!0),e){t.setCorrelationId(e.getCorrelationId());if(null==e.getReplyTo())throw new g("ReplyTo destination may not be null.",E.PARAMETER_OUT_OF_RANGE);t.setDestination(e.getReplyTo())}this.validateAndSendMessage(t)}getStat(e){const{LOG_TRACE:t}=this.logger,n=this.allowOperation(W.QUERY_OPERATION);if(n)throw new g(n,E.INVALID_OPERATION,null);return p.isEnumMember("statType",e,X),this._sessionFSM.getStat(e)}resetStats(){const{LOG_TRACE:e}=this.logger,t=this.allowOperation(W.QUERY_OPERATION);if(t)throw new g(t,E.INVALID_OPERATION,null);this._sessionFSM.resetStats()}getSessionProperties(){const{LOG_TRACE:e}=this.logger,t=this.allowOperation(W.QUERY_OPERATION);if(t)throw new g(t,E.INVALID_OPERATION,null);const n=this._sessionProperties.clone();return this.getSessionState()!==Y.DISCONNECTED&&this._sessionFSM._transport&&n._setWebTransportProtocolInUse(this._sessionFSM._transport.getTransportProtocol()),n}isCapable(e){const{LOG_TRACE:t}=this.logger,n=this.allowOperation(W.QUERY_OPERATION);if(n)throw new g(n,E.INVALID_OPERATION,null);p.isNumber("capabilityType",e);const s=this._capabilities;return!!s&&"boolean"==typeof s[e]&&s[e]}getCapability(e){const{LOG_TRACE:t}=this.logger,n=this.allowOperation(W.QUERY_OPERATION);if(n)throw new g(n,E.INVALID_OPERATION,null);p.isNumber("capabilityType",e);const s=this._getCapability(e);return"boolean"==typeof s?v.create(w.BOOL,s):"number"==typeof s?v.create(w.INT64,s):"string"==typeof s?v.create(w.STRING,s):null}_getCapability(e){const t=this._capabilities;if(!t)return null;const n=t[e];return void 0===n?null:n}getSessionState(){const{LOG_TRACE:e}=this.logger,t=this.allowOperation(W.QUERY_OPERATION);if(t)throw new g(t,E.INVALID_OPERATION,null);const n=this.getFSMState();switch(n){case Y.FULLY_CONNECTED:return H.CONNECTED;case Y.DISCONNECTING:return H.DISCONNECTING;case Y.DISCONNECTED:return H.DISCONNECTED;case Y.SESSION_ERROR:return H.SESSION_ERROR;case Y.CONNECTING:case Y.WAITING_FOR_INTERCONNECT_TIMEOUT:case Y.WAITING_FOR_DNS:case Y.WAITING_FOR_TRANSPORT_UP:case Y.WAITING_FOR_SESSION_UP:case Y.WAITING_FOR_LOGIN:case Y.WAITING_FOR_P2PINBOX_REG:case Y.WAITING_FOR_PUBFLOW:case Y.REAPPLYING_SUBSCRIPTIONS:return H.CONNECTING;default:{const{LOG_INFO:e}=this.logger;return e(`Unmapped session state ${Y.describe(n)}`),null}}}getFSMState(){return this._sessionFSM.getCurrentStateName()}createCacheSession(e){const{LOG_TRACE:t}=this.logger;return new c(e,this,{incStat:this._sessionFSM.incStat.bind(this._sessionFSM)})}createMessageConsumer(e){const{LOG_TRACE:t}=this.logger;if(this._adDisabledReason)throw new g("Session does not provide MessageConsumer capability",E.GM_UNAVAILABLE,this._adDisabledReason);if(null!=e){const{LOG_DEBUG:e}=this.logger}return this._sessionFSM.createMessageConsumer(e)}provisionEndpoint(e,t,n,s){p.isBooleanOrNothing("ignoreExists",n),p.isInstanceOf("queueDescriptor",e,Object,E.PARAMETER_INVALID_TYPE,"queueDescriptor must be supplied, can't be "+e),p.isString("queueDescriptor.name",e.name,E.PARAMETER_INVALID_TYPE,"provisionEndpoint only works on named endpoints");const r=new y(e);if(!r.durable)throw new T("provisionEndpoint() only works for durable endpoints.");t&&(p.isInstanceOfOrNothing("queueProperties",t,Object,E.PARAMETER_INVALID_TYPE,"queueProperties must be an object, ideally a solace.QueueProperties instance. Can't be "+t),D.validate(t));const i=this.handleProvisionResponse.bind(this,n,!0);return this._sessionFSM.provisionEndpoint(r,t,s,i)}deprovisionEndpoint(e,t,n){if(p.isBooleanOrNothing("ignoreMissing",t),!(e instanceof Object))throw new g("queueDesscriptor object required",E.PARAMETER_INVALID_TYPE);p.isString("queueDescriptor.name",e.name,E.PARAMETER_INVALID_TYPE,"deprovisionEndpoint only works on named endpoints");const s=this.handleProvisionResponse.bind(this,t,!1);return this._sessionFSM.deprovisionEndpoint(e,n,s)}handleProvisionResponse(e,t,n,s){const{correlationKey:i}=s,o=n._smfHeader.pm_respcode,a=n._smfHeader.pm_respstr,c=_.getADErrorSubcode(o,a),u=new y;u.durable=void 0;const l=new P,{DestinationUtil:h}=r;n.getQueueNameBytes()&&n.getQueueNameBytes().length>0?(u.name=h.decodeBytes(n.getQueueNameBytes()),u.type=b.QUEUE):n.getTopicEndpointBytes()&&n.getTopicEndpointBytes().length>0?(u.name=h.decodeBytes(n.getTopicEndpointBytes()),u.type=b.TOPIC_ENDPOINT):(u.name=void 0,u.type=void 0),u.durable=n.getDurability(),l.accessType=n.getAccessType(),l.discardBehavior=n.getQueueDiscardBehavior(),l.maxMessageRedelivery=n.getMaxRedelivery(),l.maxMessageSize=n.getMaxMsgSize(),l.permissions=n.getAllOthersPermissions(),l.quotaMB=n.getQuota(),l.respectsTTL=n.getRespectsTTL();let p=null;400!==o||c!==E.INVALID_QUEUE_NAME&&c!==E.INVALID_TE_NAME||(p=M(u.name));let d=x.PROVISION_ERROR;200===o||e&&t&&c==E.ENDPOINT_ALREADY_EXISTS?d=x.PROVISION_OK:!e||t||c!=E.UNKNOWN_QUEUE_NAME&&c!=E.UNKNOWN_TOPIC_ENDPOINT_NAME||(d=x.PROVISION_OK);const g=new U(d,a,o,c,i,p,u,l);this.sendEvent(g)}createQueueBrowser(e){const{LOG_TRACE:t}=this.logger;if(this._adDisabledReason)throw new g("Session does not provide QueueBrowser capability",E.GM_UNAVAILABLE,this._adDisabledReason);if(null!=e){const{LOG_DEBUG:e}=this.logger}return this._sessionFSM.createQueueBrowser(e)}createDestinationFromDescriptor(e){const{DestinationType:t,Queue:n,Topic:s}=r;let i=t.TOPIC;e.type===b.QUEUE&&(i=e.durable?t.QUEUE:t.TEMPORARY_QUEUE);const o=e.name||null;return e.durable?(a(o,"Durable endpoint with generated name is not a valid configuration"),(e.getType()===b.QUEUE?n.createFromLocalName:s.createFromName)(o)):this.createTemporaryDestination(i,o)}createTemporaryDestination(e,t){const{LOG_TRACE:n}=this.logger,{DestinationFromNetwork:s,DestinationUtil:i}=r,o=this.getSessionProperties().virtualRouterName;if(!this.isCapable(l.TEMPORARY_ENDPOINT)||null==o||0===o.length)throw new g("Attempt to generate temporary destination or endpoint without suitable session",E.INVALID_OPERATION);const a=t&&t.startsWith("#P2P")?t:i.createTemporaryName(e,o,t);return s.createDestinationFromName(a)}sendEvent(e){if(!e)return;if(this._disposed)return;const{LOG_TRACE:t}=this.logger;this._eventCallbackInfo.sessionEventCBFunction(this,e,this._eventCallbackInfo.userObject)}getTransportInfo(){const{LOG_TRACE:e}=this.logger;return this._sessionFSM.getTransportInfo()}injectTransportInterceptor(e){this._sessionFSM.injectTransportInterceptor(e)}allowOperation(e,t){if(!this._sessionFSM)return!1;let n=!0;const s=this._sessionFSM.getCurrentStateName();if(s===Y.DISPOSED)n=!1;else if(h.anything(e))switch(e){case W.CONNECT:s!==Y.NEW&&s!==Y.DISCONNECTED&&(n=!1);break;case W.DISCONNECT:s===Y.NEW&&(n=!1);break;case W.SEND:case W.CTRL:n=s===Y.FULLY_CONNECTED||t&&t.getDeliveryMode()!==i.MessageDeliveryModeType.DIRECT;break;case W.QUERY_OPERATION:n=!0;break;default:n=!1}else n=!1;return n?null:`Cannot perform operation ${e} while in state ${s}`}updateCapabilities(e){this._capabilities=e}validateAndSendMessage(e){const t=e.getDestination();if(h.nothing(t)||h.empty(t.getName()))throw new g("Message must have a valid Destination",E.TOPIC_MISSING);const n=null==e.getSenderTimestamp();if(this._sessionProperties.generateSendTimestamps&&(n||e.hasAutoSenderTimestamp)){const t=new Date;e.setSenderTimestamp(t.getTime()),e.hasAutoSenderTimestamp=!0}const s=null==e.getSequenceNumber();this._sessionProperties.generateSequenceNumber&&(s||e.hasAutoSequenceNumber)&&(e.setSequenceNumber(this._seqNum++),e.hasAutoSequenceNumber=!0);const r=null==e.getSenderId();this._sessionProperties.includeSenderId&&r&&e.setSenderId(this._sessionProperties.clientName),this._sessionFSM.prepareAndSendMessage(e)}enqueueOutstandingDataReq(e,t,n,s,r){if(h.none(e))return;const{LOG_TRACE:i,LOG_ERROR:o}=this.logger,a=setTimeout((()=>{this._sessionFSM.incStat(X.TX_REQUEST_TIMEOUT);try{delete this._outstandingDataReqs[e]||o(`Cannot delete data request ${e}`)}catch(t){o(`Cannot delete data request ${e}`,t)}if(h.anything(t)){const n=L.build(S.REQUEST_TIMEOUT,"Request timeout",e);t(this,n,r)}}),n||this._sessionProperties.readTimeoutInMsecs),c=new O(e,a,s,t,r);this._outstandingDataReqs[e]=c}cancelOutstandingDataReq(e){const{LOG_TRACE:t,LOG_ERROR:n}=this.logger;if(h.none(e)||!this._outstandingDataReqs)return null;const s=this._outstandingDataReqs[e];if(null==s)return null;s.timer&&(clearTimeout(s.timer),s.timer=null);try{delete this._outstandingDataReqs[e]||n(`Cannot delete data request ${e}`)}catch(t){n(`Cannot delete data request ${e}`,t)}return s}cleanupSession(){const{LOG_TRACE:e}=this.logger;this._outstandingDataReqs&&Object.keys(this._outstandingDataReqs).forEach((e=>{const t=this.cancelOutstandingDataReq(e);if(t&&t.reqFailedCBFunction){const n=L.build(S.REQUEST_ABORTED,"Request aborted",e);t.reqFailedCBFunction(this,n,t.userObject)}}))}handleDataMessage(e){const{LOG_TRACE:t,LOG_INFO:n}=this.logger,s=e;if(this._sessionProperties.generateReceiveTimestamps){const e=new Date;s._receiverTimestamp=e.getTime()}if(s.isReplyMessage()){const e=s.getCorrelationId();if(h.anything(e)){const t=this.cancelOutstandingDataReq(e);if(null!==t)return this._sessionFSM.incStat(X.RX_REPLY_MSG_RECVED),void t.replyReceivedCBFunction(this,s,t.userObject);if(e.startsWith(te))return n("DROP: Discard reply message due to missing outstanding request"),void this._sessionFSM.incStat(X.RX_REPLY_MSG_DISCARD);if(e.startsWith(u)&&!(c&&this._messageCallbackInfo.userObject instanceof c))return n("DROP: Discard cache reply due to no cache session active"),void this._sessionFSM.incStat(X.RX_REPLY_MSG_DISCARD)}}this._messageCallbackInfo.messageRxCBFunction(this,s,this._messageCallbackInfo.userObject)}handleSubscriptionUpdateResponse(e,t,n){const s=e.getResponse(),{responseCode:r,responseString:i}=s,{correlationKey:o}=t;if(200===r){const e=L.build(x.SUBSCRIPTION_OK,i,r,0,o,null);this.sendEvent(e)}else{const t=K.stripNullTerminate(e.encodedUtf8Subscription);this._sessionFSM.handleSubscriptionUpdateError(r,i,t,o,n)}}handleQueueSubscriptionUpdateResponse(e,t,n){const{LOG_TRACE:s}=this.logger;if(!e)return void n(!1,E.TIMEOUT,0,"Timeout");const r=e.getResponse(),{responseCode:i,responseString:o}=r,a=_.getADErrorSubcode(i,o);200===i||a===E.SUBSCRIPTION_ALREADY_PRESENT||a===E.SUBSCRIPTION_NOT_FOUND?n(!0,0,i,o):n(!1,a,i,o)}handleDTEUnsubscribeResponse(e,t){const n=e.getResponse(),{responseCode:s,responseString:r}=n,{correlationKey:i}=t,o=200===s?x.UNSUBSCRIBE_TE_TOPIC_OK:x.UNSUBSCRIBE_TE_TOPIC_ERROR,a=200===s?0:_.getADErrorSubcode(s,r);this.sendEvent(L.build(o,r,s,a,i))}handleSubscriptionUpdateError(e,t,n,s,r){const i=_.getErrorSubcode(e,t);if(i===E.SUBSCRIPTION_ALREADY_PRESENT&&this._sessionProperties.ignoreDuplicateSubscriptionError||i===E.SUBSCRIPTION_NOT_FOUND&&this._sessionProperties.ignoreSubscriptionNotFoundError){if(r){const n=L.build(x.SUBSCRIPTION_OK,t,e,0,s,null);this.sendEvent(n)}}else{const r=L.build(x.SUBSCRIPTION_ERROR,t,e,i,s,`Topic: ${n}`);this.sendEvent(r)}}getEventCBInfo(){return this._eventCallbackInfo}setEventCBInfo(e){this._eventCallbackInfo=e}getMessageCBInfo(){return this._messageCallbackInfo}setMessageCBInfo(e){this._messageCallbackInfo=e}getCorrelationTag(){return this._sessionFSM.getCorrelationTag()}wrapEventCallback(e){const{LOG_WARN:t}=this.logger,n=e?e.sessionEventCBFunction?e:new F(e):null;return new F(((e,s,r,i)=>{const{sessionEventCode:o}=s;if(n)try{n.sessionEventCBFunction(e,s,r,i)}catch(e){const n=Object.assign(new g(`Unhandled error in SessionEventRxCBInfo callback on sessionEventCode ${x.describe(o)}`,E.CALLBACK_ERROR,`On event: ${[o,s,r,i]} ${e}`),{stack:e.stack,info:{event:{name:o,formattedName:`SessionEventCode.${x.describe(o)}`,args:[s,r,i]},error:e}});t(n.toString(),n.info)}this.emit(o,s)}))}wrapMessageCallback(e){const{LOG_WARN:t}=this.logger,n=e?e.messageRxCBFunction?e:new C(e):null,s=`SessionEventCode.${x.describe(x.MESSAGE)}`,r=(e,t,n)=>Object.assign(new g(`Unhandled error in MessageRxCBInfo callback/handler for ${s}`,E.CALLBACK_ERROR),{stack:e.stack,info:{event:{name:x.MESSAGE,formattedName:s,args:[t,n]},error:e}});return new C(((e,s,i)=>{if(n)try{n.messageRxCBFunction(e,s,i)}catch(e){const n=r(e,s,i).toString();t(n,n.info,e)}try{this.emitDirect(s)}catch(e){this.emit("error",r(e,s,i))}}))}get adLocallyDisabled(){return!!this._adDisabledReason}get canConnectConsumer(){return!this.adLocallyDisabled&&(this._capabilities?this.isCapable(l.GUARANTEED_MESSAGE_CONSUME):void 0)}get canConnectPublisher(){return!this.adLocallyDisabled&&(this._capabilities?this.isCapable(l.GUARANTEED_MESSAGE_PUBLISH):void 0)}get disposed(){return this._disposed}inspect(){return{sessionId:this._sessionFSM&&this._sessionFSM.sessionIdHex||"(N/A)",transport:this.getTransportInfo(),state:H.describe(this.getSessionState())}}toString(){return s(this)}}},9580:(e,t,n)=>{const{TransportProtocol:s}=n(9072),{TSHState:r}=n(3718),{WebTransportCapabilities:i}=n(261);e.exports.StateStreamingAndBinary=class extends r{constructor(e,t,n){super(e,s.HTTP_BINARY_STREAMING,t,n)}validateLegal(){return i.streaming()&&i.xhrBinary()}}},9620:(e,t,n)=>{const{Destination:s}=n(5136),{DestinationFromNetwork:r}=n(1618),{DestinationType:i}=n(8805),{DestinationUtil:o}=n(617),{Parameter:a}=n(802),{Queue:c}=n(1435),{SolclientFactory:u}=n(4386),{Topic:l}=n(8335);u.createTopicDestination=u.createFactory((e=>(a.isString("topicName",e),l.createFromName(e)))),u.createTopic=u.createFactory((e=>new l(e))),u.createDurableQueueDestination=u.createFactory((e=>(a.isString("queueName",e),c.createFromLocalName(e)))),e.exports.Destination=s,e.exports.DestinationFromNetwork=r,e.exports.DestinationType=i,e.exports.DestinationUtil=o,e.exports.Queue=c,e.exports.Topic=l},9631:(e,t,n)=>{const{AbstractQueueDescriptor:s}=n(8293),{QueueAccessType:r}=n(1851),{QueueDescriptor:i}=n(8976),{QueueDescriptorValidator:o}=n(6039),{QueueDiscardBehavior:a}=n(9449),{QueuePermissions:c}=n(9489),{QueueProperties:u}=n(9304),{QueuePropertiesValidator:l}=n(719),{QueueType:h}=n(6228),{EndpointNameComplaint:p,explainInvalidEndpointName:d}=n(2764);e.exports.AbstractQueueDescriptor=s,e.exports.QueueAccessType=r,e.exports.QueueDescriptor=i,e.exports.QueueDescriptorValidator=o,e.exports.QueueDiscardBehavior=a,e.exports.QueuePermissions=c,e.exports.QueueProperties=u,e.exports.QueuePropertiesValidator=l,e.exports.QueueType=h,e.exports.EndpointNameComplaint=p,e.exports.explainInvalidEndpointName=d},9640:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.SMFClientCtrlMessageType=s.new({LOGIN:0,UPDATE:1})},9656:(e,t,n)=>{n(5837);const s=n(5898),{APIProperties:r}=n(968),{AuthenticationScheme:i}=n(3399),{Check:o}=n(802),{LOG_WARN:a}=n(2694),{SslDowngrade:c}=n(6415),u=class extends r{get transportProtocol(){return o.nothing(this._tpProtocol)?null:this._tpProtocol}set transportProtocol(e){this._tpProtocol=e}get transportDowngradeTimeoutInMsecs(){return o.nothing(this._tpDowngradeTimeout)?3e3:this._tpDowngradeTimeout}set transportDowngradeTimeoutInMsecs(e){this._tpDowngradeTimeout=e}get webTransportProtocolList(){return o.nothing(this._transportProtocolList)?null:this._transportProtocolList}set webTransportProtocolList(e){this._transportProtocolList=e}get bufferedAmountQueryIntervalInMsecs(){return o.nothing(this._bufferedAmountQueryInterval)?100:this._bufferedAmountQueryInterval}set bufferedAmountQueryIntervalInMsecs(e){this._bufferedAmountQueryInterval=e}get transportProtocolInUse(){return this._tpProtocolInUse||null}_setTransportProtocolInUse(e){this._tpProtocolInUse=e}get webTransportProtocolInUse(){return this._tpProtocolInUse||null}_setWebTransportProtocolInUse(e){this._tpProtocolInUse=e}get transportContentType(){return this._tpContentType||"text/plain"}set transportContentType(e){this._tpContentType=e}_lendsInspect(){return{bufferedAmountQueryIntervalInMsecs:this.bufferedAmountQueryIntervalInMsecs,transportContentType:this.transportContentType,transportDowngradeTimeoutInMsecs:this.transportDowngradeTimeoutInMsecs,transportProtocol:this.transportProtocol,transportProtocolInUse:this.transportProtocolInUse,webTransportProtocolList:this.webTransportProtocolList}}};e.exports.SessionProperties=class extends u{constructor(e){super(function(){const{MessagePublisherProperties:e}=s;return{_vpnNameInUse:"",_virtualRouterName:"",_p2pInboxInUse:"",_p2pInboxBase:"",_userIdentification:"",_tpProtocolInUse:null,_tpContentType:"text/plain",_publisherProperties:new e,_payloadCompressionLevel:0}}(),e)}get authenticationScheme(){return o.nothing(this._authScheme)?i.BASIC:this._authScheme}set authenticationScheme(e){this._authScheme=e}get accessToken(){return o.empty(this._accessToken)?"":this._accessToken}set accessToken(e){this._accessToken=e}get idToken(){return o.empty(this._idToken)?"":this._idToken}set idToken(e){this._idToken=e}get issuerIdentifier(){return o.empty(this._issuerIdentifier)?"":this._issuerIdentifier}set issuerIdentifier(e){o.type(e,"string")?o.empty(e)?a("Failed to set the issuer identifier because the passed string was empty or null. The passed issuer identifier must not be empty or null. Setting issuer identifier to default value empty string."):this._issuerIdentifier=e:(a("Failed to set issuer identifier because the pased value was not of type String. The passed issuer identifier must be of type String. Setting issuer identifier to default value empty string."),this._issuerIdentifier="")}get url(){return o.nothing(this._url)?"":this._url}set url(e){this._url=e}get password(){return o.nothing(this._password)?"":this._password}set password(e){this._password=e}get userName(){return o.nothing(this._userName)?"":this._userName}set userName(e){this._userName=e}get clientName(){return o.nothing(this._clientName)?"":this._clientName}set clientName(e){this._clientName=e}get applicationDescription(){return o.nothing(this._appDesc)?"":this._appDesc}set applicationDescription(e){this._appDesc=e}get vpnName(){return o.nothing(this._vpnName)?"":this._vpnName}set vpnName(e){this._vpnName=e}get vpnNameInUse(){return o.nothing(this._vpnNameInUse)?"":this._vpnNameInUse}_setVpnNameInUse(e){this._vpnNameInUse=e}get virtualRouterName(){return o.nothing(this._virtualRouterName)?"":this._virtualRouterName}_setVirtualRouterName(e){this._virtualRouterName=e}get connectTimeoutInMsecs(){return o.nothing(this._connectTimeout)?this.defaultConnectTimeoutInMsecs:this._connectTimeout}set connectTimeoutInMsecs(e){this._connectTimeout=e}get defaultConnectTimeoutInMsecs(){const{webTransportProtocolList:e,transportDowngradeTimeoutInMsecs:t}=this,n=e?e.length:1,s=n*t+(n>1?1e3:0);return Math.max(8e3,s)}get connectRetries(){return o.nothing(this._connectRetries)?20:this._connectRetries}set connectRetries(e){this._connectRetries=e}get connectRetriesPerHost(){return o.nothing(this._connectRetriesPerHost)?0:this._connectRetriesPerHost}set connectRetriesPerHost(e){this._connectRetriesPerHost=e}get reconnectRetryWaitInMsecs(){return o.nothing(this._reconnectRetryWaitInMsecs)?3e3:this._reconnectRetryWaitInMsecs}set reconnectRetryWaitInMsecs(e){this._reconnectRetryWaitInMsecs=e}get reconnectRetries(){return o.nothing(this._reconnectRetries)?20:this._reconnectRetries}set reconnectRetries(e){this._reconnectRetries=e}get generateSendTimestamps(){return!o.nothing(this._genSendTimestamps)&&this._genSendTimestamps}set generateSendTimestamps(e){this._genSendTimestamps=e}get generateReceiveTimestamps(){return!o.nothing(this._genReceiveTimestamps)&&this._genReceiveTimestamps}set generateReceiveTimestamps(e){this._genReceiveTimestamps=e}get includeSenderId(){return!o.nothing(this._includeSenderId)&&this._includeSenderId}set includeSenderId(e){this._includeSenderId=e}get generateSequenceNumber(){return!o.nothing(this._genSequenceNumber)&&this._genSequenceNumber}set generateSequenceNumber(e){this._genSequenceNumber=e}get keepAliveIntervalInMsecs(){return o.nothing(this._kaInterval)?3e3:this._kaInterval}set keepAliveIntervalInMsecs(e){this._kaInterval=e}get keepAliveIntervalsLimit(){return o.nothing(this._kaIntervalsLimit)?3:this._kaIntervalsLimit}set keepAliveIntervalsLimit(e){this._kaIntervalsLimit=e}get p2pInboxInUse(){return o.nothing(this._p2pInboxInUse)?"":this._p2pInboxInUse}_setP2pInboxInUse(e){this._p2pInboxInUse=e}get p2pInboxBase(){return o.nothing(this._p2pInboxBase)?"":this._p2pInboxBase}_setP2pInboxBase(e){this._p2pInboxBase=e}get userIdentification(){return o.nothing(this._userIdentification)?"":this._userIdentification}_setUserIdentification(e){this._userIdentification=e}get subscriberLocalPriority(){return o.nothing(this._subLocalPriority)?1:this._subLocalPriority}set subscriberLocalPriority(e){this._subLocalPriority=e}get subscriberNetworkPriority(){return o.nothing(this._subNetworkPriority)?1:this._subNetworkPriority}set subscriberNetworkPriority(e){this._subNetworkPriority=e}get ignoreDuplicateSubscriptionError(){return!!o.nothing(this._ignoreDupSubError)||this._ignoreDupSubError}set ignoreDuplicateSubscriptionError(e){this._ignoreDupSubError=e}get ignoreSubscriptionNotFoundError(){return!!o.nothing(this._ignoreSubNotFoundError)||this._ignoreSubNotFoundError}set ignoreSubscriptionNotFoundError(e){this._ignoreSubNotFoundError=e}get reapplySubscriptions(){return!o.nothing(this._reapplySubcriptions)&&this._reapplySubcriptions}set reapplySubscriptions(e){this._reapplySubcriptions=e}get publisherProperties(){return this._publisherProperties}set publisherProperties(e){const{MessagePublisherProperties:t}=s;this._publisherProperties=e instanceof t?e:new t(e)}get noLocal(){return!o.nothing(this._noLocal)&&this._noLocal}set noLocal(e){this._noLocal=e}get readTimeoutInMsecs(){return o.nothing(this._readTimeout)?1e4:this._readTimeout}set readTimeoutInMsecs(e){this._readTimeout=e}get sendBufferMaxSize(){return o.nothing(this._sendBufferMaxSize)?65536:this._sendBufferMaxSize}set sendBufferMaxSize(e){this._sendBufferMaxSize=e}get assumedMaxAdSize(){return o.nothing(this._assumedMaxAdSize)?3e7:this._assumedMaxAdSize}set assumedMaxAdSize(e){this._assumedMaxAdSize=e}get maxWebPayload(){return o.nothing(this._maxWebPayload)?1048576:this._maxWebPayload}set maxWebPayload(e){this._maxWebPayload=e}get nonHTTPTransportPropsSet(){return[].filter((e=>o.something(this[e])))}get payloadCompressionLevel(){return o.nothing(this._payloadCompressionLevel)?0:this._payloadCompressionLevel}set payloadCompressionLevel(e){this._payloadCompressionLevel=e}inspect(){return Object.assign(this._lendsInspect(),{authenticationScheme:i.describe(this.authenticationScheme),accessToken:this.accessToken?"*****":"Not Set",idToken:this.idToken?"*****":"Not Set",issuerIdentifier:this.issuerIdentifier?"*****":"Not Set",url:this.url,password:this.password?"*****":this.password,userName:this.userName,clientName:this.clientName,applicationDescription:this.applicationDescription,vpnName:this.vpnName,vpnNameInUse:this.vpnNameInUse,virtualRouterName:this.virtualRouterName,connectTimeoutInMsecs:this.connectTimeoutInMsecs,connectRetries:this.connectRetries,connectRetriesPerHost:this.connectRetriesPerHost,reconnectRetryWaitInMsecs:this.reconnectRetryWaitInMsecs,reconnectRetries:this.reconnectRetries,generateSendTimestamps:this.generateSendTimestamps,generateReceiveTimestamps:this.generateReceiveTimestamps,includeSenderId:this.includeSenderId,generateSequenceNumber:this.generateSequenceNumber,keepAliveIntervalInMsecs:this.keepAliveIntervalInMsecs,keepAliveIntervalsLimit:this.keepAliveIntervalsLimit,p2pInboxInUse:this.p2pInboxInUse,p2pInboxBase:this.p2pInboxBase,userIdentification:this.userIdentification,subscriberLocalPriority:this.subscriberLocalPriority,subscriberNetworkPriority:this.subscriberNetworkPriority,ignoreDuplicateSubscriptionError:this.ignoreDuplicateSubscriptionError,reapplySubscriptions:this.reapplySubscriptions,publisherProperties:this.publisherProperties,noLocal:this.noLocal,readTimeoutInMsecs:this.readTimeoutInMsecs,sendBufferMaxSize:this.sendBufferMaxSize,maxWebPayload:this.maxWebPayload,payloadCompressionLevel:this.payloadCompressionLevel})}toString(){return super.toString()}}},9685:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.SMFClientCtrlAuthType=s.new({CLIENT_CERTIFICATE:"",OAUTH2:"\n"}),e.exports.SMFClientCtrlParam=s.new({SOFTWAREVERSION:0,SOFTWAREDATE:1,PLATFORM:2,USERID:3,CLIENTDESC:4,CLIENTNAME:5,MSGVPNNAME:6,DELIVERTOONEPRIORITY:7,P2PTOPIC:8,ROUTER_CAPABILITIES:9,VRIDNAME:10,PHYSICALROUTERNAME:12,BRIDGE_MSG_VPN_NAME:13,BRIDGE_ROUTER_NAME:14,NO_LOCAL:15,BRIDGE_VERSION:16,AUTHENTICATION_SCHEME:17,CONNECTION_TYPE:18,ROUTER_CAPABILITIES_EXTENDED:19,REQUIRES_RELEASE_7:20,SSL_DOWNGRADE:21,CLIENT_CAPABILITIES:23,KEEP_ALIVE_INTERVAL:24})},9692:(e,t,n)=>{var s=n(8287).hp;const r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",i=[-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,99,-1,-1,99,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,99,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,62,-1,-1,-1,63,52,53,54,55,56,57,58,59,60,61,-1,-1,-1,64,-1,-1,-1,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,-1,-1,-1,-1,-1,-1,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1];class o extends Error{}const a={base64_encode(e){let t="",n=0;do{const s=e.charCodeAt(n++),i=e.charCodeAt(n++),o=e.charCodeAt(n++),a=s>>2,c=(3&s)<<4|i>>4;let u=(15&i)<<2|o>>6,l=63&o;isNaN(i)?u=l=64:isNaN(o)&&(l=64),t+=r.charAt(a),t+=r.charAt(c),t+=r.charAt(u),t+=r.charAt(l)}while(n<e.length);return t},base64_decode(e){let t="",n=0;do{for(;i[e.charCodeAt(n)]>64;)n++;const s=i[e.charCodeAt(n++)],r=i[e.charCodeAt(n++)],a=i[e.charCodeAt(n++)],c=i[e.charCodeAt(n++)];if(s<0||r<0||a<0||c<0)throw new o("Invalid base64 character");const u=s<<2|r>>4,l=(15&r)<<4|a>>2,h=(3&a)<<6|c;t+=String.fromCharCode(u),64!==a&&(t+=String.fromCharCode(l)),64!==c&&(t+=String.fromCharCode(h))}while(n<e.length-3);return t}},c="undefined"==typeof window,u="undefined"!=typeof Blob,l=void 0!==s&&(u||c)?{base64_encode:e=>s.from(e,"binary").toString("base64"),base64_decode:e=>s.from(e,"base64").toString("binary")}:{},h="undefined"!=typeof window?{base64_encode:window.btoa?e=>window.btoa(e):null,base64_decode:window.atob?e=>window.atob(e):null}:{},p={encode:h.base64_encode||l.base64_encode||a.base64_encode,decode:h.base64_decode||l.base64_decode||a.base64_decode};e.exports.Base64=p},9710:e=>{e.exports.StringBuffer=class{constructor(...e){this.clear(),this.append(...e)}append(...e){return[...e].forEach((e=>{this.buffer[this.index++]=String(e)})),this}clear(){this.buffer=[],this.index=0}toString(){return this.buffer.join("")}}},9727:(e,t,n)=>{const s=n(5898),r=(n(9656),n(3450)),{APIPropertiesValidators:i,parseURL:o}=n(968),{AuthenticationScheme:a}=n(3399),{Check:c}=n(802),{ErrorSubcode:u,OperationError:l}=n(6706),{LOG_WARN:h,LOG_INFO:p}=n(2694),{SslDowngrade:d}=n(6415),{TransportProtocol:_}=n(8205),{validateInstance:E,valArrayIsMember:g,valArrayOfString:T,valBoolean:S,valLength:m,valNotEmpty:f,valNumber:I,valRange:R,valString:C,valStringOrArray:A}=i,O=["http:","https:","ws:","wss:","tcp:","tcps:"];function N(e,t,n){const s=r.ClientCtrlMessage.validateClientName(t[n],(t=>new l(`${e} validation: Property '${n}': ${t}`,u.PARAMETER_OUT_OF_RANGE)));if(s)throw s}function y(e,t,n,s,r,i){const o=t[n];if(!(i&&null===o||void 0===o||s.values.some((e=>e===o))))throw new l(`${e} validation: Property '${n}' must be a member of ${r}`,u.PARAMETER_INVALID_TYPE)}function P(e,t,n){const s=t[n],r="string"==typeof s?s.split(","):s;if(!c.array(r))throw new l(`${e} validation: Property '${n}' not an array or comma-delimited string`,u.PARAMETER_INVALID_TYPE);r.forEach((t=>{let s=null;try{s=o(t)}catch(s){throw new l(`${e} validation: Property '${n}' contained an invalid URL: ${t}`,u.PARAMETER_OUT_OF_RANGE)}if(!c.included(s.protocol,O))throw new l(`${e} validation: Property '${n}' contained a URL'${s.href}' with an invalid protocol: '${s.protocol}'`,u.PARAMETER_OUT_OF_RANGE)}))}function D(e,t,n){return function(e,t,n,s){const r=e[t];if(r instanceof Array){const e=r.length;for(let t=0;t<e;t++){const e=r[t];if(s){if(s&&(!c.string(e)||!e.match(n)))return!1}else if(c.string(e)&&e.match(n))return!0}if(!s)return!1;if(s)return!0}return c.string(r)&&r.match(n)}(e,t,/^(https|wss|tcps):/i,n)}const b={validate(e){const t=E.bind(null,"SessionProperties",e);if(t("url",[f],[A],[P]),t("userName",[C],[m,189]),t("password",[C],[m,128]),t("clientName",[C],[m,160],[N]),t("applicationDescription",[C],[m,254]),t("vpnName",[C],[m,32]),t("connectTimeoutInMsecs",[I],[R,1,Number.MAX_VALUE]),t("connectRetriesPerHost",[I],[R,-1,Number.MAX_VALUE]),t("connectRetries",[I],[R,-1,Number.MAX_VALUE]),t("reconnectRetries",[I],[R,-1,Number.MAX_VALUE]),t("reconnectRetryWaitInMsecs",[I],[R,0,6e4]),t("readTimeoutInMsecs",[I],[R,1,Number.MAX_VALUE]),t("sendBufferMaxSize",[I],[R,1,Number.MAX_VALUE]),t("maxWebPayload",[I],[R,100,Number.MAX_VALUE]),t("bufferedAmountQueryIntervalInMsecs",[I],[R,4,Number.MAX_VALUE]),t("generateSendTimestamps",[S]),t("generateReceiveTimestamps",[S]),t("includeSenderId",[S]),t("keepAliveIntervalInMsecs",[I],[R,0,Number.MAX_VALUE]),t("keepAliveIntervalsLimit",[I],[R,3,Number.MAX_VALUE]),t("generateSequenceNumber",[S]),t("subscriberLocalPriority",[I],[R,1,4]),t("subscriberNetworkPriority",[I],[R,1,4]),t("ignoreDuplicateSubscriptionError",[S]),t("ignoreSubscriptionNotFoundError",[S]),t("reapplySubscriptions",[S]),t("noLocal",[S]),t("transportDowngradeTimeoutInMsecs",[I],[R,1,Number.MAX_VALUE]),t("idToken",[C]),t("accessToken",[C]),t("payloadCompressionLevel",[I],[R,0,9]),e.transportProtocol&&e.webTransportProtocolList)throw new l("SessionProperties validation: Property 'transportProtocol' and 'webTransportProtocolList' cannot be set at the same time",u.PARAMETER_OUT_OF_RANGE);if(null!==e.webTransportProtocolList&&void 0!==e.webTransportProtocolList){if(!Array.isArray(e.webTransportProtocolList))throw new l("Property 'webTransportProtocolList' must be an array if set",u.PARAMETER_INVALID_TYPE);if(0===e.webTransportProtocolList.length)throw new l("Property 'webTransportProtocolList' must be non-empty if set",u.PARAMETER_OUT_OF_RANGE)}t("authenticationScheme",[y,a,"AuthenticationScheme",!1]);const n=e.authenticationScheme===a.CLIENT_CERTIFICATE;if(!D(e,"url",!0)&&n)throw new l("SessionProperties validation: Property 'authenticationScheme' cannot be set to client certificate for unsecured sessions",u.PARAMETER_OUT_OF_RANGE);if(c.equal(e.authenticationScheme,a.OAUTH2)){if(!D(e,"url",!0))throw new l(`SessionProperties validation: Property 'authenticationScheme' cannot be set to '${a.OAUTH2}' unless the session property 'url' is written to use a secure communication protocol like tcps or https.`,u.PARAMETER_CONFLICT);if(c.empty(e.idToken)&&c.empty(e.accessToken))throw new l(`SessionProperties validation: Property 'authenticationScheme' can be set to '${a.OAUTH2}' only if there is an accompanying token set as a session property. The token types that are supported for OAuth authentication are OAuth2.0 Access Tokens and OpenID Connect ID Tokens. To set an access token you can use the accessToken session property. To set an id you can use the idToken session property.`,u.PARAMETER_CONFLICT)}else c.empty(e.idToken)&&c.empty(e.accessToken)||p(`SessionProperties validation: Property 'authenticationScheme' must be set to '${a.OAUTH2}'in order to use either  an OAUTH2 access token or an OpenID Connect ID token.`);t("transportProtocol",[y,_,"TransportProtocol",!0]),t("webTransportProtocolList",[g,_,"TransportProtocol",!0,!1,!1]),function(e,t){if(t.length>0&&function(e){return e&&(e===_.HTTP_BINARY_STREAMING||e===_.HTTP_BINARY||e===_.HTTP_BASE64)}(e)){const n=t.length<=5?t:t.slice(0,5);throw new l(`SessionProperties validation: properties that are not supported by transport protocol ${e} have been set: ${n}`,u.PARAMETER_OUT_OF_RANGE)}}(e.transportProtocol,e.nonHTTPTransportPropsSet),e.publisherProperties&&s.MessagePublisherPropertiesValidator.validate(e.publisherProperties);const r=e.defaultConnectTimeoutInMsecs,i=e.connectTimeoutInMsecs;(e.webTransportProtocolList?e.webTransportProtocolList.length:1)>1&&i<r&&h(`Connect timeout of ${i} msecs is less than default and recommended minimum of ${r} msecs for current transport selection. Transport downgrades may not complete.`)}};e.exports.SessionPropertiesValidator=b},9728:(e,t,n)=>{const{FsmEvent:s}=n(7414);e.exports.PublisherFSMEvent=class extends s{constructor(e,t,n){super(e),Object.assign(this,t),Object.assign(this,n)}getEventText(){return this._eventText}}},9731:e=>{e.exports.SMFHeader=class{constructor(e=0,t=0){this._parameters=[],this.smf_version=3,this.smf_uh=0,this.smf_protocol=e,this.smf_priority=0,this.smf_ttl=t,this.smf_msgLen=0,this.smf_di=0,this.smf_tqd=0,this.smf_elidingEligible=0,this.smf_dto=0,this.smf_adf=0,this.smf_deadMessageQueueEligible=0,this.pm_userdata=null,this.pm_respcode=0,this.pm_respstr=null,this.pm_username=null,this.pm_password=null,this.pm_tr_topicname_bytes=null,this.pm_deliverymode=null,this.pm_ad_msgid=void 0,this.pm_ad_prevmsgid=void 0,this.pm_ad_redelflag=0,this.pm_ad_flowredelflag=0,this.pm_ad_ttl=void 0,this.pm_ad_ackimm=void 0,this.pm_ad_flowid=0,this.pm_ad_publisherid=0,this.pm_ad_publishermsgid=0,this.pm_content_summary=null,this.pm_corrtag=null,this.pm_topic_offset=0,this.pm_topic_len=0,this.pm_queue_offset=0,this.pm_queue_len=0,this.pm_msg_priority=null,this.pm_oauth2_access_token=null,this.pm_oidc_id_token=null,this.pm_oauth2_issuer_identifier=null,this.pm_ts_transport_context=null,this.unknownProtoFlag=!1,this.messageLength=0,this.payloadLength=0,this.headerLength=0,this.payload=null,this.discardMessage=!1}setMessageSizes(e,t){this.headerLength=e,this.payloadLength=t,this.messageLength=e+t}setPayloadSize(e){this.payloadLength=e}}},9747:(e,t,n)=>{const{Enum:s}=n(7444);e.exports.PrivateFlowEventName=s.new({BIND_WAITING:"PrivateFlowEventName_bindWaiting"})},9783:(e,t,n)=>{const{Base64:s}=n(9692),{Bits:r}=n(9887),{Convert:i}=n(8076),{Hex:o}=n(8926),{Long:a}=n(7753);e.exports.Base64=s,e.exports.Bits=r,e.exports.Convert=i,e.exports.Hex=o,e.exports.Long=a},9812:(e,t,n)=>{const{BaseMessage:s}=n(8668),{Convert:r,Long:i}=n(9783),{DestinationType:o}=n(9620),{LOG_TRACE:a,LOG_INFO:c}=n(2694),{OperationError:u}=n(6706),{QueueAccessType:l,QueueDiscardBehavior:h}=n(9631),{QueuePermissions:p,QueueType:d}=n(9631),{ReplayStartType:_}=n(9309),{MessageOutcome:E}=n(6247),{SMFAdProtocolMessageType:g}=n(7250),{SMFAdProtocolParam:T}=n(5099),{SMFHeader:S}=n(9731),{SMFParameter:m}=n(1123),{SMFProtocol:f}=n(5052),{SMFUH:I}=n(8379),{StringUtils:R}=n(968),C=n(8287).hp,{strToInt8:A,strToInt16:O,strToUInt32:N,strToUInt64:y}=r,P=C.prototype.readUInt8,D=C.prototype.readUInt16BE,b=C.prototype.readUInt32BE,M=function(e){return i.fromBits(this.readUInt32BE(e+4),this.readUInt32BE(e),!0)},{nullTerminate:v,stripNullTerminate:w}=R,L={[o.TOPIC]:T.DTENAME,[o.QUEUE]:T.QUEUENAME},U={[d.TOPIC_ENDPOINT]:T.DTENAME,[d.QUEUE]:T.QUEUENAME},F={[p.NONE]:0,[p.READ_ONLY]:1,[p.CONSUME]:3,[p.MODIFY_TOPIC]:7,[p.DELETE]:15},x={1:l.EXCLUSIVE,2:l.NONEXCLUSIVE},B={[l.EXCLUSIVE]:1,[l.NONEXCLUSIVE]:2},G={1:!0,2:!1,3:!1},k={[h.NOTIFY_SENDER_OFF]:1,[h.NOTIFY_SENDER_ON]:2};function W(e,t,n=!1){if(!t)return;const{accessType:s,discardBehavior:r,maxMessageRedelivery:i,maxMessageSize:o,permissions:a,quotaMB:c,respectsTTL:u}=t;a&&void 0!==F[a]&&e.addParameter(new m(I.IGNORE,T.EP_ALLOTHER_PERMISSION,F[a])),n||void 0===s||void 0===B[s]||e.addParameter(new m(I.IGNORE,T.ACCESSTYPE,B[s])),null!=c&&e.addParameter(new m(I.IGNORE,T.EP_QUOTA,c)),null!=o&&e.addParameter(new m(I.IGNORE,T.EP_MAX_MSGSIZE,o));let l=0;null!=r&&(l|=k[r]<<12),l&&e.addParameter(new m(I.IGNORE,T.EP_BEHAVIOUR,l)),null!=i&&e.addParameter(new m(I.IGNORE,T.MAX_REDELIVERY,i)),null!=u&&e.addParameter(new m(I.IGNORE,T.EP_RESPECTS_TTL,u?1:0))}class $ extends s{constructor(e=0,t=3){super(new S(f.ADCTRL,1)),this.msgType=e,this.version=t}_readParameter(e,t=null,n=null){const s=this.getParameter(e);if(void 0===s)return;if(n&&s.getBuffer())return n.call(s.getBuffer(),s.getBegin());const r=s.getValue();return t?t(r):r}getAccessType(){const e=this._readParameter(T.ACCESSTYPE,A,P);return x[e]}getDurability(){const e=this._readParameter(T.EP_DURABLE,A,P);return 3===e&&c("Non durable reliable queue. This does not happen often."),G[e]}getActiveFlow(){return this._readParameter(T.ACTIVE_FLOW_INDICATION,A,P)}getQueueDiscardBehavior(){const e=this._readParameter(T.EP_BEHAVIOUR,O,D);if(void 0===e)return;const t=(12288&e)>>12;return t===k[h.NOTIFY_SENDER_OFF]?h.NOTIFY_SENDER_OFF:t===k[h.NOTIFY_SENDER_ON]?h.NOTIFY_SENDER_ON:void 0}getEndpointDeliveryCountSent(){switch((3072&this._readParameter(T.EP_BEHAVIOUR,O,D))>>10){case 0:default:return;case 1:return!1;case 2:return!0}}getEndpointId(){return this._readParameter(T.ENDPOINT_ID,N,b)}getRespectsTTL(){const e=this._readParameter(T.EP_RESPECTS_TTL,A,P);if(void 0!==e)return!!e}getFlowName(){return this._readParameter(T.FLOWNAME,w)}getFlowId(){return this._readParameter(T.FLOWID,N,b)}getQuota(){return this._readParameter(T.EP_QUOTA,N,b)}getMaxMsgSize(){return this._readParameter(T.EP_MAX_MSGSIZE,N,b)}getTopicEndpointBytes(){return this._readParameter(T.DTENAME)}getQueueNameBytes(){return this._readParameter(T.QUEUENAME)}getGrantedPermissions(){const e=this._readParameter(T.GRANTED_PERMISSIONS,N,b);let t;return Object.keys(F).forEach((n=>{F[n]===e&&(t=n)})),t}getAllOthersPermissions(){const e=this._readParameter(T.EP_ALLOTHER_PERMISSION,N,b);let t;return Object.keys(F).forEach((n=>{F[n]===e&&(t=n)})),t}getLastMsgIdAcked(){return this._readParameter(T.LASTMSGIDACKED,y,M)}getLastMsgIdReceived(){return this._readParameter(T.LASTMSGIDRECEIVED,y,M)}getPublisherId(){return this._readParameter(T.PUBLISHER_ID,N,b)}getWantFlowChangeNotify(){return!!this._readParameter(T.WANT_FLOW_CHANGE_NOTIFY,A,P)}getWindow(){return this._readParameter(T.WINDOW,A,P)}getMaxRedelivery(){return this._readParameter(T.MAX_REDELIVERY,A,P)}getMaxUnackedMessages(){return this._readParameter(T.MAX_DELIVERED_UNACKED_MESSAGES_PER_FLOW,N,b)}getEndpointErrorId(){return this._readParameter(T.ENDPOINT_ERROR_ID,y,M)}getPartitionGroupId(){return this._readParameter(T.PARTITION_GROUP_ID,O,D)}getSpoolerUniqueId(){return this._readParameter(T.SPOOLER_UNIQUE_ID,y,M)}static getCloseMessagePublisher(e,t){const n=new $(g.CLOSEPUBFLOW);return n.smfHeader.pm_corrtag=t,n.addParameter(new m(I.REJECT,T.FLOWID,e)),n}static getCreate(e,t,n){const s=new $(g.CREATE);s.smfHeader.pm_corrtag=n;const r=U[e.type];if(void 0===r)throw new u("Unknown destination type");return s.addParameter(new m(I.REJECT,r,v(e.name))),s.addParameter(new m(I.IGNORE,T.EP_DURABLE,e.durable?1:2)),W(s,t),s}static getDelete(e,t){const n=new $(g.DELETE);n.smfHeader.pm_corrtag=t;const s=U[e.type];if(void 0===s)throw new u("Unknown destination type");return n.addParameter(new m(I.REJECT,s,v(e.name))),n}static getOpenMessagePublisher(e,t,n,s,r){const i=new $(g.OPENPUBFLOW);return i.smfHeader.pm_corrtag=r,void 0!==e&&i.addParameter(new m(I.REJECT,T.LASTMSGIDACKED,e)),void 0!==t&&i.addParameter(new m(I.REJECT,T.LASTMSGIDSENT,t)),i.addParameter(new m(I.REJECT,T.WINDOW,n)),i.addParameter(new m(I.IGNORE,T.FLOWNAME,s||"")),i}static getOpenMessageConsumer(e,t,n,s,r,a,c,l,h=i.UZERO,p=i.UZERO,d=!1,E=void 0,S=void 0,f=void 0,R=!1){const C=e.durable,A=n.bytes,O=n.type,N=new $(g.BIND);N.smfHeader.pm_corrtag=r;const y=L[O];if(void 0===y)throw new u("Unknown destination type");if(N.addParameter(new m(I.REJECT,y,A)),s&&N.addParameter(new m(I.REJECT,T.TOPICNAME,s.bytes)),O===o.QUEUE&&(N.addParameter(new m(I.REJECT,T.LASTMSGIDACKED,h)),N.addParameter(new m(I.IGNORE,T.LASTMSGIDRECEIVED,p))),N.addParameter(new m(I.REJECT,T.WINDOW,a)),N.addParameter(new m(I.IGNORE,T.EP_DURABLE,C)),W(N,t,!0),c&&N.addParameter(new m(I.REJECT,T.NOLOCAL,1)),l&&N.addParameter(new m(I.IGNORE,T.WANT_FLOW_CHANGE_NOTIFY,1)),R?N.addParameter(new m(I.REJECT,T.FLOWTYPE,3)):d&&N.addParameter(new m(I.REJECT,T.FLOWTYPE,2)),void 0!==E){let e=E._replayStartValue;E._type===_.DATE&&(e=i.fromNumber(E._replayStartValue,!0).multiply(1e6)),N.addParameter(new m(I.REJECT,T.REPLAY_START_LOCATION,{type:E._type,value:e}))}return void 0!==S&&N.addParameter(new m(I.IGNORE,T.ENDPOINT_ERROR_ID,S)),null!=f&&N.addParameter(new m(I.IGNORE,T.PARTITION_GROUP_ID,f)),N}static getCloseMessageConsumer(e,t){const n=new $(g.UNBIND);return n.smfHeader.pm_corrtag=t,n.addParameter(new m(I.REJECT,T.FLOWID,e)),n}static getDTEUnsubscribeMessage(e,t){const n=new $(g.UNSUBSCRIBE);return n.smfHeader.pm_corrtag=e,n.addParameter(new m(I.REJECT,T.DTENAME,t.getBytes())),n}static getAck(e,t=void 0,n=void 0,s=void 0){const r=new $(g.CLIENTACK);if(r.addParameter(new m(I.REJECT,T.FLOWID,e)),t&&r.addParameter(new m(I.REJECT,T.LASTMSGIDACKED,t)),null!=n&&r.addParameter(new m(I.REJECT,n<=255?T.WINDOW:T.TRANSPORT_WINDOW,n)),s&&s.size>0){let e=0;const t=E.values;for(let n=0;n<t.length;n++)e+=s.has(t[n])?s.get(t[n]).length:0;if(e>$.MAX_CLIENT_ACK_RANGES)throw new u("Application ack range count exceeds limit of 64");r.addParameter(new m(I.REJECT,T.APPLICATION_ACK,s))}return r}static getUnbindAck(e,t=void 0,n=void 0){const s=new $(g.UNBIND);return s.addParameter(new m(I.REJECT,T.FLOWID,e)),t&&s.addParameter(new m(I.IGNORE,T.ENDPOINT_ERROR_ID,t)),s}}$.MAX_CLIENT_ACK_RANGES=64,e.exports.AdProtocolMessage=$},9887:e=>{const t={get:(e,t,n)=>e>>>t&(1<<n)-1,set(e,t,n,s){const r=(1<<s)-1;return e&~(r<<n)|(t&r)<<n}};e.exports.Bits=t},9944:(e,t,n)=>{const{Enum:s}=n(7444),r={OK:0,FAIL:1,NO_SPACE:2,DATA_DECODE_ERROR:3,INVALID_STATE_FOR_OPERATION:4,CONNECTION_ERROR:5};e.exports.TransportReturnCode=s.new(r),e.exports.TransportReturnCode._setCanonical({OK:r.OK,FAIL:r.FAIL,NO_SPACE:r.NO_SPACE,DATA_DECODE_ERROR:r.DATA_DECODE_ERROR,INVALID_STATE_FOR_OPERATION:r.INVALID_STATE_FOR_OPERATION,CONNECTION_ERROR:r.CONNECTION_ERROR})},9963:(e,t,n)=>{const{Bits:s,Convert:r}=n(9783),{LOG_DEBUG:i,LOG_TRACE:o}=n(2694),{SMFSMPMessageType:a}=n(3647),{SMPMessage:c}=n(8247),{get:u,set:l}=s,{int8ToStr:h,int32ToStr:p}=r,d={parseSMPAt:function(e,t){if(t+6>e.length)return!1;let n=t;const s=e.readUInt8(n);n++;const r=u(s,0,7),i=new c;if(r!==a.ADDSUBSCRIPTION&&r!==a.REMSUBSCRIPTION&&r!==a.ADDQUEUESUBSCRIPTION&&r!==a.REMQUEUESUBSCRIPTION)return!1;e.toString("latin1");const o=e.readUInt32BE(n);if(n+=4,t+o>e.length)return!1;const l=e.readUInt8(n);if(n++,i.msgType=r,i.smpFlags=l,r===a.ADDSUBSCRIPTION||r===a.REMSUBSCRIPTION)i.encodedUtf8Subscription=e.toString("latin1",n,n+o-6);else{const t=e.readUInt8(n);n++,i.encodedUtf8QueueName=e.toString("latin1",n,n+t),n+=t;const s=e.readUInt8(n);n++,i.encodedUtf8Subscription=e.toString("latin1",n,n+s),n+=s}return i},encSmp:function(e){if(e.msgType!==a.ADDSUBSCRIPTION&&e.msgType!==a.REMSUBSCRIPTION&&e.msgType!==a.ADDQUEUESUBSCRIPTION&&e.msgType!==a.REMQUEUESUBSCRIPTION)return!1;const t=[];let n=0;n=l(n,1,7,1),n=l(n,e.msgType,0,7),t.push(h(n));let s=6+e.encodedUtf8Subscription.length;return e.msgType!==a.ADDQUEUESUBSCRIPTION&&e.msgType!==a.REMQUEUESUBSCRIPTION||(s+=2+e.encodedUtf8QueueName.length),t.push(p(s)),t.push(h(e.smpFlags)),e.msgType===a.ADDQUEUESUBSCRIPTION||e.msgType===a.REMQUEUESUBSCRIPTION?(t.push(h(e.encodedUtf8QueueName.length)),t.push(e.encodedUtf8QueueName),t.push(h(e.encodedUtf8Subscription.length)),t.push(e.encodedUtf8Subscription)):t.push(e.encodedUtf8Subscription),t.join("")}};e.exports.SMP=d},9984:(e,t,n)=>{const{WebTransportCapabilities:s}=n(9556),r={web:s};e.exports.TransportCapabilities=r},9990:(e,t,n)=>{const{LOG_TRACE:s,LOG_DEBUG:r,LOG_INFO:i,LOG_WARN:o,LOG_ERROR:a}=n(2694),{ArrayUtils:c}=n(968),{Convert:u,Hex:l}=n(9783),{ErrorSubcode:h}=n(6706),{mixin:p}=n(7444),{TransportError:d}=n(2680),{TransportReturnCode:_}=n(9944),{TransportSessionEvent:E}=n(7368),{TransportSessionEventCode:g}=n(3427),{TransportSessionState:T}=n(3304),{WebSocketCloseCodes:S}=n(5181),{WebTransportSessionBase:m}=n(1517),{stringToArrayBuffer:f}=(n(886),n(7625),u),{formatHexString:I}=l,{includes:R}=c;let C=("undefined"==typeof window?n.g:window).WebSocket;class A extends m{constructor(e,t,n,s){super(e,t,n,s),this._url=function(e){return`ws${e.match(/(ws|http)(s?:\/\/.+)/)[2]}`}(e),this._socket=null,this._sessionId=(new Date).getTime(),this._bufferedAmountQueryIntervalInMsecs=s.bufferedAmountQueryIntervalInMsecs,this._bufferedAmountQueryTimer=null,this._bufferedAmountQueryIntervalDelayMultiplier=1}onOpen(){this.cancelConnectTimeout(),this._state=T.SESSION_UP,this._eventCB(new E(g.UP_NOTICE,"Connected",0,null,this._sessionId))}onClose(e,t){if(e!==this._socket)return;if(this._state===T.WAITING_FOR_DESTROY)return;const n=[],s=S[t.code]||S[0];n.push(`${t.code} ${s.name} (${s.description})`),void 0!==t.wasClean&&n.push(`clean closure: ${t.wasClean}`),t.reason&&n.push(`reason: ${t.reason}`);const r=n.join(", ");t.type,t.wasClean,t.code,t.reason,this._state=T.CONNECTION_FAILED,this.destroy(`Connection closed: ${r}`,h.COMMUNICATION_ERROR)}onDrain(){this.maybeEmitCanSend(),this.maybeEmitFlush()}onBufferedAmountPoll(){0===this.getBufferedAmount()?this.onDrain():this.scheduleQuery&&this.scheduleQuery()}onError(e,t){if(i(`Websocket Transport Session onError for socket ${e} while socket is ${this._socket}`),e!==this._socket)return void i("Websocket Transport Session stray onError for previous socket, ignoring.");if(this._state===T.WAITING_FOR_DESTROY)return void i("WebSocket transport is being destroyed, ignore error");const n=t.message?`: ${t.message}`:"";i(`WebSocket transport connection error ${n} while in state ${this._state}`),this._state===T.WAITING_FOR_CONNECT?(this.cancelConnectTimeout(),this._state=T.CONNECTION_FAILED,this.destroy(`Connection failed: ${n}`,h.CONNECTION_ERROR)):this._eventCB(new E(g.SEND_ERROR,`Connection error${n}`,null,h.CONNECTION_ERROR,null))}onMessage(e){this._client&&this._client.rxDataArrayBuffer(e.data)}connectTimerExpiry(){i("WebSocket transport connect timeout"),this.state=T.CONNECTION_FAILED,this._eventCB(new E(g.CONNECT_TIMEOUT,"Connection timed out",null,h.TIMEOUT))}connect(){if(this._state!==T.DOWN)return a(`Invalid state for operation: ${T.nameOf(this._state)}`),_.INVALID_STATE_FOR_OPERATION;if(!this._url)return o("Cannot connect to null URL"),_.CONNECTION_ERROR;this._socket&&this.onError("Socket already connected"),i("Establishing WebSocket transport session");try{this.createConnectTimeout(),this._state=T.WAITING_FOR_CREATE,i("Constructing socket"),this._socket=new C(this._url,"smf.solacesystems.com"),this._socket.binaryType="arraybuffer",this._socket.onopen=this.onOpen.bind(this),this._socket.onmessage=this.onMessage.bind(this),this._socket.onclose=this.onClose.bind(this,this._socket),this._socket.onerror=this.onError.bind(this,this._socket)}catch(e){if(i(`Error connecting: ${e.message}`),e.stack,this._state=T.CONNECTION_FAILED,this.cancelConnectTimeout(),!(e instanceof d))throw new d(`Could not create WebSocket: ${e.message}`,e.subcode||h.CONNECTION_ERROR);return this._connError=e,_.CONNECTION_ERROR}return i("WebSocket is connecting"),_.OK}send(e,t=!1){if(this._state!==T.SESSION_UP)return _.INVALID_STATE_FOR_OPERATION;const n=e.length,s=this._sendBufferMaxSize-this.getBufferedAmount()>=0;if(!t&&!s)return this._canSendNeeded=!0,this.scheduleQuery&&this.scheduleQuery(),_.NO_SPACE;const r=this._maxPayloadBytes,i=f(e);if(n>r)for(let e=0;e<n;e+=r)this._socket.send(i.slice(e,e+r));else this._socket.send(i);return this._clientstats.bytesWritten+=n,++this._clientstats.msgWritten,_.OK}getBufferedAmount(){return this._socket?this._socket.bufferedAmount:0}flush(e){this._flushCallback=e,this.maybeEmitFlush()}maybeEmitCanSend(){this._canSendNeeded&&this.getBufferedAmount()<this._sendBufferMaxSize&&(this._canSendNeeded=!1,this._eventCB(new E(g.CAN_ACCEPT_DATA,"",null,0,this._sessionId)))}maybeEmitFlush(){if(!this._flushCallback)return;if(this.getBufferedAmount()>0)return void(this._bufferedAmountQueryTimer||this.scheduleQuery());const e=this._flushCallback;this._flushCallback=null,e()}destroy(e,t){return this._state!==T.DOWN&&(i(`Destroy WebSocket transport: ${e}`),this._state=T.WAITING_FOR_DESTROY,this._socket&&(this._socket.close(),this._socket.onopen=null,this._socket.onmessage=null,this._socket.onclose=null,this._socket.onerror=function(){},this._socket=null),this._connectTimer&&(clearTimeout(this._connectTimer),this._connectTimer=void 0),this.cancelQuery(),this._bufferedAmountQueryIntervalDelayMultiplier=1,this._canSendNeeded=!1,this._state=T.DOWN,this._client=null),this._eventCB&&(this._eventCB(new E(g.DESTROYED_NOTICE,e||"Session is destroyed",null,t||0,this._sessionId)),this._eventCB=null),_.OK}getInfoStr(){return`WebSocketTransportSession; sid=${I(this._sessionId)}`}static browserSupportsBinaryWebSockets(){const e=["function","object"];return R(e,typeof C)&&R(e,typeof ArrayBuffer)&&R(e,typeof Uint8Array)?"binaryType"in C.prototype?(i("websocket browserSupportBinaryCheck: true - WebSocket supports binaryType"),!0):(i("websocket browserSupportBinaryCheck: false - WebSocket does not support binaryType"),!1):(i("websocket browserSupportBinaryCheck: false - some required classes not supported"),!1)}}p(A,class{scheduleQuery(){if(this.getBufferedAmount()>0&&this._bufferedAmountQueryIntervalInMsecs>0){this.cancelQuery(),this._bufferedAmountQueryIntervalDelayMultiplier>1&&(this._bufferedAmountQueryIntervalInMsecs,this._bufferedAmountQueryIntervalDelayMultiplier);const e=this._bufferedAmountQueryIntervalInMsecs*this._bufferedAmountQueryIntervalDelayMultiplier;this._bufferedAmountQueryTimer=setTimeout((()=>{this.cancelQuery();try{this.onBufferedAmountPoll()}catch(e){a(`Error occurred in onBufferedAmountPoll: ${e.message}`),e.stack}}),e)}}cancelQuery(){this._bufferedAmountQueryTimer&&(clearTimeout(this._bufferedAmountQueryTimer),this._bufferedAmountQueryTimer=null)}}),e.exports.WebSocketTransportSession=A}},t={};function n(s){var r=t[s];if(void 0!==r)return r.exports;var i=t[s]={exports:{}};return e[s].call(i.exports,i,i.exports,n),i.exports}return n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n(5237)})()));
//# sourceMappingURL=solclient.js.map