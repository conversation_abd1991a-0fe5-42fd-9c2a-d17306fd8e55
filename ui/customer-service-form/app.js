/**
 * Solace Event Publisher with First Abu Dhabi Bank-styled frontend (Node.js version)
 *
 * This application consists of:
 * 1. A Node.js Express backend that publishes events to a Solace topic
 * 2. A HTML/CSS/JS frontend resembling First Abu Dhabi Bank's styling with official logo
 * 3. JSON payload formatting for the events
 *
 * Requirements:
 * - Node.js and npm
 * - Express (npm install express)
 * - Solclientjs (npm install solclientjs)
 * - Body-parser (npm install body-parser)
 * - UUID (npm install uuid)
 */

// Package.json file content:
/*
{
  "name": "fab-solace-publisher",
  "version": "1.0.0",
  "description": "First Abu Dhabi Bank comment form with Solace publishing",
  "main": "app.js",
  "scripts": {
    "start": "node app.js",
    "dev": "nodemon app.js"
  },
  "dependencies": {
    "body-parser": "^1.20.2",
    "express": "^4.18.2",
    "solclientjs": "^10.15.0",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "nodemon": "^3.0.1"
  }
}
*/

// Import required modules
const express = require('express');
const bodyParser = require('body-parser');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const solace = require('solclientjs');

// Initialize Express application
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());
app.use(express.static(path.join(__dirname, 'public')));

// Solace connection parameters - replace with your actual Solace broker details
const SOLACE_HOST = "wss://mr-connection-wqwjq3lyiah.messaging.solace.cloud:443"; // Replace with your Solace host
const SOLACE_VPN = "agent-mesh-demo";             // Replace with your VPN name
const SOLACE_USERNAME = "solace-cloud-client";        // Replace with your username
const SOLACE_PASSWORD = "aclebqot3nmjm9v84mhqtemist";        // Replace with your password
const SOLACE_TOPIC = "fab/customer/feedback/submitted/v1/id-439823"; // Topic to publish to

// Initialize Solace
let solaceSession = null;

// Initialize Solace factory
const factoryProps = new solace.SolclientFactoryProperties();
factoryProps.profile = solace.SolclientFactoryProfiles.version10;
solace.SolclientFactory.init(factoryProps);

// Solace session event listeners
const sessionEventCb = (sessionEvent) => {
    console.log(`Session event: ${sessionEvent.toString()}`);
    switch (sessionEvent.sessionEventCode) {
        case solace.SessionEventCode.UP_NOTICE:
            console.log('=== Connected to Solace message router ===');
            break;
        case solace.SessionEventCode.DISCONNECTED:
            console.log('=== Disconnected from Solace message router ===');
            if (solaceSession !== null) {
                solaceSession.dispose();
                solaceSession = null;
            }
            break;
        case solace.SessionEventCode.SUBSCRIPTION_ERROR:
            console.log('=== Cannot subscribe to topic ===');
            break;
        case solace.SessionEventCode.SUBSCRIPTION_OK:
            console.log('=== Successfully subscribed to topic ===');
            break;
    }
};

// Connect to Solace
function connectToSolace() {
    try {
        // Create session
        solaceSession = solace.SolclientFactory.createSession({
            url: SOLACE_HOST,
            vpnName: SOLACE_VPN,
            userName: SOLACE_USERNAME,
            password: SOLACE_PASSWORD,
            connectRetries: 3,
            reconnectRetries: 5
        });

        // Define session event listeners
        solaceSession.on(solace.SessionEventCode.UP_NOTICE, sessionEventCb);
        solaceSession.on(solace.SessionEventCode.CONNECT_FAILED_ERROR, sessionEventCb);
        solaceSession.on(solace.SessionEventCode.DISCONNECTED, sessionEventCb);
        solaceSession.on(solace.SessionEventCode.SUBSCRIPTION_ERROR, sessionEventCb);
        solaceSession.on(solace.SessionEventCode.SUBSCRIPTION_OK, sessionEventCb);

        // Connect to Solace Message Router
        solaceSession.connect();
    } catch (error) {
        console.log(`Error connecting to Solace: ${error.toString()}`);
    }
}

// Publish message to Solace
function publishToSolace(topic, message) {


    try {
        const solaceMessage = solace.SolclientFactory.createMessage();
        solaceMessage.setDestination(solace.SolclientFactory.createTopicDestination(topic));
        solaceMessage.setBinaryAttachment(JSON.stringify(message));
        solaceMessage.setDeliveryMode(solace.MessageDeliveryModeType.DIRECT);
        solaceMessage.setApplicationMessageId('FAB_Comment_Form');
        
        console.log(`Publishing message to topic: ${topic}`);
        solaceSession.send(solaceMessage);
        return true;
    } catch (error) {
        console.log(`Error publishing to Solace: ${error.toString()}`);
        return false;
    }
}

// Routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.post('/submit_comment', (req, res) => {
    try {
        // Extract form data
        const data = req.body;
        
        // Create a payload with additional metadata
        const payload = {
            "commentId": uuidv4(),
            "timestamp": new Date().toISOString(),
            "name": data.name || '',
            "email": data.email || '',
            "subject": data.subject || '',
            "message": data.message || '',
            "category": data.category || 'General',
            "rating": data.rating || 0
        };
        
        // Publish to Solace
        const publishResult = publishToSolace(SOLACE_TOPIC, payload);
        
        if (publishResult) {
            res.json({ status: "success", message: "Comment submitted successfully" });
        } else {
            res.status(500).json({ status: "error", message: "Failed to publish to Solace" });
        }
    } catch (error) {
        console.error(`Error processing comment: ${error.toString()}`);
        res.status(500).json({ status: "error", message: error.toString() });
    }
});

// Create public directory and HTML template
function setupPublicFiles() {
    // Create public directory if it doesn't exist
    if (!fs.existsSync(path.join(__dirname, 'public'))) {
        fs.mkdirSync(path.join(__dirname, 'public'));
    }
    
    // Create the FAB logo directory
    if (!fs.existsSync(path.join(__dirname, 'public', 'images'))) {
        fs.mkdirSync(path.join(__dirname, 'public', 'images'));
    }
    
    // Write the HTML template
    const htmlTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>First Abu Dhabi Bank - Customer Feedback</title>
    <style>
        :root {
            --fab-primary: #00AD4E;
            --fab-secondary: #005E35;
            --fab-accent: #E2F1E8;
            --fab-neutral: #F5F5F5;
            --fab-dark: #333333;
            --fab-light: #FFFFFF;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Arial', sans-serif;
        }
        
        body {
            background-color: var(--fab-neutral);
            color: var(--fab-dark);
            line-height: 1.6;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background-color: var(--fab-light);
            padding: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .logo {
            height: 50px;
            padding-left: 20px;
        }
        
        .header-text {
            color: var(--fab-secondary);
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            font-size: 28px;
        }
        
        .feedback-form {
            background-color: var(--fab-light);
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--fab-secondary);
        }
        
        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        
        textarea {
            height: 150px;
            resize: vertical;
        }
        
        .rating-container {
            display: flex;
            flex-direction: row-reverse;
            justify-content: flex-end;
        }
        
        .rating-container input {
            display: none;
        }
        
        .rating-container label {
            cursor: pointer;
            width: 40px;
            height: 40px;
            margin: 0 5px;
            background-color: #ddd;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--fab-light);
            transition: all 0.2s ease;
        }
        
        .rating-container input:checked ~ label,
        .rating-container label:hover,
        .rating-container label:hover ~ label {
            background-color: var(--fab-primary);
        }
        
        button {
            background-color: var(--fab-primary);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: var(--fab-secondary);
        }
        
        .success-message {
            display: none;
            background-color: var(--fab-accent);
            color: var(--fab-secondary);
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            text-align: center;
        }
        
        .error-message {
            display: none;
            background-color: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            text-align: center;
        }
        
        footer {
            background-color: var(--fab-secondary);
            color: var(--fab-light);
            text-align: center;
            padding: 20px 0;
            margin-top: 40px;
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .feedback-form {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <img src="/images/First_Abu_Dhabi_Bank_Logo.svg" alt="First Abu Dhabi Bank" class="logo">
        </div>
    </header>
    
    <div class="container">
        <h1 class="header-text">We Value Your Feedback</h1>
        
        <div class="feedback-form">
            <form id="commentForm">
                <div class="form-group">
                    <label for="name">Full Name*</label>
                    <input type="text" id="name" name="name" required>
                </div>
                
                <div class="form-group">
                    <label for="email">Email Address*</label>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="category">Category</label>
                    <select id="category" name="category">
                        <option value="General">General Feedback</option>
                        <option value="Product">Product Suggestion</option>
                        <option value="Service">Service Experience</option>
                        <option value="Technical">Technical Issue</option>
                        <option value="Other">Other</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="subject">Subject*</label>
                    <input type="text" id="subject" name="subject" required>
                </div>
                
                <div class="form-group">
                    <label for="message">Your Message*</label>
                    <textarea id="message" name="message" required></textarea>
                </div>
                
                <div class="form-group">
                    <label>Rate Your Experience</label>
                    <div class="rating-container">
                        <input type="radio" id="star5" name="rating" value="5">
                        <label for="star5">5</label>
                        <input type="radio" id="star4" name="rating" value="4">
                        <label for="star4">4</label>
                        <input type="radio" id="star3" name="rating" value="3">
                        <label for="star3">3</label>
                        <input type="radio" id="star2" name="rating" value="2">
                        <label for="star2">2</label>
                        <input type="radio" id="star1" name="rating" value="1">
                        <label for="star1">1</label>
                    </div>
                </div>
                
                <button type="submit">Submit Feedback</button>
            </form>
            
            <div class="success-message" id="successMessage">
                Thank you for your feedback! We appreciate your time and will use your comments to improve our services.
            </div>
            
            <div class="error-message" id="errorMessage">
                There was an error submitting your feedback. Please try again later.
            </div>
        </div>
    </div>
    
    <footer>
        <div class="container">
            <p>&copy; 2025 First Abu Dhabi Bank. All rights reserved.</p>
        </div>
    </footer>
    
    <script>
        document.getElementById('commentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const formDataObj = {};
            
            formData.forEach((value, key) => {
                formDataObj[key] = value;
            });
            
            fetch('/submit_comment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formDataObj)
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    document.getElementById('successMessage').style.display = 'block';
                    document.getElementById('errorMessage').style.display = 'none';
                    document.getElementById('commentForm').reset();
                } else {
                    document.getElementById('errorMessage').style.display = 'block';
                    document.getElementById('successMessage').style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('errorMessage').style.display = 'block';
                document.getElementById('successMessage').style.display = 'none';
            });
        });
    </script>
</body>
</html>
    `;
    
    fs.writeFileSync(path.join(__dirname, 'public', 'index.html'), htmlTemplate);
    
}

// Start server
function startServer() {
    // Connect to Solace first
    connectToSolace();
    
    // Set up the public files
    setupPublicFiles();
    
    // Start the Express server
    app.listen(PORT, () => {
        console.log(`Server running on port ${PORT}`);
    });
}

// Handle application shutdown
process.on('SIGINT', () => {
    if (solaceSession !== null) {
        console.log('Disconnecting from Solace...');
        solaceSession.disconnect();
    }
    process.exit();
});

// Start the server
startServer();
