{"name": "solace-generic-electron-consumer", "version": "1.0.0", "description": "Electron app for consuming messages from Solace topics", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "dist": "electron-builder --publish=never"}, "keywords": ["electron", "solace", "messaging", "consumer"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"electron": "^32.3.3", "electron-builder": "^26.0.12"}, "dependencies": {"dotenv": "^16.3.1", "lru-cache": "^10.4.3", "solclientjs": "^10.18.0"}, "overrides": {"glob": "^10.4.5", "inflight": "npm:lru-cache@^10.0.0"}, "build": {"appId": "com.solace.electron.consumer", "productName": "Solace Message Consumer", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules/**/*", "node_modules/solclientjs/**/*"], "mac": {"category": "public.app-category.developer-tools"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}