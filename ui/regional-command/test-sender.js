// Simple test script to send analysis data to Solace for testing
// This would normally be run from a separate process/system

const solace = require('solclientjs');

// Sample analysis data matching the schema
const sampleAnalysisData = [
    {
        threat_level_field_reports: 2,
        threat_level_image_analysis: 3,
        threat_level_combined: 3,
        security_situation_summary: "Routine patrol completed. Minor suspicious activity reported in sector 7. Situation under observation.",
        recommended_actions: [
            "Continue regular patrol schedule",
            "Increase surveillance in sector 7",
            "Report any changes immediately"
        ],
        regionId: 15,
        baseId: 203,
        latitude: 35.6762,
        longitude: 139.6503
    },
    {
        threat_level_field_reports: 6,
        threat_level_image_analysis: 7,
        threat_level_combined: 7,
        security_situation_summary: "Elevated threat detected. Multiple unidentified vehicles approaching from the southeast. Perimeter sensors triggered.",
        recommended_actions: [
            "Activate perimeter defense systems",
            "Deploy rapid response team to southeast sector",
            "Establish communication with approaching vehicles",
            "Prepare for potential evacuation if needed"
        ],
        regionId: 22,
        baseId: 156,
        latitude: 40.7128,
        longitude: -74.0060
    },
    {
        threat_level_field_reports: 9,
        threat_level_image_analysis: 8,
        threat_level_combined: 9,
        security_situation_summary: "CRITICAL ALERT: Hostile forces confirmed. Multiple armed contacts detected. Base under immediate threat. All personnel to battle stations.",
        recommended_actions: [
            "IMMEDIATE: Sound general alarm",
            "Deploy all available defensive units",
            "Initiate lockdown procedures",
            "Request immediate backup from allied forces",
            "Prepare for potential base evacuation"
        ],
        regionId: 8,
        baseId: 442,
        latitude: 32.7767,
        longitude: -96.7970
    }
];

class TestSender {
    constructor() {
        this.session = null;
        this.isConnected = false;
    }

    async connect() {
        try {
            // Initialize Solace factory
            const factoryProps = new solace.SolclientFactoryProperties();
            factoryProps.profile = solace.SolclientFactoryProfiles.version10;
            solace.SolclientFactory.init(factoryProps);

            // Create session properties (use environment variables or defaults)
            const sessionProperties = new solace.SessionProperties();
            sessionProperties.url = process.env.SOLACE_URL || 'ws://localhost:8008';
            sessionProperties.vpnName = process.env.SOLACE_VPN || 'default';
            sessionProperties.userName = process.env.SOLACE_USERNAME || 'default';
            sessionProperties.password = process.env.SOLACE_PASSWORD || '';

            // Create session
            this.session = solace.SolclientFactory.createSession(sessionProperties);

            // Set up event listeners
            this.session.on(solace.SessionEventCode.UP_NOTICE, () => {
                this.isConnected = true;
                console.log('✅ Connected to Solace broker');
                this.startSendingData();
            });

            this.session.on(solace.SessionEventCode.CONNECT_FAILED_ERROR, (sessionEvent) => {
                console.error('❌ Connection failed:', sessionEvent.infoStr);
            });

            this.session.on(solace.SessionEventCode.DISCONNECTED, () => {
                this.isConnected = false;
                console.log('🔌 Disconnected from Solace broker');
            });

            // Connect
            console.log('🔄 Connecting to Solace broker...');
            this.session.connect();

        } catch (error) {
            console.error('❌ Error connecting to Solace:', error);
        }
    }

    startSendingData() {
        console.log('📡 Starting to send test analysis data...');
        
        // Send one message every 5 seconds
        let messageIndex = 0;
        const interval = setInterval(() => {
            if (!this.isConnected) {
                clearInterval(interval);
                return;
            }

            const data = sampleAnalysisData[messageIndex % sampleAnalysisData.length];
            this.sendAnalysisData(data);
            messageIndex++;

            // Stop after sending 10 messages
            if (messageIndex >= 10) {
                clearInterval(interval);
                console.log('✅ Finished sending test data');
                setTimeout(() => {
                    this.disconnect();
                }, 2000);
            }
        }, 5000);
    }

    sendAnalysisData(data) {
        try {
            const topic = solace.SolclientFactory.createTopicDestination(
                process.env.SOLACE_TOPIC || 'regional/command/analysis'
            );
            
            const message = solace.SolclientFactory.createMessage();
            message.setDestination(topic);
            message.setBinaryAttachment(JSON.stringify(data));
            message.setDeliveryMode(solace.MessageDeliveryModeType.DIRECT);

            this.session.send(message);
            
            const threatLabel = this.getThreatLevelLabel(data.threat_level_combined);
            console.log(`📤 Sent analysis data - Threat Level: ${data.threat_level_combined} (${threatLabel}) - Region: ${data.regionId}`);
            
        } catch (error) {
            console.error('❌ Error sending message:', error);
        }
    }

    getThreatLevelLabel(level) {
        if (level >= 8) return 'CRITICAL';
        if (level >= 6) return 'HIGH';
        if (level >= 4) return 'MEDIUM';
        if (level >= 2) return 'LOW';
        return 'MINIMAL';
    }

    disconnect() {
        if (this.session) {
            this.session.disconnect();
        }
    }
}

// Run the test sender
if (require.main === module) {
    console.log('🚀 Regional Command Analysis Test Sender');
    console.log('📋 This will send sample analysis data to test the dashboard');
    
    const sender = new TestSender();
    sender.connect();

    // Handle graceful shutdown
    process.on('SIGINT', () => {
        console.log('\n🛑 Shutting down test sender...');
        sender.disconnect();
        process.exit(0);
    });
}

module.exports = TestSender;
