const solace = require('solclientjs');

class SolaceClient {
    constructor() {
        this.session = null;
        this.isConnected = false;
        this.subscriptions = new Map();
        this.messageCallback = null;
        this.statusCallback = null;
        this.config = null;
        
        // Initialize Solace factory
        const factoryProps = new solace.SolclientFactoryProperties();
        factoryProps.profile = solace.SolclientFactoryProfiles.version10;
        solace.SolclientFactory.init(factoryProps);
    }

    setMessageCallback(callback) {
        this.messageCallback = callback;
    }

    setStatusCallback(callback) {
        this.statusCallback = callback;
    }

    async connect(config) {
        try {
            this.config = config;
            
            if (this.session) {
                this.disconnect();
            }

            // Create session properties
            const sessionProperties = new solace.SessionProperties();
            sessionProperties.url = config.url;
            sessionProperties.vpnName = config.vpn;
            sessionProperties.userName = config.username;
            sessionProperties.password = config.password;
            sessionProperties.connectRetries = 3;
            sessionProperties.reconnectRetries = 3;
            sessionProperties.connectTimeoutInMsecs = 10000;

            // Create session
            this.session = solace.SolclientFactory.createSession(sessionProperties);

            // Set up event listeners
            this.session.on(solace.SessionEventCode.UP_NOTICE, (sessionEvent) => {
                this.isConnected = true;
                console.log('Session connected');
                if (this.statusCallback) {
                    this.statusCallback('connected', 'Connected to Solace');
                }
            });

            this.session.on(solace.SessionEventCode.CONNECT_FAILED_ERROR, (sessionEvent) => {
                this.isConnected = false;
                console.error('Connection failed:', sessionEvent.infoStr);
                if (this.statusCallback) {
                    this.statusCallback('disconnected', `Connection failed: ${sessionEvent.infoStr}`);
                }
            });

            this.session.on(solace.SessionEventCode.DISCONNECTED, (sessionEvent) => {
                this.isConnected = false;
                console.log('Session disconnected');
                if (this.statusCallback) {
                    this.statusCallback('disconnected', 'Disconnected from Solace');
                }
            });

            this.session.on(solace.SessionEventCode.DOWN_ERROR, (sessionEvent) => {
                this.isConnected = false;
                console.error('Session down error:', sessionEvent.infoStr);
                if (this.statusCallback) {
                    this.statusCallback('disconnected', `Session error: ${sessionEvent.infoStr}`);
                }
            });

            this.session.on(solace.SessionEventCode.MESSAGE, (message) => {
                this.handleMessage(message);
            });

            // Connect
            if (this.statusCallback) {
                this.statusCallback('connecting', 'Connecting to Solace...');
            }
            
            this.session.connect();
            
        } catch (error) {
            console.error('Error connecting to Solace:', error);
            if (this.statusCallback) {
                this.statusCallback('disconnected', `Connection error: ${error.message}`);
            }
            throw error;
        }
    }

    disconnect() {
        if (this.session) {
            try {
                // Unsubscribe from all topics
                this.subscriptions.forEach((subscription, topic) => {
                    this.unsubscribe(topic);
                });
                
                this.session.disconnect();
                this.session.dispose();
                this.session = null;
                this.isConnected = false;
                
                if (this.statusCallback) {
                    this.statusCallback('disconnected', 'Disconnected');
                }
            } catch (error) {
                console.error('Error disconnecting:', error);
            }
        }
    }

    subscribe(topicName) {
        if (!this.session || !this.isConnected) {
            throw new Error('Not connected to Solace');
        }

        try {
            const topic = solace.SolclientFactory.createTopicDestination(topicName);
            
            this.session.subscribe(
                topic,
                true, // requestConfirmation
                topicName, // correlationKey
                10000 // requestTimeout
            );
            
            this.subscriptions.set(topicName, topic);
            console.log(`Subscribed to topic: ${topicName}`);
            
        } catch (error) {
            console.error('Error subscribing to topic:', error);
            throw error;
        }
    }

    unsubscribe(topicName) {
        if (!this.session || !this.subscriptions.has(topicName)) {
            return;
        }

        try {
            const topic = this.subscriptions.get(topicName);
            this.session.unsubscribe(
                topic,
                true, // requestConfirmation
                topicName, // correlationKey
                10000 // requestTimeout
            );
            
            this.subscriptions.delete(topicName);
            console.log(`Unsubscribed from topic: ${topicName}`);
            
        } catch (error) {
            console.error('Error unsubscribing from topic:', error);
            throw error;
        }
    }

    handleMessage(message) {
        try {
            const messageData = {
                topic: message.getDestination().getName(),
                payload: message.getBinaryAttachment() || message.getSdtContainer() || 'No payload',
                timestamp: new Date().toISOString(),
                messageId: message.getApplicationMessageId() || 'N/A',
                messageType: message.getApplicationMessageType() || 'N/A',
                senderId: message.getSenderId() || 'N/A',
                sequenceNumber: message.getSequenceNumber() || 'N/A'
            };

            // Convert payload to string if it's binary
            if (messageData.payload && typeof messageData.payload !== 'string') {
                try {
                    messageData.payload = messageData.payload.toString();
                } catch (e) {
                    messageData.payload = '[Binary data]';
                }
            }

            if (this.messageCallback) {
                this.messageCallback(messageData);
            }
            
        } catch (error) {
            console.error('Error handling message:', error);
        }
    }

    getConnectionStatus() {
        return {
            connected: this.isConnected,
            subscriptions: Array.from(this.subscriptions.keys()),
            config: this.config
        };
    }
}

// Export for use in renderer
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SolaceClient;
} else {
    window.SolaceClient = SolaceClient;
}
