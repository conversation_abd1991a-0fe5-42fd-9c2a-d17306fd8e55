{"name": "solace-generic-electron-consumer", "version": "1.0.0", "description": "Electron app for consuming messages from Solace topics", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "dist": "electron-builder --publish=never"}, "keywords": ["electron", "solace", "messaging", "consumer"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"electron": "^36.4.0", "electron-builder": "^26.0.17"}, "dependencies": {"chart.js": "^4.5.0", "dotenv": "^16.4.7", "leaflet": "^1.9.4", "solclientjs": "^10.18.1"}, "overrides": {"glob": "^10.4.5", "inflight": "npm:@isaacs/inflight@^1.0.6", "boolean": "npm:yn@^5.0.0"}, "build": {"appId": "com.solace.electron.consumer", "productName": "Solace Message Consumer", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules/**/*", "node_modules/solclientjs/**/*"], "mac": {"category": "public.app-category.developer-tools"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}