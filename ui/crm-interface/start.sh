#!/bin/bash

# Script to start the Solace Electron Consumer with proper environment settings

# Set display if not set (for headless environments)
if [ -z "$DISPLAY" ]; then
    export DISPLAY=:0
fi

# Disable GPU acceleration if running in a container or headless environment
export ELECTRON_DISABLE_GPU=true

# Disable sandbox for compatibility
export ELECTRON_DISABLE_SANDBOX=true

# Start the application
npm start
