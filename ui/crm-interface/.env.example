# Solace Connection Configuration
# This application now reads configuration from the root project .env file
# Add these variables to ../../.env (root directory)

# DATAPLANE Solace broker configuration
DATAPLANE_SOLACE_BROKER_URL=wss://your-solace-broker:443
DATAPLANE_SOLACE_BROKER_VPN=your-vpn-name
DATAPLANE_SOLACE_BROKER_USERNAME=your-username
DATAPLANE_SOLACE_BROKER_PASSWORD=your-password

# Optional: Default topic to subscribe to
SOLACE_TOPIC=test/topic

# Note: The application automatically loads these from ../../.env
# No local .env file is needed in this directory
