import { <PERSON><PERSON> } from "lazy-val";
import { Configuration } from "../configuration";
import { NodeModuleDirInfo } from "./packageDependencies";
export declare function installOrRebuild(config: Configuration, { appDir, projectDir }: DirectoryPaths, options: RebuildOptions, forceInstall?: boolean): Promise<void>;
export interface DesktopFrameworkInfo {
    version: string;
    useCustomDist: boolean;
}
export declare function getGypEnv(frameworkInfo: DesktopFrameworkInfo, platform: NodeJS.Platform, arch: string, buildFromSource: boolean): any;
export declare function installDependencies(config: Configuration, { appDir, projectDir }: DirectoryPaths, options: RebuildOptions): Promise<any>;
export declare function nodeGypRebuild(platform: NodeJS.Platform, arch: string, frameworkInfo: DesktopFrameworkInfo): Promise<void>;
export interface RebuildOptions {
    frameworkInfo: DesktopFrameworkInfo;
    productionDeps: Lazy<Array<NodeModuleDirInfo>>;
    platform?: NodeJS.Platform;
    arch?: string;
    buildFromSource?: boolean;
    additionalArgs?: Array<string> | null;
}
export interface DirectoryPaths {
    appDir: string;
    projectDir: string;
}
