{"version": 3, "file": "packageMetadata.js", "sourceRoot": "", "sources": ["../../src/util/packageMetadata.ts"], "names": [], "mappings": ";;AASA,0CAQC;AAkBD,sCA6CC;AAhFD,+CAA8E;AAE9E,uCAA2D;AAC3D,6BAA4B;AAC5B,iCAAgC;AAEhC,iEAA6D;AAE7D,gBAAgB;AACT,KAAK,UAAU,eAAe,CAAC,IAAY;IAChD,MAAM,IAAI,GAAG,MAAM,IAAA,mBAAQ,EAAC,IAAI,CAAC,CAAA;IACjC,MAAM,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IACzB,kEAAkE;IAClE,OAAO,IAAI,CAAC,OAAO,CAAA;IACnB,OAAO,IAAI,CAAC,MAAM,CAAA;IAClB,IAAA,2CAAoB,EAAC,IAAI,CAAC,CAAA;IAC1B,OAAO,IAAI,CAAA;AACb,CAAC;AAED,KAAK,UAAU,OAAO,CAAC,IAAY,EAAE,IAAS;IAC5C,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,EAAE,CAAC;QAC9B,OAAM;IACR,CAAC;IAED,IAAI,UAAU,CAAA;IACd,IAAI,CAAC;QACH,UAAU,GAAG,MAAM,IAAA,mBAAQ,EAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,EAAE,MAAM,CAAC,CAAA;IAClF,CAAC;IAAC,OAAO,QAAQ,EAAE,CAAC;QAClB,OAAM;IACR,CAAC;IAED,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;AAC7F,CAAC;AAED,gBAAgB;AAChB,SAAgB,aAAa,CAAC,QAAkB,EAAE,WAAuB,EAAE,cAAsB,EAAE,iBAAyB;IAC1H,MAAM,MAAM,GAAkB,EAAE,CAAA;IAChC,MAAM,WAAW,GAAG,CAAC,eAAuB,EAAE,EAAE;QAC9C,MAAM,CAAC,IAAI,CAAC,mBAAmB,eAAe,0BAA0B,cAAc,GAAG,CAAC,CAAA;IAC5F,CAAC,CAAA;IAED,MAAM,aAAa,GAAG,CAAC,IAAY,EAAE,KAAuB,EAAE,EAAE;QAC9D,IAAI,IAAA,8BAAe,EAAC,KAAK,CAAC,EAAE,CAAC;YAC3B,WAAW,CAAC,IAAI,CAAC,CAAA;QACnB,CAAC;IACH,CAAC,CAAA;IAED,IAAK,QAAgB,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;QAC1C,MAAM,CAAC,IAAI,CAAC,wEAAwE,CAAC,CAAA;IACvF,CAAC;IAED,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAA;IAEpC,IAAI,IAAA,8BAAe,EAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;QAC1C,kBAAG,CAAC,IAAI,CAAC,EAAE,cAAc,EAAE,EAAE,2CAA2C,CAAC,CAAA;IAC3E,CAAC;IACD,IAAI,QAAQ,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;QAC5B,kBAAG,CAAC,IAAI,CAAC,EAAE,cAAc,EAAE,EAAE,sCAAsC,CAAC,CAAA;IACtE,CAAC;IACD,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAA;IAE1C,iBAAiB,CAAC,QAAQ,CAAC,YAAY,EAAE,MAAM,CAAC,CAAA;IAChD,IAAI,QAAQ,KAAK,WAAW,EAAE,CAAC;QAC7B,IAAI,QAAQ,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;YAC3B,MAAM,CAAC,IAAI,CACT,4CAA4C,cAAc,gGAAgG,iBAAiB,GAAG,CAC/K,CAAA;QACH,CAAC;IACH,CAAC;IAED,MAAM,eAAe,GAAI,QAAgB,CAAC,eAAe,CAAA;IACzD,IAAI,eAAe,IAAI,IAAI,IAAI,CAAC,kBAAkB,IAAI,eAAe,IAAI,mBAAmB,IAAI,eAAe,CAAC,EAAE,CAAC;QACjH,kBAAG,CAAC,IAAI,CACN,8RAA8R,CAC/R,CAAA;IACH,CAAC;IAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACtB,MAAM,IAAI,wCAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;IACxD,CAAC;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,OAAsC,EAAE,KAA4B,EAAE,KAAe;IAC7G,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,OAAO,KAAK,CAAA;IACd,CAAC;IAED,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IACtC,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,OAAO,KAAK,CAAA;IACd,CAAC;IAED,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;AAChD,CAAC;AAED,SAAS,iBAAiB,CAAC,YAA8C,EAAE,MAAqB;IAC9F,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;QACzB,OAAM;IACR,CAAC;IAED,IAAI,cAAc,GAAG,YAAY,CAAC,kBAAkB,CAAC,CAAA;IACrD,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;QAC3B,kDAAkD;QAClD,mGAAmG;QACnG,IAAI,cAAc,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxC,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;YACnD,IAAI,KAAK,EAAE,CAAC;gBACV,cAAc,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;YAC3B,CAAC;QACH,CAAC;QAED,2DAA2D;QAC3D,IAAI,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YACvC,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAA;YAC3E,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC,CAAA;YACtG,MAAM,IAAI,GAAG,IAAA,uBAAY,EAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC,CAAA;YACrE,cAAc,GAAG,IAAI,CAAC,OAAO,CAAA;QAC/B,CAAC;QAED,MAAM,8BAA8B,GAAG,OAAO,CAAA;QAC9C,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,KAAK,8BAA8B,EAAE,CAAC,EAAE,CAAC;YAC7E,MAAM,CAAC,IAAI,CACT,6BAA6B,8BAA8B,iGAAiG,8BAA8B,gBAAgB,cAAc,GAAG,CAC5N,CAAA;QACH,CAAC;IACH,CAAC;IAED,MAAM,SAAS,GAAG,YAAY,CAAC,mCAAmC,CAAC,CAAA;IACnE,IAAI,SAAS,IAAI,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE,CAAC;QACnE,MAAM,CAAC,IAAI,CAAC,gKAAgK,CAAC,CAAA;IAC/K,CAAC;IAED,MAAM,IAAI,GAAG,CAAC,UAAU,EAAE,mBAAmB,EAAE,kBAAkB,CAAC,CAAA;IAClE,IAAI,OAAO,CAAC,GAAG,CAAC,+CAA+C,KAAK,MAAM,EAAE,CAAC;QAC3E,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;IAC/B,CAAC;IACD,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC;QACxB,IAAI,IAAI,IAAI,YAAY,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,YAAY,IAAI,0CAA0C,GAAG,wEAAwE,CAAC,CAAA;QACpJ,CAAC;IACH,CAAC;AACH,CAAC", "sourcesContent": ["import { InvalidConfigurationError, isEmptyOrSpaces, log } from \"builder-util\"\nimport { Nullish } from \"builder-util-runtime\"\nimport { readFile, readJson, readJsonSync } from \"fs-extra\"\nimport * as path from \"path\"\nimport * as semver from \"semver\"\nimport { Metadata } from \"../options/metadata\"\nimport { normalizePackageData } from \"./normalizePackageData\"\n\n/** @internal */\nexport async function readPackageJson(file: string): Promise<any> {\n  const data = await readJson(file)\n  await authors(file, data)\n  // remove not required fields because can be used for remote build\n  delete data.scripts\n  delete data.readme\n  normalizePackageData(data)\n  return data\n}\n\nasync function authors(file: string, data: any) {\n  if (data.contributors != null) {\n    return\n  }\n\n  let authorData\n  try {\n    authorData = await readFile(path.resolve(path.dirname(file), \"AUTHORS\"), \"utf8\")\n  } catch (_ignored) {\n    return\n  }\n\n  data.contributors = authorData.split(/\\r?\\n/g).map(it => it.replace(/^\\s*#.*$/, \"\").trim())\n}\n\n/** @internal */\nexport function checkMetadata(metadata: Metadata, devMetadata: any | null, appPackageFile: string, devAppPackageFile: string): void {\n  const errors: Array<string> = []\n  const reportError = (missedFieldName: string) => {\n    errors.push(`Please specify '${missedFieldName}' in the package.json (${appPackageFile})`)\n  }\n\n  const checkNotEmpty = (name: string, value: string | Nullish) => {\n    if (isEmptyOrSpaces(value)) {\n      reportError(name)\n    }\n  }\n\n  if ((metadata as any).directories != null) {\n    errors.push(`\"directories\" in the root is deprecated, please specify in the \"build\"`)\n  }\n\n  checkNotEmpty(\"name\", metadata.name)\n\n  if (isEmptyOrSpaces(metadata.description)) {\n    log.warn({ appPackageFile }, `description is missed in the package.json`)\n  }\n  if (metadata.author == null) {\n    log.warn({ appPackageFile }, `author is missed in the package.json`)\n  }\n  checkNotEmpty(\"version\", metadata.version)\n\n  checkDependencies(metadata.dependencies, errors)\n  if (metadata !== devMetadata) {\n    if (metadata.build != null) {\n      errors.push(\n        `'build' in the application package.json (${appPackageFile}) is not supported since 3.0 anymore. Please move 'build' into the development package.json (${devAppPackageFile})`\n      )\n    }\n  }\n\n  const devDependencies = (metadata as any).devDependencies\n  if (devDependencies != null && (\"electron-rebuild\" in devDependencies || \"@electron/rebuild\" in devDependencies)) {\n    log.info(\n      '@electron/rebuild already used by electron-builder, please consider to remove excess dependency from devDependencies\\n\\nTo ensure your native dependencies are always matched electron version, simply add script `\"postinstall\": \"electron-builder install-app-deps\" to your `package.json`'\n    )\n  }\n\n  if (errors.length > 0) {\n    throw new InvalidConfigurationError(errors.join(\"\\n\"))\n  }\n}\n\nfunction versionSatisfies(version: string | semver.SemVer | null, range: string | semver.Range, loose?: boolean): boolean {\n  if (version == null) {\n    return false\n  }\n\n  const coerced = semver.coerce(version)\n  if (coerced == null) {\n    return false\n  }\n\n  return semver.satisfies(coerced, range, loose)\n}\n\nfunction checkDependencies(dependencies: Record<string, string> | Nullish, errors: Array<string>) {\n  if (dependencies == null) {\n    return\n  }\n\n  let updaterVersion = dependencies[\"electron-updater\"]\n  if (updaterVersion != null) {\n    // Pick the version out of yarn berry patch syntax\n    // \"patch:electron-updater@npm%3A6.4.1#~/.yarn/patches/electron-updater-npm-6.4.1-ef33e6cc39.patch\"\n    if (updaterVersion.startsWith(\"patch:\")) {\n      const match = updaterVersion.match(/@npm%3A(.+?)#/)\n      if (match) {\n        updaterVersion = match[1]\n      }\n    }\n\n    // for testing auto-update using workspace electron-updater\n    if (updaterVersion.startsWith(\"file:\")) {\n      const normalized = path.normalize(updaterVersion.substring(\"file:\".length))\n      const packageJsonPath = path.isAbsolute(normalized) ? normalized : path.resolve(__dirname, normalized)\n      const json = readJsonSync(path.join(packageJsonPath, \"package.json\"))\n      updaterVersion = json.version\n    }\n\n    const requiredElectronUpdaterVersion = \"4.0.0\"\n    if (!versionSatisfies(updaterVersion, `>=${requiredElectronUpdaterVersion}`)) {\n      errors.push(\n        `At least electron-updater ${requiredElectronUpdaterVersion} is recommended by current electron-builder version. Please set electron-updater version to \"^${requiredElectronUpdaterVersion}\". Received \"${updaterVersion}\"`\n      )\n    }\n  }\n\n  const swVersion = dependencies[\"electron-builder-squirrel-windows\"]\n  if (swVersion != null && !versionSatisfies(swVersion, \">=20.32.0\")) {\n    errors.push(`At least electron-builder-squirrel-windows 20.32.0 is required by current electron-builder version. Please set electron-builder-squirrel-windows to \"^20.32.0\"`)\n  }\n\n  const deps = [\"electron\", \"electron-prebuilt\", \"electron-rebuild\"]\n  if (process.env.ALLOW_ELECTRON_BUILDER_AS_PRODUCTION_DEPENDENCY !== \"true\") {\n    deps.push(\"electron-builder\")\n  }\n  for (const name of deps) {\n    if (name in dependencies) {\n      errors.push(`Package \"${name}\" is only allowed in \"devDependencies\". ` + `Please remove it from the \"dependencies\" section in your package.json.`)\n    }\n  }\n}\n"]}