{"version": 3, "file": "yarn.js", "sourceRoot": "", "sources": ["../../src/util/yarn.ts"], "names": [], "mappings": ";;AAcA,4CAqBC;AAWD,8BAiCC;AAED,kDA+BC;AAED,wCAiBC;AAmBD,0BAuCC;AA3LD,+CAAkD;AAClD,uCAAqC;AAErC,2BAA4B;AAC5B,6BAA4B;AAE5B,6CAA4D;AAC5D,oEAA6F;AAE7F,+CAA4D;AAC5D,+BAA8B;AAEvB,KAAK,UAAU,gBAAgB,CAAC,MAAqB,EAAE,EAAE,MAAM,EAAE,UAAU,EAAkB,EAAE,OAAuB,EAAE,YAAY,GAAG,KAAK;IACjJ,MAAM,gBAAgB,GAAmB;QACvC,eAAe,EAAE,MAAM,CAAC,2BAA2B,KAAK,IAAI;QAC5D,cAAc,EAAE,IAAA,sBAAO,EAAC,MAAM,CAAC,OAAO,CAAC;QACvC,GAAG,OAAO;KACX,CAAA;IACD,IAAI,uBAAuB,GAAG,KAAK,CAAA;IAEnC,KAAK,MAAM,SAAS,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,EAAE,CAAC;QACpD,IAAI,MAAM,IAAA,qBAAU,EAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC;YACnD,uBAAuB,GAAG,IAAI,CAAA;YAE9B,MAAK;QACP,CAAC;IACH,CAAC;IAED,IAAI,YAAY,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC7C,MAAM,mBAAmB,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,gBAAgB,CAAC,CAAA;IAC7E,CAAC;SAAM,CAAC;QACN,MAAM,OAAO,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,gBAAgB,CAAC,CAAA;IACjE,CAAC;AACH,CAAC;AAOD,SAAS,sBAAsB;IAC7B,OAAO,IAAI,CAAC,IAAI,CAAC,IAAA,YAAO,GAAE,EAAE,eAAe,CAAC,CAAA;AAC9C,CAAC;AAED,SAAgB,SAAS,CAAC,aAAmC,EAAE,QAAyB,EAAE,IAAY,EAAE,eAAwB;IAC9H,MAAM,aAAa,GAAG,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAA;IACtD,MAAM,MAAM,GAAQ;QAClB,GAAG,OAAO,CAAC,GAAG;QACd,eAAe,EAAE,aAAa;QAC9B,sBAAsB,EAAE,aAAa;QACrC,mBAAmB,EAAE,QAAQ;QAC7B,4BAA4B,EAAE,eAAe;QAC7C,4BAA4B;QAC5B,0BAA0B,EAAE,QAAQ;QACpC,wBAAwB,EAAE,IAAI;QAC9B,4BAA4B,EAAE,IAAI;KACnC,CAAA;IAED,IAAI,QAAQ,KAAK,OAAO,CAAC,QAAQ,EAAE,CAAC;QAClC,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAA;IAClC,CAAC;IACD,IAAI,QAAQ,KAAK,OAAO,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;QAClD,MAAM,CAAC,sBAAsB,GAAG,SAAS,CAAA;IAC3C,CAAC;IAED,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;QACjC,OAAO,MAAM,CAAA;IACf,CAAC;IAED,+CAA+C;IAC/C,OAAO;QACL,GAAG,MAAM;QACT,kBAAkB,EAAE,MAAM,CAAC,0BAA0B,IAAI,gCAAgC;QACzF,iBAAiB,EAAE,aAAa,CAAC,OAAO;QACxC,kBAAkB,EAAE,UAAU;QAC9B,iBAAiB,EAAE,sBAAsB,EAAE;KAC5C,CAAA;AACH,CAAC;AAEM,KAAK,UAAU,mBAAmB,CAAC,MAAqB,EAAE,EAAE,MAAM,EAAE,UAAU,EAAkB,EAAE,OAAuB;IAC9H,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAA;IACrD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAA;IACzC,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,CAAA;IAE7C,MAAM,EAAE,GAAG,IAAA,4CAAoB,EAAC,UAAU,CAAC,CAAA;IAC3C,kBAAG,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE,oCAAoC,CAAC,CAAA;IAC1F,MAAM,QAAQ,GAAG,CAAC,SAAS,CAAC,CAAA;IAC5B,IAAI,EAAE,KAAK,0BAAE,CAAC,UAAU,EAAE,CAAC;QACzB,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,KAAK,MAAM,EAAE,CAAC;YAC5C,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;QACjC,CAAC;IACH,CAAC;IAED,IAAI,EAAE,KAAK,0BAAE,CAAC,IAAI,EAAE,CAAC;QACnB,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;IACnC,CAAC;IAED,MAAM,QAAQ,GAAG,IAAA,gDAAwB,EAAC,EAAE,CAAC,CAAA;IAE7C,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;QAC3B,QAAQ,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAA;IAClC,CAAC;IACD,MAAM,IAAA,oBAAK,EAAC,QAAQ,EAAE,QAAQ,EAAE;QAC9B,GAAG,EAAE,MAAM;QACX,GAAG,EAAE,SAAS,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,eAAe,KAAK,IAAI,CAAC;KACxF,CAAC,CAAA;IAEF,2JAA2J;IAC3J,oEAAoE;IACpE,OAAO,OAAO,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,OAAO,CAAC,CAAA;AACzD,CAAC;AAEM,KAAK,UAAU,cAAc,CAAC,QAAyB,EAAE,IAAY,EAAE,aAAmC;IAC/G,kBAAG,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,4BAA4B,CAAC,CAAA;IAC1D,6CAA6C;IAC7C,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAA;IAClF,MAAM,IAAI,GAAG,CAAC,SAAS,CAAC,CAAA;IACxB,wEAAwE;IACxE,oEAAoE;IACpE,oCAAoC;IACpC,wDAAwD;IACxD,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,aAAa,CAAC,OAAO;SACzC,KAAK,CAAC,GAAG,CAAC;SACV,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;SACX,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;IAC5B,IAAI,KAAK,IAAI,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC;QAC9E,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAA;IACrC,CAAC;IACD,MAAM,IAAA,oBAAK,EAAC,OAAO,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,SAAS,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;AACrF,CAAC;AAkBD,gBAAgB;AACT,KAAK,UAAU,OAAO,CAAC,MAAqB,EAAE,EAAE,MAAM,EAAE,UAAU,EAAkB,EAAE,OAAuB;IAClH,MAAM,aAAa,GAAG;QACpB,YAAY,EAAE,MAAM,OAAO,CAAC,cAAc,CAAC,KAAK;QAChD,YAAY,EAAE,OAAO,CAAC,QAAQ;QAC9B,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ;QAC9C,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI;QAClC,cAAc,EAAE,OAAO,CAAC,cAAc;QACtC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU;QAC5D,eAAe,EAAE,OAAO,CAAC,eAAe,KAAK,IAAI;KAClD,CAAA;IACD,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,GAAG,aAAa,CAAA;IAEzD,IAAI,MAAM,CAAC,eAAe,KAAK,QAAQ,EAAE,CAAC;QACxC,MAAM,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,eAAe,CAAC,CAAA;QAC7E,OAAO,IAAA,0CAA6B,EAAC,CAAC,sBAAsB,CAAC,EAAE,aAAa,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,CAAA;IACrG,CAAC;IAED,MAAM,EACJ,aAAa,EAAE,EAAE,OAAO,EAAE,eAAe,EAAE,GAC5C,GAAG,OAAO,CAAA;IACX,MAAM,OAAO,GAAG;QACd,eAAe;QACf,IAAI;QACJ,eAAe;QACf,MAAM,EAAE,kBAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI;KACrC,CAAA;IACD,kBAAG,CAAC,IAAI,CAAC,OAAO,EAAE,6BAA6B,CAAC,CAAA;IAEhD,MAAM,cAAc,GAAmC;QACrD,SAAS,EAAE,MAAM;QACjB,eAAe;QACf,IAAI;QACJ,QAAQ;QACR,eAAe;QACf,eAAe,EAAE,UAAU;QAC3B,IAAI,EAAG,MAAM,CAAC,eAA+B,IAAI,YAAY;QAC7D,iBAAiB,EAAE,IAAI;KACxB,CAAA;IACD,OAAO,IAAA,iBAAa,EAAC,cAAc,CAAC,CAAA;AACtC,CAAC", "sourcesContent": ["import * as electronRebuild from \"@electron/rebuild\"\nimport { RebuildMode } from \"@electron/rebuild/lib/types\"\nimport { asArray, log, spawn } from \"builder-util\"\nimport { pathExists } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport { homedir } from \"os\"\nimport * as path from \"path\"\nimport { Configuration } from \"../configuration\"\nimport { executeAppBuilderAndWriteJson } from \"./appBuilder\"\nimport { PM, detectPackageManager, getPackageManagerCommand } from \"../node-module-collector\"\nimport { NodeModuleDirInfo } from \"./packageDependencies\"\nimport { rebuild as remoteRebuild } from \"./rebuild/rebuild\"\nimport * as which from \"which\"\n\nexport async function installOrRebuild(config: Configuration, { appDir, projectDir }: DirectoryPaths, options: RebuildOptions, forceInstall = false) {\n  const effectiveOptions: RebuildOptions = {\n    buildFromSource: config.buildDependenciesFromSource === true,\n    additionalArgs: asArray(config.npmArgs),\n    ...options,\n  }\n  let isDependenciesInstalled = false\n\n  for (const fileOrDir of [\"node_modules\", \".pnp.js\"]) {\n    if (await pathExists(path.join(appDir, fileOrDir))) {\n      isDependenciesInstalled = true\n\n      break\n    }\n  }\n\n  if (forceInstall || !isDependenciesInstalled) {\n    await installDependencies(config, { appDir, projectDir }, effectiveOptions)\n  } else {\n    await rebuild(config, { appDir, projectDir }, effectiveOptions)\n  }\n}\n\nexport interface DesktopFrameworkInfo {\n  version: string\n  useCustomDist: boolean\n}\n\nfunction getElectronGypCacheDir() {\n  return path.join(homedir(), \".electron-gyp\")\n}\n\nexport function getGypEnv(frameworkInfo: DesktopFrameworkInfo, platform: NodeJS.Platform, arch: string, buildFromSource: boolean) {\n  const npmConfigArch = arch === \"armv7l\" ? \"arm\" : arch\n  const common: any = {\n    ...process.env,\n    npm_config_arch: npmConfigArch,\n    npm_config_target_arch: npmConfigArch,\n    npm_config_platform: platform,\n    npm_config_build_from_source: buildFromSource,\n    // required for node-pre-gyp\n    npm_config_target_platform: platform,\n    npm_config_update_binary: true,\n    npm_config_fallback_to_build: true,\n  }\n\n  if (platform !== process.platform) {\n    common.npm_config_force = \"true\"\n  }\n  if (platform === \"win32\" || platform === \"darwin\") {\n    common.npm_config_target_libc = \"unknown\"\n  }\n\n  if (!frameworkInfo.useCustomDist) {\n    return common\n  }\n\n  // https://github.com/nodejs/node-gyp/issues/21\n  return {\n    ...common,\n    npm_config_disturl: common.npm_config_electron_mirror || \"https://electronjs.org/headers\",\n    npm_config_target: frameworkInfo.version,\n    npm_config_runtime: \"electron\",\n    npm_config_devdir: getElectronGypCacheDir(),\n  }\n}\n\nexport async function installDependencies(config: Configuration, { appDir, projectDir }: DirectoryPaths, options: RebuildOptions): Promise<any> {\n  const platform = options.platform || process.platform\n  const arch = options.arch || process.arch\n  const additionalArgs = options.additionalArgs\n\n  const pm = detectPackageManager(projectDir)\n  log.info({ pm, platform, arch, projectDir, appDir }, `installing production dependencies`)\n  const execArgs = [\"install\"]\n  if (pm === PM.YARN_BERRY) {\n    if (process.env.NPM_NO_BIN_LINKS === \"true\") {\n      execArgs.push(\"--no-bin-links\")\n    }\n  }\n\n  if (pm === PM.YARN) {\n    execArgs.push(\"--prefer-offline\")\n  }\n\n  const execPath = getPackageManagerCommand(pm)\n\n  if (additionalArgs != null) {\n    execArgs.push(...additionalArgs)\n  }\n  await spawn(execPath, execArgs, {\n    cwd: appDir,\n    env: getGypEnv(options.frameworkInfo, platform, arch, options.buildFromSource === true),\n  })\n\n  // Some native dependencies no longer use `install` hook for building their native module, (yarn 3+ removed implicit link of `install` and `rebuild` steps)\n  // https://github.com/electron-userland/electron-builder/issues/8024\n  return rebuild(config, { appDir, projectDir }, options)\n}\n\nexport async function nodeGypRebuild(platform: NodeJS.Platform, arch: string, frameworkInfo: DesktopFrameworkInfo) {\n  log.info({ platform, arch }, \"executing node-gyp rebuild\")\n  // this script must be used only for electron\n  const nodeGyp = process.platform === \"win32\" ? which.sync(\"node-gyp\") : \"node-gyp\"\n  const args = [\"rebuild\"]\n  // headers of old Electron versions do not have a valid config.gypi file\n  // and --force-process-config must be passed to node-gyp >= 8.4.0 to\n  // correctly build modules for them.\n  // see also https://github.com/nodejs/node-gyp/pull/2497\n  const [major, minor] = frameworkInfo.version\n    .split(\".\")\n    .slice(0, 2)\n    .map(n => parseInt(n, 10))\n  if (major <= 13 || (major == 14 && minor <= 1) || (major == 15 && minor <= 2)) {\n    args.push(\"--force-process-config\")\n  }\n  await spawn(nodeGyp, args, { env: getGypEnv(frameworkInfo, platform, arch, true) })\n}\nexport interface RebuildOptions {\n  frameworkInfo: DesktopFrameworkInfo\n  productionDeps: Lazy<Array<NodeModuleDirInfo>>\n\n  platform?: NodeJS.Platform\n  arch?: string\n\n  buildFromSource?: boolean\n\n  additionalArgs?: Array<string> | null\n}\n\nexport interface DirectoryPaths {\n  appDir: string\n  projectDir: string\n}\n\n/** @internal */\nexport async function rebuild(config: Configuration, { appDir, projectDir }: DirectoryPaths, options: RebuildOptions) {\n  const configuration = {\n    dependencies: await options.productionDeps.value,\n    nodeExecPath: process.execPath,\n    platform: options.platform || process.platform,\n    arch: options.arch || process.arch,\n    additionalArgs: options.additionalArgs,\n    execPath: process.env.npm_execpath || process.env.NPM_CLI_JS,\n    buildFromSource: options.buildFromSource === true,\n  }\n  const { arch, buildFromSource, platform } = configuration\n\n  if (config.nativeRebuilder === \"legacy\") {\n    const env = getGypEnv(options.frameworkInfo, platform, arch, buildFromSource)\n    return executeAppBuilderAndWriteJson([\"rebuild-node-modules\"], configuration, { env, cwd: appDir })\n  }\n\n  const {\n    frameworkInfo: { version: electronVersion },\n  } = options\n  const logInfo = {\n    electronVersion,\n    arch,\n    buildFromSource,\n    appDir: log.filePath(appDir) || \"./\",\n  }\n  log.info(logInfo, \"executing @electron/rebuild\")\n\n  const rebuildOptions: electronRebuild.RebuildOptions = {\n    buildPath: appDir,\n    electronVersion,\n    arch,\n    platform,\n    buildFromSource,\n    projectRootPath: projectDir,\n    mode: (config.nativeRebuilder as RebuildMode) || \"sequential\",\n    disablePreGypCopy: true,\n  }\n  return remoteRebuild(rebuildOptions)\n}\n"]}