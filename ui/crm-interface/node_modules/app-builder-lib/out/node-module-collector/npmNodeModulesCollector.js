"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NpmNodeModulesCollector = void 0;
const nodeModulesCollector_1 = require("./nodeModulesCollector");
const packageManager_1 = require("./packageManager");
class NpmNodeModulesCollector extends nodeModulesCollector_1.NodeModulesCollector {
    constructor(rootDir) {
        super(rootDir);
        this.installOptions = { manager: packageManager_1.PM.NPM, lockfile: "package-lock.json" };
    }
    getArgs() {
        return ["list", "-a", "--include", "prod", "--include", "optional", "--omit", "dev", "--json", "--long", "--silent"];
    }
    collectAllDependencies(tree) {
        for (const [key, value] of Object.entries(tree.dependencies || {})) {
            const { _dependencies = {}, dependencies = {} } = value;
            const isDuplicateDep = Object.keys(_dependencies).length > 0 && Object.keys(dependencies).length === 0;
            if (isDuplicateDep) {
                continue;
            }
            this.allDependencies.set(`${key}@${value.version}`, value);
            this.collectAllDependencies(value);
        }
    }
    extractProductionDependencyGraph(tree, dependencyId) {
        var _a, _b;
        if (this.productionGraph[dependencyId]) {
            return;
        }
        const { _dependencies: prodDependencies = {}, dependencies = {} } = tree;
        const isDuplicateDep = Object.keys(prodDependencies).length > 0 && Object.keys(dependencies).length === 0;
        const resolvedDeps = isDuplicateDep ? ((_b = (_a = this.allDependencies.get(dependencyId)) === null || _a === void 0 ? void 0 : _a.dependencies) !== null && _b !== void 0 ? _b : {}) : dependencies;
        // Initialize with empty dependencies array first to mark this dependency as "in progress"
        // After initialization, if there are libraries with the same name+version later, they will not be searched recursively again
        // This will prevents infinite loops when circular dependencies are encountered.
        this.productionGraph[dependencyId] = { dependencies: [] };
        const productionDeps = Object.entries(resolvedDeps)
            .filter(([packageName]) => prodDependencies[packageName])
            .map(([packageName, dependency]) => {
            const childDependencyId = `${packageName}@${dependency.version}`;
            this.extractProductionDependencyGraph(dependency, childDependencyId);
            return childDependencyId;
        });
        this.productionGraph[dependencyId] = { dependencies: productionDeps };
    }
    parseDependenciesTree(jsonBlob) {
        return JSON.parse(jsonBlob);
    }
}
exports.NpmNodeModulesCollector = NpmNodeModulesCollector;
//# sourceMappingURL=npmNodeModulesCollector.js.map