import { NodeModulesCollector } from "./nodeModulesCollector";
import { PM } from "./packageManager";
import { PnpmDependency } from "./types";
export declare class PnpmNodeModulesCollector extends NodeModulesCollector<PnpmDependency, PnpmDependency> {
    constructor(rootDir: string);
    readonly installOptions: {
        manager: PM;
        lockfile: string;
    };
    protected getArgs(): string[];
    extractProductionDependencyGraph(tree: PnpmDependency, dependencyId: string): void;
    protected collectAllDependencies(tree: PnpmDependency): void;
    protected parseDependenciesTree(jsonBlob: string): PnpmDependency;
}
