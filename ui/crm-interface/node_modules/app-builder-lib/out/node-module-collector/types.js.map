{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/node-module-collector/types.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface NodeModuleInfo {\n  name: string\n  version: string\n  dir: string\n  dependencies?: Array<NodeModuleInfo>\n}\n\nexport type ParsedDependencyTree = {\n  readonly name: string\n  readonly version: string\n  readonly path: string\n  readonly workspaces?: string[] // we only use this at root level\n}\n\n// Note: `PnpmDependency` and `NpmDependency` include the output of `JSON.parse(...)` of `pnpm list` and `npm list` respectively\n// This object has a TON of info - a majority, if not all, of each dependency's package.json\n// We extract only what we need when constructing DependencyTree in `extractProductionDependencyTree`\nexport interface PnpmDependency extends Dependency<PnpmDependency, PnpmDependency> {\n  readonly from: string\n}\n\nexport interface NpmDependency extends Dependency<NpmDependency, string> {\n  // implicit dependencies\n  readonly _dependencies?: {\n    [packageName: string]: string\n  }\n}\n\nexport type Dependency<T, V> = Dependencies<T, V> & ParsedDependencyTree\n\nexport type Dependencies<T, V> = {\n  readonly dependencies?: {\n    [packageName: string]: T\n  }\n  readonly optionalDependencies?: {\n    [packageName: string]: V\n  }\n}\n\nexport interface DependencyGraph {\n  [packageNameAndVersion: string]: PackageDependencies\n}\n\ninterface PackageDependencies {\n  readonly dependencies: string[]\n}\n"]}