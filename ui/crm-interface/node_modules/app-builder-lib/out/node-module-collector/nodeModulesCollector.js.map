{"version": 3, "file": "nodeModulesCollector.js", "sourceRoot": "", "sources": ["../../src/node-module-collector/nodeModulesCollector.ts"], "names": [], "mappings": ";;;AAAA,mCAAqE;AACrE,6BAA4B;AAC5B,yBAAwB;AAExB,+CAAwC;AACxC,qDAA+D;AAE/D,MAAsB,oBAAoB;IAKxC,YAA6B,OAAe;QAAf,YAAO,GAAP,OAAO,CAAQ;QAJpC,gBAAW,GAAqB,EAAE,CAAA;QAChC,oBAAe,GAAmB,IAAI,GAAG,EAAE,CAAA;QAC3C,oBAAe,GAAoB,EAAE,CAAA;IAEA,CAAC;IAEzC,KAAK,CAAC,cAAc;QACzB,MAAM,IAAI,GAAM,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAA;QAChD,MAAM,QAAQ,GAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAA;QACpD,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAA;QACrC,IAAI,CAAC,gCAAgC,CAAC,QAAQ,EAAE,GAAG,CAAC,qBAAqB,CAAC,CAAA;QAE1E,MAAM,aAAa,GAAkB,IAAA,aAAK,EAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;QAC1G,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;QAElE,OAAO,IAAI,CAAC,WAAW,CAAA;IACzB,CAAC;IAYS,KAAK,CAAC,mBAAmB;QACjC,MAAM,OAAO,GAAG,IAAA,yCAAwB,EAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA;QACrE,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAA;QAC3B,MAAM,YAAY,GAAG,MAAM,IAAA,mBAAI,EAAC,OAAO,EAAE,IAAI,EAAE;YAC7C,GAAG,EAAE,IAAI,CAAC,OAAO;YACjB,KAAK,EAAE,IAAI;SACZ,CAAC,CAAA;QACF,OAAO,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAA;IACjD,CAAC;IAES,WAAW,CAAC,QAAgB;QACpC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;YACpC,IAAI,KAAK,CAAC,cAAc,EAAE,EAAE,CAAC;gBAC3B,OAAO,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAA;YAClC,CAAC;iBAAM,CAAC;gBACN,OAAO,QAAQ,CAAA;YACjB,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,kBAAG,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE,sBAAsB,CAAC,CAAA;YAC5E,OAAO,QAAQ,CAAA;QACjB,CAAC;IACH,CAAC;IAEO,qBAAqB,CAAC,IAAO;QACnC,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACzC,MAAM,WAAW,GAA+B,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAA;YAChG,MAAM,cAAc,GAAG,WAAW,CAAC,IAAI,CAAA;YACvC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC7D,IAAI,GAAG,KAAK,cAAc,EAAE,CAAC;oBAC3B,OAAO,KAAK,CAAA;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAEO,kBAAkB,CAAC,GAAoB,EAAE,MAAc,GAAG,EAAE,QAAkC,IAAI,GAAG,EAAE;QAC7G,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QACzB,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAE,CAAC,CAAC,CAAC,CAAA;QACrC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,GAAG;gBACL,IAAI;gBACJ,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,GAAG,CAAC,KAAK,CAAC,gBAAgB,CAAE,CAAC,CAAC,CAAC,IAAI,EAAE;gBAChD,YAAY,EAAE,IAAI,GAAG,EAAe;gBACpC,SAAS,EAAE,IAAI,GAAG,CAAS,EAAE,CAAC;aAC/B,CAAA;YACD,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;YAEpB,KAAK,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;gBACtD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,CAAA;YACjE,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAEO,eAAe,CAAC,YAAgC,EAAE,MAAwB;;QAChF,IAAI,YAAY,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAM;QACR,CAAC;QAED,KAAK,MAAM,CAAC,IAAI,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;YACtC,MAAM,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;YACtC,MAAM,CAAC,GAAG,MAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,SAAS,EAAE,CAAC,0CAAE,IAAI,CAAA;YAClE,IAAI,CAAC,KAAK,SAAS,EAAE,CAAC;gBACpB,kBAAG,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,iCAAiC,CAAC,CAAA;gBACzE,SAAQ;YACV,CAAC;YACD,MAAM,IAAI,GAAmB;gBAC3B,IAAI,EAAE,CAAC,CAAC,IAAI;gBACZ,OAAO,EAAE,SAAS;gBAClB,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;aACzB,CAAA;YACD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACjB,IAAI,CAAC,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;gBAC5B,IAAI,CAAC,YAAY,GAAG,EAAE,CAAA;gBACtB,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,CAAA;YACzD,CAAC;QACH,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;IACrD,CAAC;CACF;AAhHD,oDAgHC", "sourcesContent": ["import { hoist, type HoisterTree, type HoisterResult } from \"./hoist\"\nimport * as path from \"path\"\nimport * as fs from \"fs\"\nimport type { NodeModuleInfo, DependencyGraph, Dependency } from \"./types\"\nimport { exec, log } from \"builder-util\"\nimport { getPackageManagerCommand, PM } from \"./packageManager\"\n\nexport abstract class NodeModulesCollector<T extends Dependency<T, OptionalsType>, OptionalsType> {\n  private nodeModules: NodeModuleInfo[] = []\n  protected allDependencies: Map<string, T> = new Map()\n  protected productionGraph: DependencyGraph = {}\n\n  constructor(private readonly rootDir: string) {}\n\n  public async getNodeModules(): Promise<NodeModuleInfo[]> {\n    const tree: T = await this.getDependenciesTree()\n    const realTree: T = this.getTreeFromWorkspaces(tree)\n    this.collectAllDependencies(realTree)\n    this.extractProductionDependencyGraph(realTree, \".\" /*root project name*/)\n\n    const hoisterResult: HoisterResult = hoist(this.transToHoisterTree(this.productionGraph), { check: true })\n    this._getNodeModules(hoisterResult.dependencies, this.nodeModules)\n\n    return this.nodeModules\n  }\n\n  public abstract readonly installOptions: {\n    manager: PM\n    lockfile: string\n  }\n\n  protected abstract getArgs(): string[]\n  protected abstract parseDependenciesTree(jsonBlob: string): T\n  protected abstract extractProductionDependencyGraph(tree: Dependency<T, OptionalsType>, dependencyId: string): void\n  protected abstract collectAllDependencies(tree: Dependency<T, OptionalsType>): void\n\n  protected async getDependenciesTree(): Promise<T> {\n    const command = getPackageManagerCommand(this.installOptions.manager)\n    const args = this.getArgs()\n    const dependencies = await exec(command, args, {\n      cwd: this.rootDir,\n      shell: true,\n    })\n    return this.parseDependenciesTree(dependencies)\n  }\n\n  protected resolvePath(filePath: string): string {\n    try {\n      const stats = fs.lstatSync(filePath)\n      if (stats.isSymbolicLink()) {\n        return fs.realpathSync(filePath)\n      } else {\n        return filePath\n      }\n    } catch (error: any) {\n      log.debug({ message: error.message || error.stack }, \"error resolving path\")\n      return filePath\n    }\n  }\n\n  private getTreeFromWorkspaces(tree: T): T {\n    if (tree.workspaces && tree.dependencies) {\n      const packageJson: Dependency<string, string> = require(path.join(this.rootDir, \"package.json\"))\n      const dependencyName = packageJson.name\n      for (const [key, value] of Object.entries(tree.dependencies)) {\n        if (key === dependencyName) {\n          return value\n        }\n      }\n    }\n\n    return tree\n  }\n\n  private transToHoisterTree(obj: DependencyGraph, key: string = `.`, nodes: Map<string, HoisterTree> = new Map()): HoisterTree {\n    let node = nodes.get(key)\n    const name = key.match(/@?[^@]+/)![0]\n    if (!node) {\n      node = {\n        name,\n        identName: name,\n        reference: key.match(/@?[^@]+@?(.+)?/)![1] || ``,\n        dependencies: new Set<HoisterTree>(),\n        peerNames: new Set<string>([]),\n      }\n      nodes.set(key, node)\n\n      for (const dep of (obj[key] || {}).dependencies || []) {\n        node.dependencies.add(this.transToHoisterTree(obj, dep, nodes))\n      }\n    }\n    return node\n  }\n\n  private _getNodeModules(dependencies: Set<HoisterResult>, result: NodeModuleInfo[]) {\n    if (dependencies.size === 0) {\n      return\n    }\n\n    for (const d of dependencies.values()) {\n      const reference = [...d.references][0]\n      const p = this.allDependencies.get(`${d.name}@${reference}`)?.path\n      if (p === undefined) {\n        log.debug({ name: d.name, reference }, \"cannot find path for dependency\")\n        continue\n      }\n      const node: NodeModuleInfo = {\n        name: d.name,\n        version: reference,\n        dir: this.resolvePath(p),\n      }\n      result.push(node)\n      if (d.dependencies.size > 0) {\n        node.dependencies = []\n        this._getNodeModules(d.dependencies, node.dependencies)\n      }\n    }\n    result.sort((a, b) => a.name.localeCompare(b.name))\n  }\n}\n"]}