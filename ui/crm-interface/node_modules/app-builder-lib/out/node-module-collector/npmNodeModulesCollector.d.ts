import { NodeModulesCollector } from "./nodeModulesCollector";
import { PM } from "./packageManager";
import { NpmDependency } from "./types";
export declare class NpmNodeModulesCollector extends NodeModulesCollector<NpmDependency, string> {
    constructor(rootDir: string);
    readonly installOptions: {
        manager: PM;
        lockfile: string;
    };
    protected getArgs(): string[];
    protected collectAllDependencies(tree: NpmDependency): void;
    protected extractProductionDependencyGraph(tree: NpmDependency, dependencyId: string): void;
    protected parseDependenciesTree(jsonBlob: string): NpmDependency;
}
