{"version": 3, "file": "yarnNodeModulesCollector.js", "sourceRoot": "", "sources": ["../../src/node-module-collector/yarnNodeModulesCollector.ts"], "names": [], "mappings": ";;;AAAA,uEAAmE;AACnE,qDAAqC;AAErC,MAAa,wBAAyB,SAAQ,iDAAuB;IACnE,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,CAAC,CAAA;QAGhB,6FAA6F;QAC7E,mBAAc,GAAG,EAAE,OAAO,EAAE,mBAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAA;IAH5E,CAAC;CAIF;AAPD,4DAOC", "sourcesContent": ["import { NpmNodeModulesCollector } from \"./npmNodeModulesCollector\"\nimport { PM } from \"./packageManager\"\n\nexport class YarnNodeModulesCollector extends NpmNodeModulesCollector {\n  constructor(rootDir: string) {\n    super(rootDir)\n  }\n\n  // note: do not override instance-var `pmCommand`. We explicitly use npm for the json payload\n  public readonly installOptions = { manager: PM.YARN, lockfile: \"yarn.lock\" }\n}\n"]}