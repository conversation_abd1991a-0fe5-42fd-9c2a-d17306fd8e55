"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NodeModulesCollector = void 0;
const hoist_1 = require("./hoist");
const path = require("path");
const fs = require("fs");
const builder_util_1 = require("builder-util");
const packageManager_1 = require("./packageManager");
class NodeModulesCollector {
    constructor(rootDir) {
        this.rootDir = rootDir;
        this.nodeModules = [];
        this.allDependencies = new Map();
        this.productionGraph = {};
    }
    async getNodeModules() {
        const tree = await this.getDependenciesTree();
        const realTree = this.getTreeFromWorkspaces(tree);
        this.collectAllDependencies(realTree);
        this.extractProductionDependencyGraph(realTree, "." /*root project name*/);
        const hoisterResult = (0, hoist_1.hoist)(this.transToHoisterTree(this.productionGraph), { check: true });
        this._getNodeModules(hoisterResult.dependencies, this.nodeModules);
        return this.nodeModules;
    }
    async getDependenciesTree() {
        const command = (0, packageManager_1.getPackageManagerCommand)(this.installOptions.manager);
        const args = this.getArgs();
        const dependencies = await (0, builder_util_1.exec)(command, args, {
            cwd: this.rootDir,
            shell: true,
        });
        return this.parseDependenciesTree(dependencies);
    }
    resolvePath(filePath) {
        try {
            const stats = fs.lstatSync(filePath);
            if (stats.isSymbolicLink()) {
                return fs.realpathSync(filePath);
            }
            else {
                return filePath;
            }
        }
        catch (error) {
            builder_util_1.log.debug({ message: error.message || error.stack }, "error resolving path");
            return filePath;
        }
    }
    getTreeFromWorkspaces(tree) {
        if (tree.workspaces && tree.dependencies) {
            const packageJson = require(path.join(this.rootDir, "package.json"));
            const dependencyName = packageJson.name;
            for (const [key, value] of Object.entries(tree.dependencies)) {
                if (key === dependencyName) {
                    return value;
                }
            }
        }
        return tree;
    }
    transToHoisterTree(obj, key = `.`, nodes = new Map()) {
        let node = nodes.get(key);
        const name = key.match(/@?[^@]+/)[0];
        if (!node) {
            node = {
                name,
                identName: name,
                reference: key.match(/@?[^@]+@?(.+)?/)[1] || ``,
                dependencies: new Set(),
                peerNames: new Set([]),
            };
            nodes.set(key, node);
            for (const dep of (obj[key] || {}).dependencies || []) {
                node.dependencies.add(this.transToHoisterTree(obj, dep, nodes));
            }
        }
        return node;
    }
    _getNodeModules(dependencies, result) {
        var _a;
        if (dependencies.size === 0) {
            return;
        }
        for (const d of dependencies.values()) {
            const reference = [...d.references][0];
            const p = (_a = this.allDependencies.get(`${d.name}@${reference}`)) === null || _a === void 0 ? void 0 : _a.path;
            if (p === undefined) {
                builder_util_1.log.debug({ name: d.name, reference }, "cannot find path for dependency");
                continue;
            }
            const node = {
                name: d.name,
                version: reference,
                dir: this.resolvePath(p),
            };
            result.push(node);
            if (d.dependencies.size > 0) {
                node.dependencies = [];
                this._getNodeModules(d.dependencies, node.dependencies);
            }
        }
        result.sort((a, b) => a.name.localeCompare(b.name));
    }
}
exports.NodeModulesCollector = NodeModulesCollector;
//# sourceMappingURL=nodeModulesCollector.js.map