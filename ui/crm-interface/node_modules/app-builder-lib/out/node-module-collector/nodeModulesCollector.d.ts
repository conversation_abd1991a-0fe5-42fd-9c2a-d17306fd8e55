import type { NodeModuleInfo, DependencyGraph, Dependency } from "./types";
import { PM } from "./packageManager";
export declare abstract class NodeModulesCollector<T extends Dependency<T, OptionalsType>, OptionalsType> {
    private readonly rootDir;
    private nodeModules;
    protected allDependencies: Map<string, T>;
    protected productionGraph: DependencyGraph;
    constructor(rootDir: string);
    getNodeModules(): Promise<NodeModuleInfo[]>;
    abstract readonly installOptions: {
        manager: PM;
        lockfile: string;
    };
    protected abstract getArgs(): string[];
    protected abstract parseDependenciesTree(jsonBlob: string): T;
    protected abstract extractProductionDependencyGraph(tree: Dependency<T, OptionalsType>, dependencyId: string): void;
    protected abstract collectAllDependencies(tree: Dependency<T, OptionalsType>): void;
    protected getDependenciesTree(): Promise<T>;
    protected resolvePath(filePath: string): string;
    private getTreeFromWorkspaces;
    private transToHoisterTree;
    private _getNodeModules;
}
