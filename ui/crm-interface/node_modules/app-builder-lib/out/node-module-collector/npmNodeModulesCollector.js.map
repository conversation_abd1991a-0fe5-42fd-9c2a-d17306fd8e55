{"version": 3, "file": "npmNodeModulesCollector.js", "sourceRoot": "", "sources": ["../../src/node-module-collector/npmNodeModulesCollector.ts"], "names": [], "mappings": ";;;AAAA,iEAA6D;AAC7D,qDAAqC;AAGrC,MAAa,uBAAwB,SAAQ,2CAA2C;IACtF,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,CAAC,CAAA;QAGA,mBAAc,GAAG,EAAE,OAAO,EAAE,mBAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,mBAAmB,EAAE,CAAA;IAFnF,CAAC;IAIS,OAAO;QACf,OAAO,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAA;IACtH,CAAC;IAES,sBAAsB,CAAC,IAAmB;QAClD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC,EAAE,CAAC;YACnE,MAAM,EAAE,aAAa,GAAG,EAAE,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,KAAK,CAAA;YACvD,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,KAAK,CAAC,CAAA;YACtG,IAAI,cAAc,EAAE,CAAC;gBACnB,SAAQ;YACV,CAAC;YACD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAA;YAC1D,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAA;QACpC,CAAC;IACH,CAAC;IAES,gCAAgC,CAAC,IAAmB,EAAE,YAAoB;;QAClF,IAAI,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,EAAE,CAAC;YACvC,OAAM;QACR,CAAC;QAED,MAAM,EAAE,aAAa,EAAE,gBAAgB,GAAG,EAAE,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,IAAI,CAAA;QACxE,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,KAAK,CAAC,CAAA;QACzG,MAAM,YAAY,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,MAAA,MAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,YAAY,CAAC,0CAAE,YAAY,mCAAI,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY,CAAA;QACjH,0FAA0F;QAC1F,6HAA6H;QAC7H,gFAAgF;QAChF,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE,EAAE,CAAA;QACzD,MAAM,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC;aAChD,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;aACxD,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,EAAE,EAAE;YACjC,MAAM,iBAAiB,GAAG,GAAG,WAAW,IAAI,UAAU,CAAC,OAAO,EAAE,CAAA;YAChE,IAAI,CAAC,gCAAgC,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAA;YACpE,OAAO,iBAAiB,CAAA;QAC1B,CAAC,CAAC,CAAA;QACJ,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,GAAG,EAAE,YAAY,EAAE,cAAc,EAAE,CAAA;IACvE,CAAC;IAES,qBAAqB,CAAC,QAAgB;QAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;IAC7B,CAAC;CACF;AAhDD,0DAgDC", "sourcesContent": ["import { NodeModulesCollector } from \"./nodeModulesCollector\"\nimport { PM } from \"./packageManager\"\nimport { NpmDependency } from \"./types\"\n\nexport class NpmNodeModulesCollector extends NodeModulesCollector<NpmDependency, string> {\n  constructor(rootDir: string) {\n    super(rootDir)\n  }\n\n  public readonly installOptions = { manager: PM.NPM, lockfile: \"package-lock.json\" }\n\n  protected getArgs(): string[] {\n    return [\"list\", \"-a\", \"--include\", \"prod\", \"--include\", \"optional\", \"--omit\", \"dev\", \"--json\", \"--long\", \"--silent\"]\n  }\n\n  protected collectAllDependencies(tree: NpmDependency) {\n    for (const [key, value] of Object.entries(tree.dependencies || {})) {\n      const { _dependencies = {}, dependencies = {} } = value\n      const isDuplicateDep = Object.keys(_dependencies).length > 0 && Object.keys(dependencies).length === 0\n      if (isDuplicateDep) {\n        continue\n      }\n      this.allDependencies.set(`${key}@${value.version}`, value)\n      this.collectAllDependencies(value)\n    }\n  }\n\n  protected extractProductionDependencyGraph(tree: NpmDependency, dependencyId: string): void {\n    if (this.productionGraph[dependencyId]) {\n      return\n    }\n\n    const { _dependencies: prodDependencies = {}, dependencies = {} } = tree\n    const isDuplicateDep = Object.keys(prodDependencies).length > 0 && Object.keys(dependencies).length === 0\n    const resolvedDeps = isDuplicateDep ? (this.allDependencies.get(dependencyId)?.dependencies ?? {}) : dependencies\n    // Initialize with empty dependencies array first to mark this dependency as \"in progress\"\n    // After initialization, if there are libraries with the same name+version later, they will not be searched recursively again\n    // This will prevents infinite loops when circular dependencies are encountered.\n    this.productionGraph[dependencyId] = { dependencies: [] }\n    const productionDeps = Object.entries(resolvedDeps)\n      .filter(([packageName]) => prodDependencies[packageName])\n      .map(([packageName, dependency]) => {\n        const childDependencyId = `${packageName}@${dependency.version}`\n        this.extractProductionDependencyGraph(dependency, childDependencyId)\n        return childDependencyId\n      })\n    this.productionGraph[dependencyId] = { dependencies: productionDeps }\n  }\n\n  protected parseDependenciesTree(jsonBlob: string): NpmDependency {\n    return JSON.parse(jsonBlob)\n  }\n}\n"]}