"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PM = void 0;
exports.getPackageManagerCommand = getPackageManagerCommand;
exports.detectPackageManagerByEnv = detectPackageManagerByEnv;
exports.detectPackageManager = detectPackageManager;
const path = require("path");
const fs = require("fs");
const which = require("which");
var PM;
(function (PM) {
    PM["NPM"] = "npm";
    PM["YARN"] = "yarn";
    PM["PNPM"] = "pnpm";
    PM["YARN_BERRY"] = "yarn-berry";
})(PM || (exports.PM = PM = {}));
// Cache for resolved paths
const pmPathCache = {
    [PM.NPM]: undefined,
    [PM.YARN]: undefined,
    [PM.PNPM]: undefined,
    [PM.YARN_BERRY]: undefined,
};
function resolveCommand(pm) {
    const fallback = pm === PM.YARN_BERRY ? "yarn" : pm;
    if (process.platform !== "win32") {
        return fallback;
    }
    try {
        return path.basename(which.sync(fallback));
    }
    catch {
        // If `which` fails (not found), still return the fallback string
        return fallback;
    }
}
function getPackageManagerCommand(pm) {
    if (pmPathCache[pm] !== undefined) {
        return pmPathCache[pm];
    }
    const resolved = resolveCommand(pm);
    pmPathCache[pm] = resolved;
    return resolved;
}
function detectPackageManagerByEnv() {
    var _a, _b, _c;
    const ua = (_a = process.env.npm_config_user_agent) !== null && _a !== void 0 ? _a : "";
    const execPath = (_c = (_b = process.env.npm_execpath) === null || _b === void 0 ? void 0 : _b.toLowerCase()) !== null && _c !== void 0 ? _c : "";
    const yarnVersion = process.env.YARN_VERSION;
    const isBerry = (yarnVersion === null || yarnVersion === void 0 ? void 0 : yarnVersion.startsWith("2.")) || (yarnVersion === null || yarnVersion === void 0 ? void 0 : yarnVersion.startsWith("3."));
    if (ua.includes("pnpm") || execPath.includes("pnpm") || process.env.PNPM_HOME) {
        return PM.PNPM;
    }
    if (ua.includes("yarn") || execPath.includes("yarn") || process.env.YARN_REGISTRY) {
        return isBerry || ua.includes("yarn/2") || ua.includes("yarn/3") ? PM.YARN_BERRY : PM.YARN;
    }
    if (ua.includes("npm") || execPath.includes("npm") || process.env.npm_package_json) {
        return PM.NPM;
    }
    return PM.NPM;
}
function detectPackageManager(cwd) {
    const has = (file) => fs.existsSync(path.join(cwd, file));
    const yarn = has("yarn.lock");
    const pnpm = has("pnpm-lock.yaml");
    const npm = has("package-lock.json");
    const detected = [];
    if (yarn)
        detected.push(PM.YARN);
    if (pnpm)
        detected.push(PM.PNPM);
    if (npm)
        detected.push(PM.NPM);
    if (detected.length === 1) {
        return detected[0] === PM.YARN ? detectPackageManagerByEnv() : detected[0];
    }
    // fallback: multiple lockfiles or none
    return detectPackageManagerByEnv();
}
//# sourceMappingURL=packageManager.js.map