{"version": 3, "file": "pnpmNodeModulesCollector.js", "sourceRoot": "", "sources": ["../../src/node-module-collector/pnpmNodeModulesCollector.ts"], "names": [], "mappings": ";;;AAAA,+CAAkC;AAClC,yBAAwB;AACxB,6BAA4B;AAC5B,iEAA6D;AAC7D,qDAAqC;AAGrC,MAAa,wBAAyB,SAAQ,2CAAoD;IAChG,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,CAAC,CAAA;QAGA,mBAAc,GAAG,EAAE,OAAO,EAAE,mBAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,gBAAgB,EAAE,CAAA;IAFjF,CAAC;IAIS,OAAO;QACf,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC,CAAA;IAC5D,CAAC;IAED,gCAAgC,CAAC,IAAoB,EAAE,YAAoB;QACzE,IAAI,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,EAAE,CAAC;YACvC,OAAM;QACR,CAAC;QAED,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;QACrD,MAAM,WAAW,GAA+B,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAA;QACrF,MAAM,gBAAgB,GAAG,EAAE,GAAG,WAAW,CAAC,YAAY,EAAE,GAAG,WAAW,CAAC,oBAAoB,EAAE,CAAA;QAE7F,MAAM,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,oBAAoB,IAAI,EAAE,CAAC,EAAE,CAAA;QACnF,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE,EAAE,CAAA;QACzD,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;aACtC,MAAM,CAAC,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,EAAE,EAAE;YACpC,iDAAiD;YACjD,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,EAAE,CAAC;gBACnC,OAAO,KAAK,CAAA;YACd,CAAC;YAED,gDAAgD;YAChD,IAAI,WAAW,CAAC,oBAAoB,IAAI,WAAW,CAAC,oBAAoB,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;gBACzH,kBAAG,CAAC,KAAK,CAAC,IAAI,EAAE,uBAAuB,WAAW,IAAI,UAAU,CAAC,OAAO,wBAAwB,UAAU,CAAC,IAAI,EAAE,CAAC,CAAA;gBAClH,OAAO,KAAK,CAAA;YACd,CAAC;YAED,OAAO,IAAI,CAAA;QACb,CAAC,CAAC;aACD,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,EAAE,EAAE;YACjC,MAAM,iBAAiB,GAAG,GAAG,WAAW,IAAI,UAAU,CAAC,OAAO,EAAE,CAAA;YAChE,IAAI,CAAC,gCAAgC,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAA;YACpE,OAAO,iBAAiB,CAAA;QAC1B,CAAC,CAAC,CAAA;QAEJ,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,GAAG,EAAE,YAAY,EAAE,CAAA;IACvD,CAAC;IAES,sBAAsB,CAAC,IAAoB;QACnD,+BAA+B;QAC/B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC,EAAE,CAAC;YACnE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAA;YAC1D,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAA;QACpC,CAAC;QAED,8CAA8C;QAC9C,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,IAAI,EAAE,CAAC,EAAE,CAAC;YAC3E,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAA;YAC1D,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAA;QACpC,CAAC;IACH,CAAC;IAES,qBAAqB,CAAC,QAAgB;QAC9C,MAAM,cAAc,GAAqB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QAC7D,4CAA4C;QAC5C,OAAO,cAAc,CAAC,CAAC,CAAC,CAAA;IAC1B,CAAC;CACF;AAjED,4DAiEC", "sourcesContent": ["import { log } from \"builder-util\"\nimport * as fs from \"fs\"\nimport * as path from \"path\"\nimport { NodeModulesCollector } from \"./nodeModulesCollector\"\nimport { PM } from \"./packageManager\"\nimport { Dependency, PnpmDependency } from \"./types\"\n\nexport class PnpmNodeModulesCollector extends NodeModulesCollector<PnpmDependency, PnpmDependency> {\n  constructor(rootDir: string) {\n    super(rootDir)\n  }\n\n  public readonly installOptions = { manager: PM.PNPM, lockfile: \"pnpm-lock.yaml\" }\n\n  protected getArgs(): string[] {\n    return [\"list\", \"--prod\", \"--json\", \"--depth\", \"Infinity\"]\n  }\n\n  extractProductionDependencyGraph(tree: PnpmDependency, dependencyId: string): void {\n    if (this.productionGraph[dependencyId]) {\n      return\n    }\n\n    const p = path.normalize(this.resolvePath(tree.path))\n    const packageJson: Dependency<string, string> = require(path.join(p, \"package.json\"))\n    const prodDependencies = { ...packageJson.dependencies, ...packageJson.optionalDependencies }\n\n    const deps = { ...(tree.dependencies || {}), ...(tree.optionalDependencies || {}) }\n    this.productionGraph[dependencyId] = { dependencies: [] }\n    const dependencies = Object.entries(deps)\n      .filter(([packageName, dependency]) => {\n        // First check if it's in production dependencies\n        if (!prodDependencies[packageName]) {\n          return false\n        }\n\n        // Then check if optional dependency path exists\n        if (packageJson.optionalDependencies && packageJson.optionalDependencies[packageName] && !fs.existsSync(dependency.path)) {\n          log.debug(null, `Optional dependency ${packageName}@${dependency.version} path doesn't exist: ${dependency.path}`)\n          return false\n        }\n\n        return true\n      })\n      .map(([packageName, dependency]) => {\n        const childDependencyId = `${packageName}@${dependency.version}`\n        this.extractProductionDependencyGraph(dependency, childDependencyId)\n        return childDependencyId\n      })\n\n    this.productionGraph[dependencyId] = { dependencies }\n  }\n\n  protected collectAllDependencies(tree: PnpmDependency) {\n    // Collect regular dependencies\n    for (const [key, value] of Object.entries(tree.dependencies || {})) {\n      this.allDependencies.set(`${key}@${value.version}`, value)\n      this.collectAllDependencies(value)\n    }\n\n    // Collect optional dependencies if they exist\n    for (const [key, value] of Object.entries(tree.optionalDependencies || {})) {\n      this.allDependencies.set(`${key}@${value.version}`, value)\n      this.collectAllDependencies(value)\n    }\n  }\n\n  protected parseDependenciesTree(jsonBlob: string): PnpmDependency {\n    const dependencyTree: PnpmDependency[] = JSON.parse(jsonBlob)\n    // pnpm returns an array of dependency trees\n    return dependencyTree[0]\n  }\n}\n"]}