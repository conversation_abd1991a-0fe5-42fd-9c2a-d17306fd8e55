"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PnpmNodeModulesCollector = void 0;
const builder_util_1 = require("builder-util");
const fs = require("fs");
const path = require("path");
const nodeModulesCollector_1 = require("./nodeModulesCollector");
const packageManager_1 = require("./packageManager");
class PnpmNodeModulesCollector extends nodeModulesCollector_1.NodeModulesCollector {
    constructor(rootDir) {
        super(rootDir);
        this.installOptions = { manager: packageManager_1.PM.PNPM, lockfile: "pnpm-lock.yaml" };
    }
    getArgs() {
        return ["list", "--prod", "--json", "--depth", "Infinity"];
    }
    extractProductionDependencyGraph(tree, dependencyId) {
        if (this.productionGraph[dependencyId]) {
            return;
        }
        const p = path.normalize(this.resolvePath(tree.path));
        const packageJson = require(path.join(p, "package.json"));
        const prodDependencies = { ...packageJson.dependencies, ...packageJson.optionalDependencies };
        const deps = { ...(tree.dependencies || {}), ...(tree.optionalDependencies || {}) };
        this.productionGraph[dependencyId] = { dependencies: [] };
        const dependencies = Object.entries(deps)
            .filter(([packageName, dependency]) => {
            // First check if it's in production dependencies
            if (!prodDependencies[packageName]) {
                return false;
            }
            // Then check if optional dependency path exists
            if (packageJson.optionalDependencies && packageJson.optionalDependencies[packageName] && !fs.existsSync(dependency.path)) {
                builder_util_1.log.debug(null, `Optional dependency ${packageName}@${dependency.version} path doesn't exist: ${dependency.path}`);
                return false;
            }
            return true;
        })
            .map(([packageName, dependency]) => {
            const childDependencyId = `${packageName}@${dependency.version}`;
            this.extractProductionDependencyGraph(dependency, childDependencyId);
            return childDependencyId;
        });
        this.productionGraph[dependencyId] = { dependencies };
    }
    collectAllDependencies(tree) {
        // Collect regular dependencies
        for (const [key, value] of Object.entries(tree.dependencies || {})) {
            this.allDependencies.set(`${key}@${value.version}`, value);
            this.collectAllDependencies(value);
        }
        // Collect optional dependencies if they exist
        for (const [key, value] of Object.entries(tree.optionalDependencies || {})) {
            this.allDependencies.set(`${key}@${value.version}`, value);
            this.collectAllDependencies(value);
        }
    }
    parseDependenciesTree(jsonBlob) {
        const dependencyTree = JSON.parse(jsonBlob);
        // pnpm returns an array of dependency trees
        return dependencyTree[0];
    }
}
exports.PnpmNodeModulesCollector = PnpmNodeModulesCollector;
//# sourceMappingURL=pnpmNodeModulesCollector.js.map