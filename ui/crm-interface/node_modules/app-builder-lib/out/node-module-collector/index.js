"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPackageManagerCommand = exports.PM = exports.detectPackageManager = void 0;
exports.getCollectorByPackageManager = getCollectorByPackageManager;
exports.getNodeModules = getNodeModules;
const npmNodeModulesCollector_1 = require("./npmNodeModulesCollector");
const pnpmNodeModulesCollector_1 = require("./pnpmNodeModulesCollector");
const yarnNodeModulesCollector_1 = require("./yarnNodeModulesCollector");
const packageManager_1 = require("./packageManager");
Object.defineProperty(exports, "detectPackageManager", { enumerable: true, get: function () { return packageManager_1.detectPackageManager; } });
Object.defineProperty(exports, "PM", { enumerable: true, get: function () { return packageManager_1.PM; } });
Object.defineProperty(exports, "getPackageManagerCommand", { enumerable: true, get: function () { return packageManager_1.getPackageManagerCommand; } });
const builder_util_1 = require("builder-util");
async function isPnpmProjectHoisted(rootDir) {
    const command = (0, packageManager_1.getPackageManagerCommand)(packageManager_1.PM.PNPM);
    const config = await (0, builder_util_1.exec)(command, ["config", "list"], { cwd: rootDir, shell: true });
    const lines = Object.fromEntries(config.split("\n").map(line => line.split("=").map(s => s.trim())));
    return lines["node-linker"] === "hoisted";
}
async function getCollectorByPackageManager(rootDir) {
    const manager = (0, packageManager_1.detectPackageManager)(rootDir);
    switch (manager) {
        case packageManager_1.PM.PNPM:
            if (await isPnpmProjectHoisted(rootDir)) {
                return new npmNodeModulesCollector_1.NpmNodeModulesCollector(rootDir);
            }
            return new pnpmNodeModulesCollector_1.PnpmNodeModulesCollector(rootDir);
        case packageManager_1.PM.NPM:
            return new npmNodeModulesCollector_1.NpmNodeModulesCollector(rootDir);
        case packageManager_1.PM.YARN:
            return new yarnNodeModulesCollector_1.YarnNodeModulesCollector(rootDir);
        default:
            return new npmNodeModulesCollector_1.NpmNodeModulesCollector(rootDir);
    }
}
async function getNodeModules(rootDir) {
    const collector = await getCollectorByPackageManager(rootDir);
    return collector.getNodeModules();
}
//# sourceMappingURL=index.js.map