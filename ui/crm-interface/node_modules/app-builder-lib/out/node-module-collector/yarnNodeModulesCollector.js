"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.YarnNodeModulesCollector = void 0;
const npmNodeModulesCollector_1 = require("./npmNodeModulesCollector");
const packageManager_1 = require("./packageManager");
class YarnNodeModulesCollector extends npmNodeModulesCollector_1.NpmNodeModulesCollector {
    constructor(rootDir) {
        super(rootDir);
        // note: do not override instance-var `pmCommand`. We explicitly use npm for the json payload
        this.installOptions = { manager: packageManager_1.PM.YARN, lockfile: "yarn.lock" };
    }
}
exports.YarnNodeModulesCollector = YarnNodeModulesCollector;
//# sourceMappingURL=yarnNodeModulesCollector.js.map