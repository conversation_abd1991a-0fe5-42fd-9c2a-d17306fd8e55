{"version": 3, "file": "packageManager.js", "sourceRoot": "", "sources": ["../../src/node-module-collector/packageManager.ts"], "names": [], "mappings": ";;;AAkCA,4DAQC;AAED,8DAoBC;AAED,oDAkBC;AApFD,6BAA4B;AAC5B,yBAAwB;AACxB,+BAA8B;AAE9B,IAAY,EAKX;AALD,WAAY,EAAE;IACZ,iBAAW,CAAA;IACX,mBAAa,CAAA;IACb,mBAAa,CAAA;IACb,+BAAyB,CAAA;AAC3B,CAAC,EALW,EAAE,kBAAF,EAAE,QAKb;AAED,2BAA2B;AAC3B,MAAM,WAAW,GAA0C;IACzD,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,SAAS;IACnB,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,SAAS;IACpB,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,SAAS;IACpB,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS;CAC3B,CAAA;AAED,SAAS,cAAc,CAAC,EAAM;IAC5B,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAA;IAEnD,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QACjC,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,IAAI,CAAC;QACH,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAA;IAC5C,CAAC;IAAC,MAAM,CAAC;QACP,iEAAiE;QACjE,OAAO,QAAQ,CAAA;IACjB,CAAC;AACH,CAAC;AAED,SAAgB,wBAAwB,CAAC,EAAM;IAC7C,IAAI,WAAW,CAAC,EAAE,CAAC,KAAK,SAAS,EAAE,CAAC;QAClC,OAAO,WAAW,CAAC,EAAE,CAAE,CAAA;IACzB,CAAC;IAED,MAAM,QAAQ,GAAG,cAAc,CAAC,EAAE,CAAC,CAAA;IACnC,WAAW,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAA;IAC1B,OAAO,QAAQ,CAAA;AACjB,CAAC;AAED,SAAgB,yBAAyB;;IACvC,MAAM,EAAE,GAAG,MAAA,OAAO,CAAC,GAAG,CAAC,qBAAqB,mCAAI,EAAE,CAAA;IAClD,MAAM,QAAQ,GAAG,MAAA,MAAA,OAAO,CAAC,GAAG,CAAC,YAAY,0CAAE,WAAW,EAAE,mCAAI,EAAE,CAAA;IAE9D,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAA;IAC5C,MAAM,OAAO,GAAG,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,UAAU,CAAC,IAAI,CAAC,MAAI,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,UAAU,CAAC,IAAI,CAAC,CAAA,CAAA;IAE9E,IAAI,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;QAC9E,OAAO,EAAE,CAAC,IAAI,CAAA;IAChB,CAAC;IAED,IAAI,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;QAClF,OAAO,OAAO,IAAI,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAA;IAC5F,CAAC;IAED,IAAI,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QACnF,OAAO,EAAE,CAAC,GAAG,CAAA;IACf,CAAC;IAED,OAAO,EAAE,CAAC,GAAG,CAAA;AACf,CAAC;AAED,SAAgB,oBAAoB,CAAC,GAAW;IAC9C,MAAM,GAAG,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAA;IAEjE,MAAM,IAAI,GAAG,GAAG,CAAC,WAAW,CAAC,CAAA;IAC7B,MAAM,IAAI,GAAG,GAAG,CAAC,gBAAgB,CAAC,CAAA;IAClC,MAAM,GAAG,GAAG,GAAG,CAAC,mBAAmB,CAAC,CAAA;IAEpC,MAAM,QAAQ,GAAS,EAAE,CAAA;IACzB,IAAI,IAAI;QAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA;IAChC,IAAI,IAAI;QAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA;IAChC,IAAI,GAAG;QAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;IAE9B,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC1B,OAAO,QAAQ,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;IAC5E,CAAC;IAED,uCAAuC;IACvC,OAAO,yBAAyB,EAAE,CAAA;AACpC,CAAC", "sourcesContent": ["import * as path from \"path\"\nimport * as fs from \"fs\"\nimport * as which from \"which\"\n\nexport enum PM {\n  NPM = \"npm\",\n  YARN = \"yarn\",\n  PNPM = \"pnpm\",\n  YARN_BERRY = \"yarn-berry\",\n}\n\n// Cache for resolved paths\nconst pmPathCache: Record<PM, string | null | undefined> = {\n  [PM.NPM]: undefined,\n  [PM.YARN]: undefined,\n  [PM.PNPM]: undefined,\n  [PM.YARN_BERRY]: undefined,\n}\n\nfunction resolveCommand(pm: PM): string {\n  const fallback = pm === PM.YARN_BERRY ? \"yarn\" : pm\n\n  if (process.platform !== \"win32\") {\n    return fallback\n  }\n\n  try {\n    return path.basename(which.sync(fallback))\n  } catch {\n    // If `which` fails (not found), still return the fallback string\n    return fallback\n  }\n}\n\nexport function getPackageManagerCommand(pm: PM) {\n  if (pmPathCache[pm] !== undefined) {\n    return pmPathCache[pm]!\n  }\n\n  const resolved = resolveCommand(pm)\n  pmPathCache[pm] = resolved\n  return resolved\n}\n\nexport function detectPackageManagerByEnv(): PM {\n  const ua = process.env.npm_config_user_agent ?? \"\"\n  const execPath = process.env.npm_execpath?.toLowerCase() ?? \"\"\n\n  const yarnVersion = process.env.YARN_VERSION\n  const isBerry = yarnVersion?.startsWith(\"2.\") || yarnVersion?.startsWith(\"3.\")\n\n  if (ua.includes(\"pnpm\") || execPath.includes(\"pnpm\") || process.env.PNPM_HOME) {\n    return PM.PNPM\n  }\n\n  if (ua.includes(\"yarn\") || execPath.includes(\"yarn\") || process.env.YARN_REGISTRY) {\n    return isBerry || ua.includes(\"yarn/2\") || ua.includes(\"yarn/3\") ? PM.YARN_BERRY : PM.YARN\n  }\n\n  if (ua.includes(\"npm\") || execPath.includes(\"npm\") || process.env.npm_package_json) {\n    return PM.NPM\n  }\n\n  return PM.NPM\n}\n\nexport function detectPackageManager(cwd: string): PM {\n  const has = (file: string) => fs.existsSync(path.join(cwd, file))\n\n  const yarn = has(\"yarn.lock\")\n  const pnpm = has(\"pnpm-lock.yaml\")\n  const npm = has(\"package-lock.json\")\n\n  const detected: PM[] = []\n  if (yarn) detected.push(PM.YARN)\n  if (pnpm) detected.push(PM.PNPM)\n  if (npm) detected.push(PM.NPM)\n\n  if (detected.length === 1) {\n    return detected[0] === PM.YARN ? detectPackageManagerByEnv() : detected[0]\n  }\n\n  // fallback: multiple lockfiles or none\n  return detectPackageManagerByEnv()\n}\n"]}