"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AsarPackager = void 0;
const asar_1 = require("@electron/asar");
const builder_util_1 = require("builder-util");
const fs = require("fs-extra");
const fs_extra_1 = require("fs-extra");
const path = require("path");
const appFileCopier_1 = require("../util/appFileCopier");
const unpackDetector_1 = require("./unpackDetector");
const stream_1 = require("stream");
/** @internal */
class AsarPackager {
    constructor(packager, config) {
        this.packager = packager;
        this.config = config;
        this.outFile = path.join(config.resourcePath, `app.asar`);
    }
    async pack(fileSets) {
        const orderedFileSets = [
            // Write dependencies first to minimize offset changes to asar header
            ...fileSets.slice(1),
            // Finish with the app files that change most often
            fileSets[0],
        ].map(set => this.orderFileSet(set));
        const streams = await this.processFileSets(orderedFileSets);
        await this.executeElectronAsar(streams);
    }
    async executeElectronAsar(streams) {
        // override logger temporarily to clean up console (electron/asar does some internal logging that blogs up the default electron-builder logs)
        const consoleLogger = console.log;
        console.log = (...args) => {
            if (args[0] === "Ordering file has 100% coverage.") {
                return; // no need to log, this means our ordering logic is working correctly
            }
            builder_util_1.log.info({ args }, "logging @electron/asar");
        };
        await (0, asar_1.createPackageFromStreams)(this.outFile, streams);
        console.log = consoleLogger;
    }
    async processFileSets(fileSets) {
        var _a;
        const unpackedPaths = new Set();
        if (this.config.options.smartUnpack !== false) {
            for (const fileSet of fileSets) {
                (0, unpackDetector_1.detectUnpackedDirs)(fileSet, unpackedPaths);
            }
        }
        const results = [];
        for (const fileSet of fileSets) {
            // Don't use Promise.all, we need to retain order of execution/iteration through the already-ordered fileset
            for (const [index, file] of fileSet.files.entries()) {
                const transformedData = (_a = fileSet.transformedFiles) === null || _a === void 0 ? void 0 : _a.get(index);
                const stat = fileSet.metadata.get(file);
                const destination = path.relative(this.config.defaultDestination, (0, appFileCopier_1.getDestinationPath)(file, fileSet));
                const paths = Array.from(unpackedPaths).map(p => path.normalize(p));
                const isChildDirectory = (fileOrDirPath) => paths.includes(path.normalize(fileOrDirPath)) || paths.some(unpackedPath => path.normalize(fileOrDirPath).startsWith(unpackedPath + path.sep));
                const isUnpacked = (dir) => {
                    var _a, _b, _c;
                    const isChild = isChildDirectory(dir);
                    const isFileUnpacked = (_c = (_b = (_a = this.config).unpackPattern) === null || _b === void 0 ? void 0 : _b.call(_a, file, stat)) !== null && _c !== void 0 ? _c : false;
                    return isChild || isFileUnpacked;
                };
                this.processParentDirectories(isUnpacked, destination, results);
                const result = await this.processFileOrSymlink({
                    file,
                    destination,
                    fileSet,
                    transformedData,
                    stat,
                    isUnpacked,
                });
                if (result != null) {
                    results.push(result);
                }
            }
        }
        return results;
    }
    processParentDirectories(isUnpacked, destination, results) {
        // process parent directories
        let superDir = path.dirname(path.normalize(destination));
        while (superDir !== ".") {
            const dir = {
                type: "directory",
                path: superDir,
                unpacked: isUnpacked(superDir),
            };
            // add to results if not already present
            if (!results.some(r => r.path === dir.path)) {
                results.push(dir);
            }
            superDir = path.dirname(superDir);
        }
    }
    async processFileOrSymlink(options) {
        const { isUnpacked, transformedData, file, destination, stat, fileSet } = options;
        const unpacked = isUnpacked(destination);
        if (!stat.isFile() && !stat.isSymbolicLink()) {
            return { path: destination, unpacked, type: "directory" };
        }
        // write any data if provided, skip symlink check
        if (transformedData != null) {
            const streamGenerator = () => {
                return new stream_1.Readable({
                    read() {
                        this.push(transformedData);
                        this.push(null);
                    },
                });
            };
            const size = Buffer.byteLength(transformedData);
            return { path: destination, streamGenerator, unpacked, type: "file", stat: { mode: stat.mode, size } };
        }
        const realPathFile = await fs.realpath(file);
        const realPathRelative = path.relative(fileSet.src, realPathFile);
        const isOutsidePackage = realPathRelative.startsWith("..");
        if (isOutsidePackage) {
            builder_util_1.log.error({ source: builder_util_1.log.filePath(file), realPathFile: builder_util_1.log.filePath(realPathFile) }, `unable to copy, file is symlinked outside the package`);
            throw new Error(`Cannot copy file (${path.basename(file)}) symlinked to file (${path.basename(realPathFile)}) outside the package as that violates asar security integrity`);
        }
        const config = {
            path: destination,
            streamGenerator: () => fs.createReadStream(file),
            unpacked,
            stat,
        };
        // not a symlink, stream directly
        if (file === realPathFile) {
            return {
                ...config,
                type: "file",
            };
        }
        // okay, it must be a symlink. evaluate link to be relative to source file in asar
        let link = await (0, fs_extra_1.readlink)(file);
        if (path.isAbsolute(link)) {
            link = path.relative(path.dirname(file), link);
        }
        return {
            ...config,
            type: "link",
            symlink: link,
        };
    }
    orderFileSet(fileSet) {
        const sortedFileEntries = Array.from(fileSet.files.entries());
        sortedFileEntries.sort(([, a], [, b]) => {
            if (a === b) {
                return 0;
            }
            // Place addons last because their signature changes per build
            const isAAddon = a.endsWith(".node");
            const isBAddon = b.endsWith(".node");
            if (isAAddon && !isBAddon) {
                return 1;
            }
            if (isBAddon && !isAAddon) {
                return -1;
            }
            // Otherwise order by name
            return a < b ? -1 : 1;
        });
        let transformedFiles;
        if (fileSet.transformedFiles) {
            transformedFiles = new Map();
            const indexMap = new Map();
            for (const [newIndex, [oldIndex]] of sortedFileEntries.entries()) {
                indexMap.set(oldIndex, newIndex);
            }
            for (const [oldIndex, value] of fileSet.transformedFiles) {
                const newIndex = indexMap.get(oldIndex);
                if (newIndex === undefined) {
                    const file = fileSet.files[oldIndex];
                    throw new Error(`Internal error: ${file} was lost while ordering asar`);
                }
                transformedFiles.set(newIndex, value);
            }
        }
        const { src, destination, metadata } = fileSet;
        return {
            src,
            destination,
            metadata,
            files: sortedFileEntries.map(([, file]) => file),
            transformedFiles,
        };
    }
}
exports.AsarPackager = AsarPackager;
//# sourceMappingURL=asarUtil.js.map