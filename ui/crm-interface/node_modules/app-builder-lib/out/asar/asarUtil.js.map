{"version": 3, "file": "asarUtil.js", "sourceRoot": "", "sources": ["../../src/asar/asarUtil.ts"], "names": [], "mappings": ";;;AAAA,yCAAwF;AACxF,+CAAkC;AAElC,+BAA8B;AAC9B,uCAAmC;AACnC,6BAA4B;AAG5B,yDAA2E;AAC3E,qDAAqD;AACrD,mCAAiC;AAEjC,gBAAgB;AAChB,MAAa,YAAY;IAGvB,YACW,QAA+B,EACvB,MAKhB;QANQ,aAAQ,GAAR,QAAQ,CAAuB;QACvB,WAAM,GAAN,MAAM,CAKtB;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,UAAU,CAAC,CAAA;IAC3D,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,QAAgC;QACzC,MAAM,eAAe,GAAG;YACtB,qEAAqE;YACrE,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;YAEpB,mDAAmD;YACnD,QAAQ,CAAC,CAAC,CAAC;SACZ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAA;QAEpC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAA;QAC3D,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAA;IACzC,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,OAAyB;QACzD,6IAA6I;QAC7I,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAA;QACjC,OAAO,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE,EAAE;YACxB,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,kCAAkC,EAAE,CAAC;gBACnD,OAAM,CAAC,qEAAqE;YAC9E,CAAC;YACD,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,wBAAwB,CAAC,CAAA;QAC9C,CAAC,CAAA;QACD,MAAM,IAAA,+BAAwB,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QACrD,OAAO,CAAC,GAAG,GAAG,aAAa,CAAA;IAC7B,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,QAA2B;;QACvD,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAA;QACvC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;YAC9C,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC/B,IAAA,mCAAkB,EAAC,OAAO,EAAE,aAAa,CAAC,CAAA;YAC5C,CAAC;QACH,CAAC;QAED,MAAM,OAAO,GAAqB,EAAE,CAAA;QACpC,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,4GAA4G;YAC5G,KAAK,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;gBACpD,MAAM,eAAe,GAAG,MAAA,OAAO,CAAC,gBAAgB,0CAAE,GAAG,CAAC,KAAK,CAAC,CAAA;gBAC5D,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAE,CAAA;gBACxC,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,IAAA,kCAAkB,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAA;gBAEpG,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA;gBAEnE,MAAM,gBAAgB,GAAG,CAAC,aAAqB,EAAE,EAAE,CACjD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;gBAChJ,MAAM,UAAU,GAAG,CAAC,GAAW,EAAE,EAAE;;oBACjC,MAAM,OAAO,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAA;oBACrC,MAAM,cAAc,GAAG,MAAA,MAAA,MAAA,IAAI,CAAC,MAAM,EAAC,aAAa,mDAAG,IAAI,EAAE,IAAI,CAAC,mCAAI,KAAK,CAAA;oBACvE,OAAO,OAAO,IAAI,cAAc,CAAA;gBAClC,CAAC,CAAA;gBAED,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC,CAAA;gBAE/D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC;oBAC7C,IAAI;oBACJ,WAAW;oBACX,OAAO;oBACP,eAAe;oBACf,IAAI;oBACJ,UAAU;iBACX,CAAC,CAAA;gBACF,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;oBACnB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;gBACtB,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,OAAO,CAAA;IAChB,CAAC;IAEO,wBAAwB,CAAC,UAAqC,EAAE,WAAmB,EAAE,OAAyB;QACpH,6BAA6B;QAC7B,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAA;QACxD,OAAO,QAAQ,KAAK,GAAG,EAAE,CAAC;YACxB,MAAM,GAAG,GAAkB;gBACzB,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,UAAU,CAAC,QAAQ,CAAC;aAC/B,CAAA;YACD,wCAAwC;YACxC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC5C,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACnB,CAAC;YAED,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QACnC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,OAOlC;QACC,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAA;QACjF,MAAM,QAAQ,GAAG,UAAU,CAAC,WAAW,CAAC,CAAA;QAExC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;YAC7C,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,CAAA;QAC3D,CAAC;QAED,iDAAiD;QACjD,IAAI,eAAe,IAAI,IAAI,EAAE,CAAC;YAC5B,MAAM,eAAe,GAAG,GAAG,EAAE;gBAC3B,OAAO,IAAI,iBAAQ,CAAC;oBAClB,IAAI;wBACF,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;wBAC1B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;oBACjB,CAAC;iBACF,CAAC,CAAA;YACJ,CAAC,CAAA;YACD,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,CAAA;YAC/C,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAA;QACxG,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QAC5C,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,YAAY,CAAC,CAAA;QACjE,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;QAC1D,IAAI,gBAAgB,EAAE,CAAC;YACrB,kBAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,kBAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,YAAY,EAAE,kBAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,EAAE,uDAAuD,CAAC,CAAA;YAC5I,MAAM,IAAI,KAAK,CAAC,qBAAqB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,gEAAgE,CAAC,CAAA;QAC9K,CAAC;QAED,MAAM,MAAM,GAAG;YACb,IAAI,EAAE,WAAW;YACjB,eAAe,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC;YAChD,QAAQ;YACR,IAAI;SACL,CAAA;QAED,iCAAiC;QACjC,IAAI,IAAI,KAAK,YAAY,EAAE,CAAC;YAC1B,OAAO;gBACL,GAAG,MAAM;gBACT,IAAI,EAAE,MAAM;aACb,CAAA;QACH,CAAC;QAED,kFAAkF;QAClF,IAAI,IAAI,GAAG,MAAM,IAAA,mBAAQ,EAAC,IAAI,CAAC,CAAA;QAC/B,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAA;QAChD,CAAC;QACD,OAAO;YACL,GAAG,MAAM;YACT,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,IAAI;SACd,CAAA;IACH,CAAC;IAEO,YAAY,CAAC,OAAwB;QAC3C,MAAM,iBAAiB,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;QAE7D,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;YACtC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACZ,OAAO,CAAC,CAAA;YACV,CAAC;YAED,8DAA8D;YAC9D,MAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;YACpC,MAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;YACpC,IAAI,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC1B,OAAO,CAAC,CAAA;YACV,CAAC;YACD,IAAI,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC1B,OAAO,CAAC,CAAC,CAAA;YACX,CAAC;YAED,0BAA0B;YAC1B,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACvB,CAAC,CAAC,CAAA;QAEF,IAAI,gBAA0D,CAAA;QAC9D,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC7B,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAA;YAE5B,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAkB,CAAA;YAC1C,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,IAAI,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC;gBACjE,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;YAClC,CAAC;YAED,KAAK,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;gBACzD,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;gBACvC,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;oBAC3B,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;oBACpC,MAAM,IAAI,KAAK,CAAC,mBAAmB,IAAI,+BAA+B,CAAC,CAAA;gBACzE,CAAC;gBAED,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;YACvC,CAAC;QACH,CAAC;QAED,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAA;QAE9C,OAAO;YACL,GAAG;YACH,WAAW;YACX,QAAQ;YACR,KAAK,EAAE,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;YAChD,gBAAgB;SACjB,CAAA;IACH,CAAC;CACF;AA3ND,oCA2NC", "sourcesContent": ["import { createPackageFromStreams, AsarStreamType, AsarDirectory } from \"@electron/asar\"\nimport { log } from \"builder-util\"\nimport { Filter } from \"builder-util/out/fs\"\nimport * as fs from \"fs-extra\"\nimport { readlink } from \"fs-extra\"\nimport * as path from \"path\"\nimport { AsarOptions } from \"../options/PlatformSpecificBuildOptions\"\nimport { PlatformPackager } from \"../platformPackager\"\nimport { ResolvedFileSet, getDestinationPath } from \"../util/appFileCopier\"\nimport { detectUnpackedDirs } from \"./unpackDetector\"\nimport { Readable } from \"stream\"\n\n/** @internal */\nexport class AsarPackager {\n  private readonly outFile: string\n\n  constructor(\n    readonly packager: PlatformPackager<any>,\n    private readonly config: {\n      defaultDestination: string\n      resourcePath: string\n      options: AsarOptions\n      unpackPattern: Filter | undefined\n    }\n  ) {\n    this.outFile = path.join(config.resourcePath, `app.asar`)\n  }\n\n  async pack(fileSets: Array<ResolvedFileSet>) {\n    const orderedFileSets = [\n      // Write dependencies first to minimize offset changes to asar header\n      ...fileSets.slice(1),\n\n      // Finish with the app files that change most often\n      fileSets[0],\n    ].map(set => this.orderFileSet(set))\n\n    const streams = await this.processFileSets(orderedFileSets)\n    await this.executeElectronAsar(streams)\n  }\n\n  private async executeElectronAsar(streams: AsarStreamType[]) {\n    // override logger temporarily to clean up console (electron/asar does some internal logging that blogs up the default electron-builder logs)\n    const consoleLogger = console.log\n    console.log = (...args) => {\n      if (args[0] === \"Ordering file has 100% coverage.\") {\n        return // no need to log, this means our ordering logic is working correctly\n      }\n      log.info({ args }, \"logging @electron/asar\")\n    }\n    await createPackageFromStreams(this.outFile, streams)\n    console.log = consoleLogger\n  }\n\n  private async processFileSets(fileSets: ResolvedFileSet[]): Promise<AsarStreamType[]> {\n    const unpackedPaths = new Set<string>()\n    if (this.config.options.smartUnpack !== false) {\n      for (const fileSet of fileSets) {\n        detectUnpackedDirs(fileSet, unpackedPaths)\n      }\n    }\n\n    const results: AsarStreamType[] = []\n    for (const fileSet of fileSets) {\n      // Don't use Promise.all, we need to retain order of execution/iteration through the already-ordered fileset\n      for (const [index, file] of fileSet.files.entries()) {\n        const transformedData = fileSet.transformedFiles?.get(index)\n        const stat = fileSet.metadata.get(file)!\n        const destination = path.relative(this.config.defaultDestination, getDestinationPath(file, fileSet))\n\n        const paths = Array.from(unpackedPaths).map(p => path.normalize(p))\n\n        const isChildDirectory = (fileOrDirPath: string) =>\n          paths.includes(path.normalize(fileOrDirPath)) || paths.some(unpackedPath => path.normalize(fileOrDirPath).startsWith(unpackedPath + path.sep))\n        const isUnpacked = (dir: string) => {\n          const isChild = isChildDirectory(dir)\n          const isFileUnpacked = this.config.unpackPattern?.(file, stat) ?? false\n          return isChild || isFileUnpacked\n        }\n\n        this.processParentDirectories(isUnpacked, destination, results)\n\n        const result = await this.processFileOrSymlink({\n          file,\n          destination,\n          fileSet,\n          transformedData,\n          stat,\n          isUnpacked,\n        })\n        if (result != null) {\n          results.push(result)\n        }\n      }\n    }\n    return results\n  }\n\n  private processParentDirectories(isUnpacked: (path: string) => boolean, destination: string, results: AsarStreamType[]) {\n    // process parent directories\n    let superDir = path.dirname(path.normalize(destination))\n    while (superDir !== \".\") {\n      const dir: AsarDirectory = {\n        type: \"directory\",\n        path: superDir,\n        unpacked: isUnpacked(superDir),\n      }\n      // add to results if not already present\n      if (!results.some(r => r.path === dir.path)) {\n        results.push(dir)\n      }\n\n      superDir = path.dirname(superDir)\n    }\n  }\n\n  private async processFileOrSymlink(options: {\n    file: string\n    destination: string\n    stat: fs.Stats\n    fileSet: ResolvedFileSet\n    transformedData: string | Buffer | undefined\n    isUnpacked: (path: string) => boolean\n  }): Promise<AsarStreamType> {\n    const { isUnpacked, transformedData, file, destination, stat, fileSet } = options\n    const unpacked = isUnpacked(destination)\n\n    if (!stat.isFile() && !stat.isSymbolicLink()) {\n      return { path: destination, unpacked, type: \"directory\" }\n    }\n\n    // write any data if provided, skip symlink check\n    if (transformedData != null) {\n      const streamGenerator = () => {\n        return new Readable({\n          read() {\n            this.push(transformedData)\n            this.push(null)\n          },\n        })\n      }\n      const size = Buffer.byteLength(transformedData)\n      return { path: destination, streamGenerator, unpacked, type: \"file\", stat: { mode: stat.mode, size } }\n    }\n\n    const realPathFile = await fs.realpath(file)\n    const realPathRelative = path.relative(fileSet.src, realPathFile)\n    const isOutsidePackage = realPathRelative.startsWith(\"..\")\n    if (isOutsidePackage) {\n      log.error({ source: log.filePath(file), realPathFile: log.filePath(realPathFile) }, `unable to copy, file is symlinked outside the package`)\n      throw new Error(`Cannot copy file (${path.basename(file)}) symlinked to file (${path.basename(realPathFile)}) outside the package as that violates asar security integrity`)\n    }\n\n    const config = {\n      path: destination,\n      streamGenerator: () => fs.createReadStream(file),\n      unpacked,\n      stat,\n    }\n\n    // not a symlink, stream directly\n    if (file === realPathFile) {\n      return {\n        ...config,\n        type: \"file\",\n      }\n    }\n\n    // okay, it must be a symlink. evaluate link to be relative to source file in asar\n    let link = await readlink(file)\n    if (path.isAbsolute(link)) {\n      link = path.relative(path.dirname(file), link)\n    }\n    return {\n      ...config,\n      type: \"link\",\n      symlink: link,\n    }\n  }\n\n  private orderFileSet(fileSet: ResolvedFileSet): ResolvedFileSet {\n    const sortedFileEntries = Array.from(fileSet.files.entries())\n\n    sortedFileEntries.sort(([, a], [, b]) => {\n      if (a === b) {\n        return 0\n      }\n\n      // Place addons last because their signature changes per build\n      const isAAddon = a.endsWith(\".node\")\n      const isBAddon = b.endsWith(\".node\")\n      if (isAAddon && !isBAddon) {\n        return 1\n      }\n      if (isBAddon && !isAAddon) {\n        return -1\n      }\n\n      // Otherwise order by name\n      return a < b ? -1 : 1\n    })\n\n    let transformedFiles: Map<number, string | Buffer> | undefined\n    if (fileSet.transformedFiles) {\n      transformedFiles = new Map()\n\n      const indexMap = new Map<number, number>()\n      for (const [newIndex, [oldIndex]] of sortedFileEntries.entries()) {\n        indexMap.set(oldIndex, newIndex)\n      }\n\n      for (const [oldIndex, value] of fileSet.transformedFiles) {\n        const newIndex = indexMap.get(oldIndex)\n        if (newIndex === undefined) {\n          const file = fileSet.files[oldIndex]\n          throw new Error(`Internal error: ${file} was lost while ordering asar`)\n        }\n\n        transformedFiles.set(newIndex, value)\n      }\n    }\n\n    const { src, destination, metadata } = fileSet\n\n    return {\n      src,\n      destination,\n      metadata,\n      files: sortedFileEntries.map(([, file]) => file),\n      transformedFiles,\n    }\n  }\n}\n"]}