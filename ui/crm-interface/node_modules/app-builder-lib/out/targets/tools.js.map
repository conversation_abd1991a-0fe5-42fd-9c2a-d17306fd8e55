{"version": 3, "file": "tools.js", "sourceRoot": "", "sources": ["../../src/targets/tools.ts"], "names": [], "mappings": ";;AAGA,8CAEC;AAED,gCA6BC;AApCD,6BAA4B;AAC5B,gDAA8C;AAE9C,SAAgB,iBAAiB;IAC/B,OAAO,IAAA,2BAAa,EAAC,yBAAyB,EAAE,4BAA4B,EAAE,0FAA0F,CAAC,CAAA;AAC3K,CAAC;AAEM,KAAK,UAAU,UAAU;IAC9B,IAAI,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,IAAI,EAAE,CAAC;QACxC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;IAClD,CAAC;IACD,MAAM,IAAI,GAAG,KAAK,CAAA;IAClB,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,KAAK,MAAM,EAAE,CAAC;QAC1E,OAAO,IAAI,CAAA;IACb,CAAC;IACD,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QACjC,IAAI,QAAgB,CAAA;QACpB,IAAI,UAAkB,CAAA;QACtB,IAAI,OAAO,CAAC,IAAI,IAAI,KAAK,EAAE,CAAC;YAC1B,QAAQ,GAAG,0FAA0F,CAAA;YACrG,UAAU,GAAG,SAAS,CAAA;QACxB,CAAC;aAAM,CAAC;YACN,QAAQ,GAAG,0FAA0F,CAAA;YACrG,UAAU,GAAG,MAAM,CAAA;QACrB,CAAC;QACD,MAAM,QAAQ,GAAG,uBAAuB,GAAG,UAAU,CAAA;QACrD,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,IAAA,2BAAa,EAAC,QAAQ,EAAE,QAAQ,GAAG,KAAK,EAAE,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAA;IACnF,CAAC;IACD,OAAO,IAAI,CAAC,IAAI,CACd,MAAM,IAAA,2BAAa,EACjB,8BAA8B,EAC9B,iCAAiC,EACjC,0FAA0F,CAC3F,EACD,IAAI,CACL,CAAA;AACH,CAAC", "sourcesContent": ["import * as path from \"path\"\nimport { getBinFromUrl } from \"../binDownload\"\n\nexport function getLinuxToolsPath() {\n  return getBinFromUrl(\"linux-tools-mac-10.12.3\", \"linux-tools-mac-10.12.3.7z\", \"SQ8fqIRVXuQVWnVgaMTDWyf2TLAJjJYw3tRSqQJECmgF6qdM7Kogfa6KD49RbGzzMYIFca9Uw3MdsxzOPRWcYw==\")\n}\n\nexport async function getFpmPath() {\n  if (process.env.CUSTOM_FPM_PATH != null) {\n    return path.resolve(process.env.CUSTOM_FPM_PATH)\n  }\n  const exec = \"fpm\"\n  if (process.platform === \"win32\" || process.env.USE_SYSTEM_FPM === \"true\") {\n    return exec\n  }\n  if (process.platform === \"linux\") {\n    let checksum: string\n    let archSuffix: string\n    if (process.arch == \"x64\") {\n      checksum = \"fcKdXPJSso3xFs5JyIJHG1TfHIRTGDP0xhSBGZl7pPZlz4/TJ4rD/q3wtO/uaBBYeX0qFFQAFjgu1uJ6HLHghA==\"\n      archSuffix = \"-x86_64\"\n    } else {\n      checksum = \"OnzvBdsHE5djcXcAT87rwbnZwS789ZAd2ehuIO42JWtBAHNzXKxV4o/24XFX5No4DJWGO2YSGQttW+zn7d/4rQ==\"\n      archSuffix = \"-x86\"\n    }\n    const fileName = \"fpm-1.9.3-2.3.1-linux\" + archSuffix\n    return path.join(await getBinFromUrl(fileName, fileName + \".7z\", checksum), exec)\n  }\n  return path.join(\n    await getBinFromUrl(\n      \"fpm-1.9.3-20150715-2.2.2-mac\",\n      \"fpm-1.9.3-20150715-2.2.2-mac.7z\",\n      \"oXfq+0H2SbdrbMik07mYloAZ8uHrmf6IJk+Q3P1kwywuZnKTXSaaeZUJNlWoVpRDWNu537YxxpBQWuTcF+6xfw==\"\n    ),\n    exec\n  )\n}\n"]}