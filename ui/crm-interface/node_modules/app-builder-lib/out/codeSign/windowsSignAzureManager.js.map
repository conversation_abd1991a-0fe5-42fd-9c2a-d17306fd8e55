{"version": 3, "file": "windowsSignAzureManager.js", "sourceRoot": "", "sources": ["../../src/codeSign/windowsSignAzureManager.ts"], "names": [], "mappings": ";;;AAAA,+CAAsE;AACtE,+DAA+C;AAC/C,uCAA+B;AAO/B,MAAa,uBAAuB;IAgBlC,YAA6B,QAAqB;QAArB,aAAQ,GAAR,QAAQ,CAAa;QAbzC,0BAAqB,GAAG,IAAI,eAAI,CAAuB,GAAG,EAAE;;YACnE,MAAM,aAAa,GAAG,MAAA,IAAI,CAAC,4BAA4B,CAAC,gBAAgB,0CAAE,aAAa,CAAA;YACvF,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;gBAC3B,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YAC9B,CAAC;iBAAM,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;gBACjC,OAAO,OAAO,CAAC,OAAO,CAAC,IAAA,sBAAO,EAAC,aAAa,CAAC,CAAC,CAAA;YAChD,CAAC;YAED,6FAA6F;YAC7F,uBAAuB;YACvB,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QAC9B,CAAC,CAAC,CAAA;QA6EO,YAAO,GAAG,IAAI,+BAAQ,CAC7B,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,4BAA4B,EAChD,SAAS,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CACnC,CAAA;QA7EC,IAAI,CAAC,4BAA4B,GAAG,QAAQ,CAAC,4BAA4B,CAAA;IAC3E,CAAC;IAED,KAAK,CAAC,UAAU;QACd,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAA;QACvC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,iBAAiB,CAAC,KAAK,CAAA;QAE3C,kBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,oEAAoE,CAAC,CAAA;QACpF,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,UAAU,EAAE,yFAAyF,CAAC,CAAC,CAAA;QAC7J,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,mEAAmE;YACnE,gKAAgK;YAChK,kBAAG,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE,kHAAkH,CAAC,CAAA;QAC1K,CAAC;QACD,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,UAAU,EAAE,2GAA2G,CAAC,CAAC,CAAA;QAE7K,yDAAyD;QACzD,0HAA0H;QAC1H,kBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,6DAA6D,CAAC,CAAA;QAC7E,IAAI,CAAC,qBAAqB,EAAE,CAAA;QAC5B,IAAI,CAAC,CAAC,IAAI,CAAC,wBAAwB,EAAE,IAAI,IAAI,CAAC,6BAA6B,EAAE,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC,EAAE,CAAC;YACnH,MAAM,IAAI,wCAAyB,CACjC,0PAA0P,CAC3P,CAAA;QACH,CAAC;IACH,CAAC;IAED,qBAAqB;QACnB,CAAC;QAAA,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACtD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxB,MAAM,IAAI,wCAAyB,CACjC,wCAAwC,KAAK,+IAA+I,CAC7L,CAAA;YACH,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,wBAAwB;QACtB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,CAAC;YACrC,kBAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,qBAAqB,EAAE,EAAE,0DAA0D,CAAC,CAAA;YACxG,OAAO,KAAK,CAAA;QACd,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,6BAA6B;QAC3B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,CAAC;YAC/C,kBAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,+BAA+B,EAAE,EAAE,gFAAgF,CAAC,CAAA;YACxI,OAAO,KAAK,CAAA;QACd,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,CAAC;YACnD,kBAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,mCAAmC,EAAE,EAAE,iEAAiE,CAAC,CAAA;QAC/H,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,CAAC;YACrD,kBAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,qCAAqC,EAAE,EAAE,wCAAwC,CAAC,CAAA;QACxG,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,yBAAyB;QACvB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC;YAChC,kBAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,gBAAgB,EAAE,EAAE,4DAA4D,CAAC,CAAA;YACrG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC;gBAChC,kBAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,gBAAgB,EAAE,EAAE,4DAA4D,CAAC,CAAA;YACvG,CAAC;YACD,OAAO,KAAK,CAAA;QACd,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,oBAAoB;QAClB,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,4BAA4B,CAAC,gBAAiB,CAAC,aAAa,CAAC,CAAA;IACpG,CAAC;IAKD,mFAAmF;IACnF,KAAK,CAAC,QAAQ,CAAC,OAA2B;QACxC,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAA;QACvC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,iBAAiB,CAAC,KAAK,CAAA;QAE3C,MAAM,EACJ,aAAa,EAAE,UAAU,EAAE,kCAAkC;QAC7D,QAAQ,EACR,sBAAsB,EACtB,sBAAsB,EACtB,UAAU,EACV,gBAAgB,EAChB,eAAe,EACf,GAAG,gBAAgB,EACpB,GAAqC,OAAO,CAAC,OAAO,CAAC,gBAAiB,CAAA;QACvE,MAAM,MAAM,GAAG;YACb,GAAG,gBAAgB;YACnB,QAAQ,EAAE,QAAQ;YAClB,sBAAsB,EAAE,sBAAsB;YAC9C,sBAAsB,EAAE,sBAAsB;YAC9C,gBAAgB,EAAE,gBAAgB,IAAI,oCAAoC;YAC1E,eAAe,EAAE,eAAe,IAAI,QAAQ;YAC5C,UAAU,EAAE,UAAU,IAAI,QAAQ;YAClC,KAAK,EAAE,OAAO,CAAC,IAAI;SACpB,CAAA;QACD,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;aACxC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,IAAI,IAAI,CAAC;aACrC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE;YAC9B,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;YACtD,OAAO,CAAC,GAAG,GAAG,EAAE,IAAI,KAAK,EAAE,EAAE,IAAI,YAAY,GAAG,CAAC,CAAA;QACnD,CAAC,EAAE,EAAc,CAAC;aACjB,IAAI,CAAC,GAAG,CAAC,CAAA;QACZ,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,UAAU,EAAE,yBAAyB,YAAY,EAAE,CAAC,CAAC,CAAA;QAEzG,OAAO,IAAI,CAAA;IACb,CAAC;CACF;AAnID,0DAmIC", "sourcesContent": ["import { asArray, InvalidConfigurationError, log } from \"builder-util\"\nimport { MemoLazy } from \"builder-util-runtime\"\nimport { Lazy } from \"lazy-val\"\nimport { WindowsAzureSigningConfiguration, WindowsConfiguration } from \"../options/winOptions\"\nimport { WinPackager } from \"../winPackager\"\nimport { SignManager } from \"./signManager\"\nimport { WindowsSignOptions } from \"./windowsCodeSign\"\nimport { CertificateFromStoreInfo, FileCodeSigningInfo } from \"./windowsSignToolManager\"\n\nexport class WindowsSignAzureManager implements SignManager {\n  private readonly platformSpecificBuildOptions: WindowsConfiguration\n\n  readonly computedPublisherName = new Lazy<Array<string> | null>(() => {\n    const publisherName = this.platformSpecificBuildOptions.azureSignOptions?.publisherName\n    if (publisherName === null) {\n      return Promise.resolve(null)\n    } else if (publisherName != null) {\n      return Promise.resolve(asArray(publisherName))\n    }\n\n    // TODO: Is there another way to automatically pull Publisher Name from AzureTrusted service?\n    // For now return null.\n    return Promise.resolve(null)\n  })\n\n  constructor(private readonly packager: WinPackager) {\n    this.platformSpecificBuildOptions = packager.platformSpecificBuildOptions\n  }\n\n  async initialize() {\n    const vm = await this.packager.vm.value\n    const ps = await vm.powershellCommand.value\n\n    log.info(null, \"installing required module (TrustedSigning) with scope CurrentUser\")\n    try {\n      await vm.exec(ps, [\"-NoProfile\", \"-NonInteractive\", \"-Command\", \"Install-PackageProvider -Name NuGet -MinimumVersion 2.8.5.201 -Force -Scope CurrentUser\"])\n    } catch (error: any) {\n      // Might not be needed, seems GH runners already have NuGet set up.\n      // Logging to debug just in case users run into this. If NuGet isn't present, Install-Module -Name TrustedSigning will fail, so we'll get the logs at that point\n      log.debug({ message: error.message || error.stack }, \"unable to install PackageProvider Nuget. Might be a false alarm though as some systems already have it installed\")\n    }\n    await vm.exec(ps, [\"-NoProfile\", \"-NonInteractive\", \"-Command\", \"Install-Module -Name TrustedSigning -MinimumVersion 0.5.0 -Force -Repository PSGallery -Scope CurrentUser\"])\n\n    // Preemptively check env vars once during initialization\n    // Options: https://learn.microsoft.com/en-us/dotnet/api/azure.identity.environmentcredential?view=azure-dotnet#definition\n    log.info(null, \"verifying env vars for authenticating to Microsoft Entra ID\")\n    this.verifyRequiredEnvVars()\n    if (!(this.verifyPrincipleSecretEnv() || this.verifyPrincipleCertificateEnv() || this.verifyUsernamePasswordEnv())) {\n      throw new InvalidConfigurationError(\n        `Unable to find valid azure env configuration for signing. Missing field(s) can be debugged via \"DEBUG=electron-builder\". Please refer to: https://learn.microsoft.com/en-us/dotnet/api/azure.identity.environmentcredential?view=azure-dotnet#definition`\n      )\n    }\n  }\n\n  verifyRequiredEnvVars() {\n    ;[\"AZURE_TENANT_ID\", \"AZURE_CLIENT_ID\"].forEach(field => {\n      if (!process.env[field]) {\n        throw new InvalidConfigurationError(\n          `Unable to find valid azure env field ${field} for signing. Please refer to: https://learn.microsoft.com/en-us/dotnet/api/azure.identity.environmentcredential?view=azure-dotnet#definition`\n        )\n      }\n    })\n  }\n\n  verifyPrincipleSecretEnv() {\n    if (!process.env.AZURE_CLIENT_SECRET) {\n      log.debug({ envVar: \"AZURE_CLIENT_SECRET\" }, \"no secret found for authenticating to Microsoft Entra ID\")\n      return false\n    }\n    return true\n  }\n\n  verifyPrincipleCertificateEnv() {\n    if (!process.env.AZURE_CLIENT_CERTIFICATE_PATH) {\n      log.debug({ envVar: \"AZURE_CLIENT_CERTIFICATE_PATH\" }, \"no path found for signing certificate for authenticating to Microsoft Entra ID\")\n      return false\n    }\n    if (!process.env.AZURE_CLIENT_CERTIFICATE_PASSWORD) {\n      log.debug({ envVar: \"AZURE_CLIENT_CERTIFICATE_PASSWORD\" }, \"(optional) certificate password not found, assuming no password\")\n    }\n    if (!process.env.AZURE_CLIENT_SEND_CERTIFICATE_CHAIN) {\n      log.debug({ envVar: \"AZURE_CLIENT_SEND_CERTIFICATE_CHAIN\" }, \"(optional) certificate chain not found\")\n    }\n    return true\n  }\n\n  verifyUsernamePasswordEnv() {\n    if (!process.env.AZURE_USERNAME) {\n      log.debug({ envVar: \"AZURE_USERNAME\" }, \"no username found for authenticating to Microsoft Entra ID\")\n      if (!process.env.AZURE_PASSWORD) {\n        log.debug({ envVar: \"AZURE_PASSWORD\" }, \"no password found for authenticating to Microsoft Entra ID\")\n      }\n      return false\n    }\n    return true\n  }\n\n  computePublisherName(): Promise<string> {\n    return Promise.resolve(this.packager.platformSpecificBuildOptions.azureSignOptions!.publisherName)\n  }\n  readonly cscInfo = new MemoLazy<WindowsConfiguration, FileCodeSigningInfo | CertificateFromStoreInfo | null>(\n    () => this.packager.platformSpecificBuildOptions,\n    _selected => Promise.resolve(null)\n  )\n  // prerequisite: requires `initializeProviderModules` to already have been executed\n  async signFile(options: WindowsSignOptions): Promise<boolean> {\n    const vm = await this.packager.vm.value\n    const ps = await vm.powershellCommand.value\n\n    const {\n      publisherName: _publisher, // extract from `extraSigningArgs`\n      endpoint,\n      certificateProfileName,\n      codeSigningAccountName,\n      fileDigest,\n      timestampRfc3161,\n      timestampDigest,\n      ...extraSigningArgs\n    }: WindowsAzureSigningConfiguration = options.options.azureSignOptions!\n    const params = {\n      ...extraSigningArgs,\n      Endpoint: endpoint,\n      CertificateProfileName: certificateProfileName,\n      CodeSigningAccountName: codeSigningAccountName,\n      TimestampRfc3161: timestampRfc3161 || \"http://timestamp.acs.microsoft.com\",\n      TimestampDigest: timestampDigest || \"SHA256\",\n      FileDigest: fileDigest || \"SHA256\",\n      Files: options.path,\n    }\n    const paramsString = Object.entries(params)\n      .filter(([_, value]) => value != null)\n      .reduce((res, [field, value]) => {\n        const escapedValue = String(value).replace(/'/g, \"''\")\n        return [...res, `-${field}`, `'${escapedValue}'`]\n      }, [] as string[])\n      .join(\" \")\n    await vm.exec(ps, [\"-NoProfile\", \"-NonInteractive\", \"-Command\", `Invoke-TrustedSigning ${paramsString}`])\n\n    return true\n  }\n}\n"]}