{"version": 3, "file": "ElectronFramework.js", "sourceRoot": "", "sources": ["../../src/electron/ElectronFramework.ts"], "names": [], "mappings": ";;AA2BA,gDAKC;AAmID,wEAiBC;AApLD,+CAA8J;AAC9J,uCAAwD;AACxD,6BAA4B;AAC5B,qDAAuC;AAGvC,oCAA6C;AAG7C,qDAAqD;AACrD,6CAAiD;AACjD,+CAA4C;AAC5C,uDAA2F;AAC3F,+CAAmD;AACnD,iDAAyC;AAazC,SAAgB,kBAAkB,CAAC,IAAmB;;IACpD,OAAO;QACL,WAAW,EAAE,CAAA,MAAA,IAAI,CAAC,gBAAgB,0CAAE,WAAW,KAAI,UAAU;QAC7D,WAAW,EAAE,CAAA,MAAA,IAAI,CAAC,gBAAgB,0CAAE,WAAW,KAAI,UAAU;KAC9D,CAAA;AACH,CAAC;AA6BD,SAAS,kBAAkB,CAAC,IAAmB,EAAE,QAA8B,EAAE,IAAY,EAAE,eAAuB;IACpH,OAAO;QACL,QAAQ;QACR,IAAI;QACJ,OAAO,EAAE,eAAe;QACxB,GAAG,IAAI,CAAC,gBAAgB;KACzB,CAAA;AACH,CAAC;AAED,KAAK,UAAU,oBAAoB,CAAC,OAAoC;IACtE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAA;IACvC,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;IAC5D,IAAI,QAAQ,CAAC,QAAQ,KAAK,gBAAQ,CAAC,KAAK,EAAE,CAAC;QACzC,MAAM,aAAa,GAAG,QAAyB,CAAA;QAC/C,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,CAAC,cAAc,CAAC,CAAA;QACrE,MAAM,IAAA,iBAAM,EAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,gBAAgB,CAAC,WAAW,CAAC,EAAE,UAAU,CAAC,CAAA;IAC9E,CAAC;SAAM,IAAI,QAAQ,CAAC,QAAQ,KAAK,gBAAQ,CAAC,OAAO,EAAE,CAAC;QAClD,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,QAAQ,CAAC,OAAO,CAAC,eAAe,MAAM,CAAC,CAAA;QAClF,MAAM,IAAA,iBAAM,EAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,gBAAgB,CAAC,WAAW,MAAM,CAAC,EAAE,UAAU,CAAC,CAAA;QACrF,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YAC1B,MAAM,IAAA,iCAAmB,EAAC,UAAU,EAAE,OAAO,CAAC,aAAa,CAAC,CAAA;QAC9D,CAAC;IACH,CAAC;SAAM,CAAC;QACN,MAAM,IAAA,0BAAY,EAAC,QAAuB,EAAE,SAAS,EAAE,OAAO,CAAC,aAAa,EAAG,OAAO,CAAC,YAAqC,KAAK,KAAK,CAAC,CAAA;IACzI,CAAC;IACD,MAAM,6BAA6B,CAAC,OAAO,CAAC,CAAA;AAC9C,CAAC;AAED,KAAK,UAAU,6BAA6B,CAAC,OAAoC;IAC/E,MAAM,EACJ,QAAQ,EAAE,EAAE,MAAM,EAAE,4BAA4B,EAAE,GACnD,GAAG,OAAO,CAAA;IACX,MAAM,eAAe,GAAG,IAAA,sBAAO,EAAC,4BAA4B,CAAC,iBAAiB,IAAI,MAAM,CAAC,iBAAiB,CAAC,CAAA;IAC3G,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;QAC5B,OAAM;IACR,CAAC;IAED,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAA;IACvD,uCAAuC;IACvC,MAAM,YAAY,GAAG,KAAK,EAAE,GAAW,EAAE,EAAE;QACzC,MAAM,IAAA,yBAAS,EAAC,gCAAiB,EAAE,MAAM,IAAA,kBAAO,EAAC,GAAG,CAAC,EAAE,KAAK,EAAC,IAAI,EAAC,EAAE;YAClE,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,WAAW,EAAE,CAAC;gBACvC,OAAM;YACR,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,CAAA;YACjD,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACxC,OAAO,IAAA,aAAE,EAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;YACnE,CAAC;YACD,OAAM;QACR,CAAC,CAAC,CAAA;IACJ,CAAC,CAAA;IACD,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAA;IAEzC,SAAS,gBAAgB,CAAC,OAAoC;QAC5D,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAA;QACvC,IAAI,QAAQ,CAAC,QAAQ,KAAK,gBAAQ,CAAC,GAAG,EAAE,CAAC;YACvC,OAAO,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC,qCAAqC,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAA;QAC1I,CAAC;QACD,OAAO,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAA;IACzG,CAAC;AACH,CAAC;AAED,MAAM,iBAAiB;IAUrB,YACW,IAAY,EACZ,OAAe,EACf,gBAAwB;QAFxB,SAAI,GAAJ,IAAI,CAAQ;QACZ,YAAO,GAAP,OAAO,CAAQ;QACf,qBAAgB,GAAhB,gBAAgB,CAAQ;QAZnC,qCAAqC;QAC5B,wBAAmB,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC7C,qCAAqC;QAC5B,uBAAkB,GAAG,eAAe,CAAA;QAC7C,qCAAqC;QAC5B,wBAAmB,GAAG,IAAI,CAAA;QACnC,qCAAqC;QAC5B,yBAAoB,GAAG,IAAI,CAAA;IAMjC,CAAC;IAEJ,cAAc,CAAC,QAAkB;QAC/B,IAAI,QAAQ,KAAK,gBAAQ,CAAC,KAAK,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAA,6BAAe,EAAC,OAAO,CAAC,EAAE,gBAAgB,CAAC,CAAA;QAC9D,CAAC;aAAM,CAAC;YACN,6CAA6C;YAC7C,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gCAAgC,CAAC,OAAgD;QACrF,MAAM,eAAe,GAAG,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;QACrH,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,OAAO,EAAE,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAA;QACnF,MAAM,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAA;QACvE,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;YACpD,MAAM,IAAA,sBAAY,EAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;QAC3C,CAAC;IACH,CAAC;IAED,oBAAoB,CAAC,OAAoC;QACvD,OAAO,oBAAoB,CAAC,OAAO,CAAC,CAAA;IACtC,CAAC;CACF;AAEM,KAAK,UAAU,8BAA8B,CAAC,aAA4B,EAAE,QAAkB;IACnG,IAAI,OAAO,GAAG,aAAa,CAAC,eAAe,CAAA;IAC3C,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,qDAAqD;QACrD,IAAI,QAAQ,CAAC,kBAAkB,EAAE,CAAC;YAChC,OAAO,GAAG,MAAM,IAAA,iDAA+B,EAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;YACpE,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAA;YACvE,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,MAAM,IAAA,wCAAsB,EAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;QAC7D,CAAC;QACD,aAAa,CAAC,eAAe,GAAG,OAAO,CAAA;IACzC,CAAC;IAED,MAAM,QAAQ,GAAG,kBAAkB,CAAC,aAAa,CAAC,CAAA;IAClD,OAAO,IAAI,iBAAiB,CAAC,QAAQ,CAAC,WAAW,EAAE,OAAO,EAAE,GAAG,QAAQ,CAAC,WAAW,MAAM,CAAC,CAAA;AAC5F,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,MAAM,CAAC,cAAuD,EAAE,eAAwC,EAAE,gBAAwB;IAC/I,MAAM,2BAA2B,GAAG,CAAC,OAAgC,EAAE,EAAE;QACvE,OAAO,IAAA,gCAAiB,EAAC,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,UAAU,EAAE,SAAS,EAAE,oBAAoB,EAAE,gBAAgB,CAAC,CAAC,CAAA;IAC5J,CAAC,CAAA;IAED,MAAM,gCAAgC,GAAG,KAAK,EAAE,UAAkB,EAAE,EAAE;QACpE,kBAAG,CAAC,IAAI,CAAC,EAAE,YAAY,EAAE,kBAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,EAAE,6CAA6C,CAAC,CAAA;QACnG,MAAM,MAAM,GAAG,QAAQ,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAA;QACrD,MAAM,WAAW,GAAG,QAAQ,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAA;QACjE,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,2BAA2B,CAAC,CAAA;QAC9D,MAAM,IAAA,mBAAQ,EAAC,SAAS,CAAC,CAAA;QACzB,MAAM,IAAA,sBAAO,EAAC,MAAM,EAAE,WAAW,EAAE;YACjC,aAAa,EAAE,oCAAqB;SACrC,CAAC,CAAA;QACF,OAAO,KAAK,CAAA;IACd,CAAC,CAAA;IAED,MAAM,cAAc,GAAG,KAAK,EAAE,QAAgB,EAAE,EAAE;QAChD,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAA;QAEvG,MAAM,iBAAiB,GAAG,MAAM,IAAA,yBAAU,EAAC,YAAY,CAAC,CAAA;QACxD,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CACb,8CAA8C,YAAY,uGAAuG,CAClK,CAAA;QACH,CAAC;QAED,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAClC,kBAAG,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE,oCAAoC,CAAC,CAAA;YACzE,MAAM,2BAA2B,CAAC;gBAChC,GAAG,eAAe;gBAClB,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,mDAAmD;gBACtF,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,6CAA6C;aAC3F,CAAC,CAAA;YACF,OAAO,KAAK,CAAA,CAAC,qHAAqH;QACpI,CAAC;QAED,IAAI,iBAAiB,CAAC,WAAW,EAAE,EAAE,CAAC;YACpC,mGAAmG;YACnG,MAAM,KAAK,GAAG,MAAM,IAAA,kBAAO,EAAC,YAAY,CAAC,CAAA;YACzC,IAAI,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;gBACnC,kBAAG,CAAC,IAAI,CAAC,EAAE,YAAY,EAAE,kBAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,EAAE,qCAAqC,CAAC,CAAA;gBAC7F,MAAM,2BAA2B,CAAC;oBAChC,GAAG,eAAe;oBAClB,KAAK,EAAE,YAAY;oBACnB,cAAc,EAAE,cAAc;iBAC/B,CAAC,CAAA;gBACF,OAAO,KAAK,CAAA;YACd,CAAC;YACD,uHAAuH;YACvH,sEAAsE;YACtE,OAAO,MAAM,gCAAgC,CAAC,YAAY,CAAC,CAAA;QAC7D,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,qEAAqE,YAAY,4EAA4E,CAAC,CAAA;IAChL,CAAC,CAAA;IAED,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,cAAc,CAAA;IAC5D,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,eAAe,CAAA;IACzC,MAAM,cAAc,GAAG,aAAa,OAAO,IAAI,YAAY,IAAI,IAAI,MAAM,CAAA;IAEzE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAA;IACjD,IAAI,OAAO,YAAY,KAAK,QAAQ,IAAI,CAAC,IAAA,8BAAe,EAAC,YAAY,CAAC,EAAE,CAAC;QACvE,OAAO,cAAc,CAAC,YAAY,CAAC,CAAA;IACrC,CAAC;IAED,IAAI,YAAY,GAAkB,IAAI,CAAA;IACtC,IAAI,CAAC;QACH,MAAM,gBAAgB,GAAQ,MAAM,IAAA,yBAAe,EAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,YAAY,EAAE,cAAc,CAAC,CAAA;QACxG,YAAY,GAAG,OAAO,gBAAgB,KAAK,UAAU,CAAC,CAAC,CAAC,MAAM,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAA;IACpI,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,kBAAG,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,4DAA4D,CAAC,CAAA;IACnF,CAAC;IAED,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;QACzB,sEAAsE;QACtE,kBAAG,CAAC,KAAK,CAAC,IAAI,EAAE,0EAA0E,CAAC,CAAA;QAC3F,MAAM,2BAA2B,CAAC,eAAe,CAAC,CAAA;QAClD,OAAO,IAAI,CAAA,CAAC,oDAAoD;IAClE,CAAC;IACD,OAAO,cAAc,CAAC,YAAY,CAAC,CAAA;AACrC,CAAC;AAED,SAAS,kBAAkB,CAAC,cAAuD,EAAE,gBAAwB,EAAE,aAAsB;IACnI,MAAM,GAAG,GAAG,cAAc,CAAC,SAAS,CAAA;IACpC,MAAM,KAAK,GAAG,cAAc,CAAC,QAAQ,CAAC,QAAQ,KAAK,gBAAQ,CAAC,GAAG,CAAA;IAC/D,MAAM,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,gBAAgB,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,WAAW,CAAC,CAAA;IAErH,OAAO,OAAO,CAAC,GAAG,CAAC;QACjB,aAAa,CAAC,CAAC,CAAC,IAAA,6BAAc,EAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE;QAChG,aAAa,CAAC,CAAC,CAAC,IAAA,6BAAc,EAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE;QAC7E,KAAK;YACH,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE;YACnB,CAAC,CAAC,IAAA,iBAAM,EAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,sBAAsB,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;gBACnF,YAAY;YACd,CAAC,CAAC;KACP,CAAC,CAAA;AACJ,CAAC", "sourcesContent": ["import { asArray, copyDir, DO_NOT_USE_HARD_LINKS, executeAppBuilder, isEmptyOrSpaces, log, MAX_FILE_REQUESTS, statOrNull, unlinkIfExists } from \"builder-util\"\nimport { emptyDir, readdir, rename, rm } from \"fs-extra\"\nimport * as path from \"path\"\nimport asyncPool from \"tiny-async-pool\"\nimport { Configuration } from \"../configuration\"\nimport { BeforeCopyExtraFilesOptions, Framework, PrepareApplicationStageDirectoryOptions } from \"../Framework\"\nimport { Packager, Platform } from \"../index\"\nimport { LinuxPackager } from \"../linuxPackager\"\nimport { MacPackager } from \"../macPackager\"\nimport { getTemplatePath } from \"../util/pathManager\"\nimport { resolveFunction } from \"../util/resolve\"\nimport { createMacApp } from \"./electronMac\"\nimport { computeElectronVersion, getElectronVersionFromInstalled } from \"./electronVersion\"\nimport { addWinAsarIntegrity } from \"./electronWin\"\nimport injectFFMPEG from \"./injectFFMPEG\"\n\nexport type ElectronPlatformName = \"darwin\" | \"linux\" | \"win32\" | \"mas\"\n\n/**\n * Electron distributables branding options.\n * @see [Electron BRANDING.json](https://github.com/electron/electron/blob/master/shell/app/BRANDING.json).\n */\nexport interface ElectronBrandingOptions {\n  projectName?: string\n  productName?: string\n}\n\nexport function createBrandingOpts(opts: Configuration): Required<ElectronBrandingOptions> {\n  return {\n    projectName: opts.electronBranding?.projectName || \"electron\",\n    productName: opts.electronBranding?.productName || \"Electron\",\n  }\n}\n\nexport interface ElectronDownloadOptions {\n  // https://github.com/electron-userland/electron-builder/issues/3077\n  // must be optional\n  version?: string\n\n  /**\n   * The [cache location](https://github.com/electron-userland/electron-download#cache-location).\n   */\n  cache?: string | null\n\n  /**\n   * The mirror.\n   */\n  mirror?: string | null\n\n  /** @private */\n  customDir?: string | null\n  /** @private */\n  customFilename?: string | null\n\n  strictSSL?: boolean\n  isVerifyChecksum?: boolean\n\n  platform?: ElectronPlatformName\n  arch?: string\n}\n\nfunction createDownloadOpts(opts: Configuration, platform: ElectronPlatformName, arch: string, electronVersion: string): ElectronDownloadOptions {\n  return {\n    platform,\n    arch,\n    version: electronVersion,\n    ...opts.electronDownload,\n  }\n}\n\nasync function beforeCopyExtraFiles(options: BeforeCopyExtraFilesOptions) {\n  const { appOutDir, packager } = options\n  const electronBranding = createBrandingOpts(packager.config)\n  if (packager.platform === Platform.LINUX) {\n    const linuxPackager = packager as LinuxPackager\n    const executable = path.join(appOutDir, linuxPackager.executableName)\n    await rename(path.join(appOutDir, electronBranding.projectName), executable)\n  } else if (packager.platform === Platform.WINDOWS) {\n    const executable = path.join(appOutDir, `${packager.appInfo.productFilename}.exe`)\n    await rename(path.join(appOutDir, `${electronBranding.projectName}.exe`), executable)\n    if (options.asarIntegrity) {\n      await addWinAsarIntegrity(executable, options.asarIntegrity)\n    }\n  } else {\n    await createMacApp(packager as MacPackager, appOutDir, options.asarIntegrity, (options.platformName as ElectronPlatformName) === \"mas\")\n  }\n  await removeUnusedLanguagesIfNeeded(options)\n}\n\nasync function removeUnusedLanguagesIfNeeded(options: BeforeCopyExtraFilesOptions) {\n  const {\n    packager: { config, platformSpecificBuildOptions },\n  } = options\n  const wantedLanguages = asArray(platformSpecificBuildOptions.electronLanguages || config.electronLanguages)\n  if (!wantedLanguages.length) {\n    return\n  }\n\n  const { dirs, langFileExt } = getLocalesConfig(options)\n  // noinspection SpellCheckingInspection\n  const deletedFiles = async (dir: string) => {\n    await asyncPool(MAX_FILE_REQUESTS, await readdir(dir), async file => {\n      if (path.extname(file) !== langFileExt) {\n        return\n      }\n\n      const language = path.basename(file, langFileExt)\n      if (!wantedLanguages.includes(language)) {\n        return rm(path.join(dir, file), { recursive: true, force: true })\n      }\n      return\n    })\n  }\n  await Promise.all(dirs.map(deletedFiles))\n\n  function getLocalesConfig(options: BeforeCopyExtraFilesOptions) {\n    const { appOutDir, packager } = options\n    if (packager.platform === Platform.MAC) {\n      return { dirs: [packager.getResourcesDir(appOutDir), packager.getMacOsElectronFrameworkResourcesDir(appOutDir)], langFileExt: \".lproj\" }\n    }\n    return { dirs: [path.join(packager.getResourcesDir(appOutDir), \"..\", \"locales\")], langFileExt: \".pak\" }\n  }\n}\n\nclass ElectronFramework implements Framework {\n  // noinspection JSUnusedGlobalSymbols\n  readonly macOsDefaultTargets = [\"zip\", \"dmg\"]\n  // noinspection JSUnusedGlobalSymbols\n  readonly defaultAppIdPrefix = \"com.electron.\"\n  // noinspection JSUnusedGlobalSymbols\n  readonly isCopyElevateHelper = true\n  // noinspection JSUnusedGlobalSymbols\n  readonly isNpmRebuildRequired = true\n\n  constructor(\n    readonly name: string,\n    readonly version: string,\n    readonly distMacOsAppName: string\n  ) {}\n\n  getDefaultIcon(platform: Platform) {\n    if (platform === Platform.LINUX) {\n      return path.join(getTemplatePath(\"icons\"), \"electron-linux\")\n    } else {\n      // default icon is embedded into app skeleton\n      return null\n    }\n  }\n\n  async prepareApplicationStageDirectory(options: PrepareApplicationStageDirectoryOptions) {\n    const downloadOptions = createDownloadOpts(options.packager.config, options.platformName, options.arch, this.version)\n    const shouldCleanup = await unpack(options, downloadOptions, this.distMacOsAppName)\n    await cleanupAfterUnpack(options, this.distMacOsAppName, shouldCleanup)\n    if (options.packager.config.downloadAlternateFFmpeg) {\n      await injectFFMPEG(options, this.version)\n    }\n  }\n\n  beforeCopyExtraFiles(options: BeforeCopyExtraFilesOptions) {\n    return beforeCopyExtraFiles(options)\n  }\n}\n\nexport async function createElectronFrameworkSupport(configuration: Configuration, packager: Packager): Promise<Framework> {\n  let version = configuration.electronVersion\n  if (version == null) {\n    // for prepacked app asar no dev deps in the app.asar\n    if (packager.isPrepackedAppAsar) {\n      version = await getElectronVersionFromInstalled(packager.projectDir)\n      if (version == null) {\n        throw new Error(`Cannot compute electron version for prepacked asar`)\n      }\n    } else {\n      version = await computeElectronVersion(packager.projectDir)\n    }\n    configuration.electronVersion = version\n  }\n\n  const branding = createBrandingOpts(configuration)\n  return new ElectronFramework(branding.projectName, version, `${branding.productName}.app`)\n}\n\n/**\n * Unpacks a custom or default Electron distribution into the app output directory.\n */\nasync function unpack(prepareOptions: PrepareApplicationStageDirectoryOptions, downloadOptions: ElectronDownloadOptions, distMacOsAppName: string): Promise<boolean> {\n  const downloadUsingAdjustedConfig = (options: ElectronDownloadOptions) => {\n    return executeAppBuilder([\"unpack-electron\", \"--configuration\", JSON.stringify([options]), \"--output\", appOutDir, \"--distMacOsAppName\", distMacOsAppName])\n  }\n\n  const copyUnpackedElectronDistribution = async (folderPath: string) => {\n    log.info({ electronDist: log.filePath(folderPath) }, \"using custom unpacked Electron distribution\")\n    const source = packager.getElectronSrcDir(folderPath)\n    const destination = packager.getElectronDestinationDir(appOutDir)\n    log.info({ source, destination }, \"copying unpacked Electron\")\n    await emptyDir(appOutDir)\n    await copyDir(source, destination, {\n      isUseHardLink: DO_NOT_USE_HARD_LINKS,\n    })\n    return false\n  }\n\n  const selectElectron = async (filepath: string) => {\n    const resolvedDist = path.isAbsolute(filepath) ? filepath : path.resolve(packager.projectDir, filepath)\n\n    const electronDistStats = await statOrNull(resolvedDist)\n    if (!electronDistStats) {\n      throw new Error(\n        `The specified electronDist does not exist: ${resolvedDist}. Please provide a valid path to the Electron zip file, cache directory, or electron build directory.`\n      )\n    }\n\n    if (resolvedDist.endsWith(\".zip\")) {\n      log.info({ zipFile: resolvedDist }, \"using custom electronDist zip file\")\n      await downloadUsingAdjustedConfig({\n        ...downloadOptions,\n        cache: path.dirname(resolvedDist), // set custom directory to the zip file's directory\n        customFilename: path.basename(resolvedDist), // set custom filename to the zip file's name\n      })\n      return false // do not clean up after unpacking, it's a custom bundle and we should respect its configuration/contents as required\n    }\n\n    if (electronDistStats.isDirectory()) {\n      // backward compatibility: if electronDist is a directory, check for the default zip file inside it\n      const files = await readdir(resolvedDist)\n      if (files.includes(defaultZipName)) {\n        log.info({ electronDist: log.filePath(resolvedDist) }, \"using custom electronDist directory\")\n        await downloadUsingAdjustedConfig({\n          ...downloadOptions,\n          cache: resolvedDist,\n          customFilename: defaultZipName,\n        })\n        return false\n      }\n      // if we reach here, it means the provided electronDist is neither a zip file nor a directory with the default zip file\n      // e.g. we treat it as a custom already-unpacked Electron distribution\n      return await copyUnpackedElectronDistribution(resolvedDist)\n    }\n    throw new Error(`The specified electronDist is neither a zip file nor a directory: ${resolvedDist}. Please provide a valid path to the Electron zip file or cache directory.`)\n  }\n\n  const { packager, appOutDir, platformName } = prepareOptions\n  const { version, arch } = downloadOptions\n  const defaultZipName = `electron-v${version}-${platformName}-${arch}.zip`\n\n  const electronDist = packager.config.electronDist\n  if (typeof electronDist === \"string\" && !isEmptyOrSpaces(electronDist)) {\n    return selectElectron(electronDist)\n  }\n\n  let resolvedDist: string | null = null\n  try {\n    const electronDistHook: any = await resolveFunction(packager.appInfo.type, electronDist, \"electronDist\")\n    resolvedDist = typeof electronDistHook === \"function\" ? await Promise.resolve(electronDistHook(prepareOptions)) : electronDistHook\n  } catch (error: any) {\n    log.warn({ error }, \"Failed to resolve electronDist, using default unpack logic\")\n  }\n\n  if (resolvedDist == null) {\n    // if no custom electronDist is provided, use the default unpack logic\n    log.debug(null, \"no custom electronDist provided, unpacking default Electron distribution\")\n    await downloadUsingAdjustedConfig(downloadOptions)\n    return true // indicates that we should clean up after unpacking\n  }\n  return selectElectron(resolvedDist)\n}\n\nfunction cleanupAfterUnpack(prepareOptions: PrepareApplicationStageDirectoryOptions, distMacOsAppName: string, isFullCleanup: boolean) {\n  const out = prepareOptions.appOutDir\n  const isMac = prepareOptions.packager.platform === Platform.MAC\n  const resourcesPath = isMac ? path.join(out, distMacOsAppName, \"Contents\", \"Resources\") : path.join(out, \"resources\")\n\n  return Promise.all([\n    isFullCleanup ? unlinkIfExists(path.join(resourcesPath, \"default_app.asar\")) : Promise.resolve(),\n    isFullCleanup ? unlinkIfExists(path.join(out, \"version\")) : Promise.resolve(),\n    isMac\n      ? Promise.resolve()\n      : rename(path.join(out, \"LICENSE\"), path.join(out, \"LICENSE.electron.txt\")).catch(() => {\n          /* ignore */\n        }),\n  ])\n}\n"]}