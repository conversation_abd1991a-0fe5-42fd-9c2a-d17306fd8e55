export declare function download(url: string, output: string, checksum?: string | null): Promise<void>;
export declare function getBinFromCustomLoc(name: string, version: string, binariesLocUrl: string, checksum: string): Promise<string>;
export declare function getBinFromUrl(releaseName: string, filenameWithExt: string, checksum: string, githubOrgRepo?: string): Promise<string>;
export declare function getBin(name: string, url?: string | null, checksum?: string | null): Promise<string>;
