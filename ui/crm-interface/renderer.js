const { ipc<PERSON><PERSON><PERSON> } = require('electron');

class MessageConsumerApp {
    constructor() {
        this.solaceClient = new SolaceClient();
        this.messages = [];
        this.messageCount = 0;
        this.messageRate = 0;
        this.lastMessageTime = Date.now();
        this.messageRateInterval = null;
        this.currentTopic = null;
        this.config = null;
        
        this.initializeElements();
        this.setupEventListeners();
        this.loadConfiguration();
        this.setupSolaceClient();
        this.startMessageRateCalculation();
    }

    initializeElements() {
        // Status elements
        this.statusIndicator = document.getElementById('status-indicator');
        this.statusText = document.getElementById('status-text');
        
        // Control elements
        this.topicInput = document.getElementById('topic-input');
        this.subscribeBtn = document.getElementById('subscribe-btn');
        this.unsubscribeBtn = document.getElementById('unsubscribe-btn');
        this.connectBtn = document.getElementById('connect-btn');
        this.disconnectBtn = document.getElementById('disconnect-btn');
        this.clearBtn = document.getElementById('clear-btn');
        
        // Message elements
        this.messageList = document.getElementById('message-list');
        this.messageCount = document.getElementById('message-count');
        this.messageRateDisplay = document.getElementById('message-rate');
        
        // Config display elements
        this.configUrl = document.getElementById('config-url');
        this.configVpn = document.getElementById('config-vpn');
        this.configUsername = document.getElementById('config-username');

        // Assessment display elements
        this.assessmentContainer = document.getElementById('assessment-container');
        this.closeAssessmentBtn = document.getElementById('close-assessment');
        this.customerDetails = document.getElementById('customer-details');
        this.priorityInfo = document.getElementById('priority-info');
        this.urgencyInfo = document.getElementById('urgency-info');
        this.transactionsList = document.getElementById('transactions-list');
        this.locationInfo = document.getElementById('location-info');

        // Map and chart elements
        this.map = null;
        this.transactionsChart = null;
    }

    setupEventListeners() {
        this.connectBtn.addEventListener('click', () => this.connect());
        this.disconnectBtn.addEventListener('click', () => this.disconnect());
        this.subscribeBtn.addEventListener('click', () => this.subscribe());
        this.unsubscribeBtn.addEventListener('click', () => this.unsubscribe());
        this.clearBtn.addEventListener('click', () => this.clearMessages());

        // Assessment close button
        this.closeAssessmentBtn.addEventListener('click', () => this.hideAssessment());

        // Enter key handlers
        this.topicInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.subscribe();
            }
        });
    }

    async loadConfiguration() {
        try {
            this.config = await ipcRenderer.invoke('get-config');
            this.updateConfigDisplay();
            this.topicInput.value = this.config.topic || '';
        } catch (error) {
            console.error('Error loading configuration:', error);
            await ipcRenderer.invoke('show-error', 'Configuration Error', 'Failed to load configuration');
        }
    }

    updateConfigDisplay() {
        if (this.config) {
            this.configUrl.textContent = this.config.url;
            this.configVpn.textContent = this.config.vpn;
            this.configUsername.textContent = this.config.username;
        }
    }

    setupSolaceClient() {
        this.solaceClient.setStatusCallback((status, message) => {
            this.updateConnectionStatus(status, message);
        });

        this.solaceClient.setMessageCallback((messageData) => {
            this.addMessage(messageData);
        });
    }

    updateConnectionStatus(status, message) {
        this.statusText.textContent = message;
        
        // Remove all status classes
        this.statusIndicator.classList.remove('connected', 'disconnected', 'connecting');
        
        // Add appropriate class
        this.statusIndicator.classList.add(status);
        
        // Update button states
        const isConnected = status === 'connected';
        const isConnecting = status === 'connecting';
        
        this.connectBtn.disabled = isConnected || isConnecting;
        this.disconnectBtn.disabled = !isConnected && !isConnecting;
        this.subscribeBtn.disabled = !isConnected;
        
        if (!isConnected) {
            this.unsubscribeBtn.disabled = true;
            this.currentTopic = null;
        }
    }

    async connect() {
        if (!this.config) {
            await ipcRenderer.invoke('show-error', 'Configuration Error', 'No configuration available');
            return;
        }

        try {
            await this.solaceClient.connect(this.config);
        } catch (error) {
            console.error('Connection error:', error);
            await ipcRenderer.invoke('show-error', 'Connection Error', `Failed to connect: ${error.message}`);
        }
    }

    disconnect() {
        this.solaceClient.disconnect();
        this.currentTopic = null;
        this.unsubscribeBtn.disabled = true;
    }

    subscribe() {
        const topic = this.topicInput.value.trim();
        if (!topic) {
            ipcRenderer.invoke('show-error', 'Topic Error', 'Please enter a topic name');
            return;
        }

        try {
            // Unsubscribe from current topic if any
            if (this.currentTopic) {
                this.solaceClient.unsubscribe(this.currentTopic);
            }

            this.solaceClient.subscribe(topic);
            this.currentTopic = topic;
            this.unsubscribeBtn.disabled = false;
            
            // Clear previous messages when subscribing to new topic
            this.clearMessages();
            
        } catch (error) {
            console.error('Subscription error:', error);
            ipcRenderer.invoke('show-error', 'Subscription Error', `Failed to subscribe: ${error.message}`);
        }
    }

    unsubscribe() {
        if (this.currentTopic) {
            try {
                this.solaceClient.unsubscribe(this.currentTopic);
                this.currentTopic = null;
                this.unsubscribeBtn.disabled = true;
            } catch (error) {
                console.error('Unsubscription error:', error);
                ipcRenderer.invoke('show-error', 'Unsubscription Error', `Failed to unsubscribe: ${error.message}`);
            }
        }
    }

    addMessage(messageData) {
        this.messages.push(messageData);
        this.updateMessageCount();

        // Check if this is a customer service assessment
        if (this.isCustomerServiceAssessment(messageData.payload)) {
            this.displayAssessment(messageData.payload);
        }

        this.renderMessage(messageData);

        // Auto-scroll to newest messages
        this.messageList.scrollTop = this.messageList.scrollHeight;
    }

    renderMessage(messageData, isFiltered = false) {
        const messageElement = document.createElement('div');
        messageElement.className = 'message-item';
        if (!isFiltered) {
            messageElement.classList.add('new');
        }

        const timestamp = new Date(messageData.timestamp).toLocaleString();
        
        messageElement.innerHTML = `
            <div class="message-timestamp">${timestamp}</div>
            <div class="message-topic">Topic: ${messageData.topic}</div>
            <div class="message-payload">${this.escapeHtml(messageData.payload)}</div>
        `;

        this.messageList.appendChild(messageElement);
        
        // Remove 'new' class after animation
        if (!isFiltered) {
            setTimeout(() => {
                messageElement.classList.remove('new');
            }, 300);
        }
    }

    clearMessages() {
        this.messages = [];
        this.messageList.innerHTML = '';
        this.updateMessageCount();
    }

    updateMessageCount() {
        this.messageCount.textContent = this.messages.length;
    }

    startMessageRateCalculation() {
        let lastCount = 0;
        this.messageRateInterval = setInterval(() => {
            const currentCount = this.messages.length;
            const rate = currentCount - lastCount;
            this.messageRateDisplay.textContent = rate;
            lastCount = currentCount;
        }, 1000);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    isCustomerServiceAssessment(payload) {
        try {
            const data = JSON.parse(payload);
            return data && data.customer_service_assessment &&
                   data.customer_service_assessment.priority &&
                   data.customer_service_assessment.urgency &&
                   data.customer_service_assessment.customer_location;
        } catch (e) {
            return false;
        }
    }

    displayAssessment(payload) {
        try {
            const data = JSON.parse(payload);
            const assessment = data.customer_service_assessment;

            // Populate customer information
            this.populateCustomerInfo(assessment.customer_location);

            // Populate priority and urgency
            this.populatePriorityUrgency(assessment.priority, assessment.urgency);

            // Populate recent transactions
            this.populateTransactions(assessment.recent_transactions);

            // Populate location information
            this.populateLocationInfo(assessment.customer_location);

            // Show the assessment container
            this.showAssessment();

        } catch (error) {
            console.error('Error displaying assessment:', error);
        }
    }

    populateCustomerInfo(customerLocation) {
        this.customerDetails.innerHTML = `
            <span class="label">Name:</span>
            <span class="value">${customerLocation.first_name} ${customerLocation.last_name}</span>
            <span class="label">Phone:</span>
            <span class="value">${customerLocation.phone_number}</span>
            <span class="label">Address:</span>
            <span class="value">${customerLocation.address}</span>
            <span class="label">City:</span>
            <span class="value">${customerLocation.city}, ${customerLocation.province}</span>
            <span class="label">Postal Code:</span>
            <span class="value">${customerLocation.postal_code}</span>
            <span class="label">Country:</span>
            <span class="value">${customerLocation.country}</span>
        `;
    }

    populatePriorityUrgency(priority, urgency) {
        // Priority section
        const priorityLevel = priority.level.toLowerCase();
        this.priorityInfo.className = `priority-info ${priorityLevel}`;
        this.priorityInfo.innerHTML = `
            <div class="level-badge ${priorityLevel}">${priority.level}</div>
            <div class="reason-text">${priority.reason}</div>
        `;

        // Urgency section
        const urgencyLevel = urgency.level.toLowerCase();
        this.urgencyInfo.className = `urgency-info ${urgencyLevel}`;
        this.urgencyInfo.innerHTML = `
            <div class="level-badge ${urgencyLevel}">${urgency.level}</div>
            <div class="reason-text">${urgency.reason}</div>
        `;
    }

    populateTransactions(transactions) {
        if (!transactions || transactions.length === 0) {
            this.transactionsList.innerHTML = '<div class="no-transactions">No recent transactions available</div>';
            return;
        }

        // Create the bar chart
        this.createTransactionsChart(transactions);

        // Populate the transactions list
        this.transactionsList.innerHTML = transactions.map(transaction => {
            const amount = parseFloat(transaction.amount);
            const amountClass = transaction.type.toLowerCase() === 'deposit' ? 'positive' : 'negative';
            const formattedAmount = new Intl.NumberFormat('en-CA', {
                style: 'currency',
                currency: 'CAD'
            }).format(Math.abs(amount));

            return `
                <div class="transaction-item">
                    <div class="transaction-date">${new Date(transaction.date).toLocaleDateString()}</div>
                    <div class="transaction-type">${transaction.type}</div>
                    <div class="transaction-amount ${amountClass}">${formattedAmount}</div>
                </div>
            `;
        }).join('');
    }

    createTransactionsChart(transactions) {
        const canvas = document.getElementById('transactionsCanvas');
        const ctx = canvas.getContext('2d');

        // Destroy existing chart if it exists
        if (this.transactionsChart) {
            this.transactionsChart.destroy();
        }

        // Prepare data for the chart
        const labels = transactions.map(t => new Date(t.date).toLocaleDateString());
        const amounts = transactions.map(t => Math.abs(parseFloat(t.amount)));
        const colors = transactions.map(t => {
            switch(t.type.toLowerCase()) {
                case 'deposit': return '#28a745';
                case 'withdrawal': return '#dc3545';
                case 'transfer': return '#ffc107';
                default: return '#6c757d';
            }
        });

        this.transactionsChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Transaction Amount (CAD)',
                    data: amounts,
                    backgroundColor: colors,
                    borderColor: colors.map(color => color + '80'),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const transaction = transactions[context.dataIndex];
                                const amount = new Intl.NumberFormat('en-CA', {
                                    style: 'currency',
                                    currency: 'CAD'
                                }).format(context.parsed.y);
                                return `${transaction.type}: ${amount}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return new Intl.NumberFormat('en-CA', {
                                    style: 'currency',
                                    currency: 'CAD',
                                    minimumFractionDigits: 0
                                }).format(value);
                            }
                        }
                    },
                    x: {
                        ticks: {
                            maxRotation: 45
                        }
                    }
                }
            }
        });
    }

    populateLocationInfo(customerLocation) {
        this.locationInfo.innerHTML = `
            <div class="address-section">
                <div class="address-line"><strong>${customerLocation.first_name} ${customerLocation.last_name}</strong></div>
                <div class="address-line">${customerLocation.address}</div>
                <div class="address-line">${customerLocation.city}, ${customerLocation.province} ${customerLocation.postal_code}</div>
                <div class="address-line">${customerLocation.country}</div>
            </div>
            <div class="coordinates-section">
                <div class="coordinate-item">
                    <div class="coordinate-label">Latitude</div>
                    <div class="coordinate-value">${customerLocation.coordinates.latitude.toFixed(4)}</div>
                </div>
                <div class="coordinate-item">
                    <div class="coordinate-label">Longitude</div>
                    <div class="coordinate-value">${customerLocation.coordinates.longitude.toFixed(4)}</div>
                </div>
            </div>
        `;

        // Create the map
        this.createLocationMap(customerLocation);
    }

    createLocationMap(customerLocation) {
        // Destroy existing map if it exists
        if (this.map) {
            this.map.remove();
        }

        const lat = customerLocation.coordinates.latitude;
        const lng = customerLocation.coordinates.longitude;

        // Initialize the map
        this.map = L.map('map').setView([lat, lng], 13);

        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(this.map);

        // Create a custom marker icon
        const customerIcon = L.divIcon({
            className: 'customer-marker',
            html: '<div style="background-color: #dc3545; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>',
            iconSize: [26, 26],
            iconAnchor: [13, 13]
        });

        // Add marker for customer location
        L.marker([lat, lng], { icon: customerIcon })
            .addTo(this.map)
            .bindPopup(`
                <div style="text-align: center;">
                    <strong>${customerLocation.first_name} ${customerLocation.last_name}</strong><br>
                    ${customerLocation.address}<br>
                    ${customerLocation.city}, ${customerLocation.province}<br>
                    ${customerLocation.postal_code}
                </div>
            `)
            .openPopup();

        // Add a circle to show the general area
        L.circle([lat, lng], {
            color: '#dc3545',
            fillColor: '#dc3545',
            fillOpacity: 0.1,
            radius: 500
        }).addTo(this.map);
    }

    showAssessment() {
        this.assessmentContainer.style.display = 'block';
        // Scroll to assessment
        this.assessmentContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    hideAssessment() {
        this.assessmentContainer.style.display = 'none';

        // Clean up map and chart resources
        if (this.map) {
            this.map.remove();
            this.map = null;
        }

        if (this.transactionsChart) {
            this.transactionsChart.destroy();
            this.transactionsChart = null;
        }
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new MessageConsumerApp();
});
