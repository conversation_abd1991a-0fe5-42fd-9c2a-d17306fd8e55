const { ipc<PERSON><PERSON><PERSON> } = require('electron');

class MessageConsumerApp {
    constructor() {
        this.solaceClient = new SolaceClient();
        this.messages = [];
        this.messageCount = 0;
        this.messageRate = 0;
        this.lastMessageTime = Date.now();
        this.messageRateInterval = null;
        this.currentTopic = null;
        this.config = null;
        
        this.initializeElements();
        this.setupEventListeners();
        this.loadConfiguration();
        this.setupSolaceClient();
        this.startMessageRateCalculation();
    }

    initializeElements() {
        // Status elements
        this.statusIndicator = document.getElementById('status-indicator');
        this.statusText = document.getElementById('status-text');
        
        // Control elements
        this.topicInput = document.getElementById('topic-input');
        this.subscribeBtn = document.getElementById('subscribe-btn');
        this.unsubscribeBtn = document.getElementById('unsubscribe-btn');
        this.connectBtn = document.getElementById('connect-btn');
        this.disconnectBtn = document.getElementById('disconnect-btn');
        this.clearBtn = document.getElementById('clear-btn');
        
        // Message elements
        this.filterInput = document.getElementById('filter-input');
        this.autoScrollCheckbox = document.getElementById('auto-scroll');
        this.messageList = document.getElementById('message-list');
        this.messageCount = document.getElementById('message-count');
        this.messageRateDisplay = document.getElementById('message-rate');
        
        // Config display elements
        this.configUrl = document.getElementById('config-url');
        this.configVpn = document.getElementById('config-vpn');
        this.configUsername = document.getElementById('config-username');
    }

    setupEventListeners() {
        this.connectBtn.addEventListener('click', () => this.connect());
        this.disconnectBtn.addEventListener('click', () => this.disconnect());
        this.subscribeBtn.addEventListener('click', () => this.subscribe());
        this.unsubscribeBtn.addEventListener('click', () => this.unsubscribe());
        this.clearBtn.addEventListener('click', () => this.clearMessages());
        
        this.filterInput.addEventListener('input', () => this.filterMessages());
        
        // Enter key handlers
        this.topicInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.subscribe();
            }
        });
    }

    async loadConfiguration() {
        try {
            this.config = await ipcRenderer.invoke('get-config');
            this.updateConfigDisplay();
            this.topicInput.value = this.config.topic || '';
        } catch (error) {
            console.error('Error loading configuration:', error);
            await ipcRenderer.invoke('show-error', 'Configuration Error', 'Failed to load configuration');
        }
    }

    updateConfigDisplay() {
        if (this.config) {
            this.configUrl.textContent = this.config.url;
            this.configVpn.textContent = this.config.vpn;
            this.configUsername.textContent = this.config.username;
        }
    }

    setupSolaceClient() {
        this.solaceClient.setStatusCallback((status, message) => {
            this.updateConnectionStatus(status, message);
        });

        this.solaceClient.setMessageCallback((messageData) => {
            this.addMessage(messageData);
        });
    }

    updateConnectionStatus(status, message) {
        this.statusText.textContent = message;
        
        // Remove all status classes
        this.statusIndicator.classList.remove('connected', 'disconnected', 'connecting');
        
        // Add appropriate class
        this.statusIndicator.classList.add(status);
        
        // Update button states
        const isConnected = status === 'connected';
        const isConnecting = status === 'connecting';
        
        this.connectBtn.disabled = isConnected || isConnecting;
        this.disconnectBtn.disabled = !isConnected && !isConnecting;
        this.subscribeBtn.disabled = !isConnected;
        
        if (!isConnected) {
            this.unsubscribeBtn.disabled = true;
            this.currentTopic = null;
        }
    }

    async connect() {
        if (!this.config) {
            await ipcRenderer.invoke('show-error', 'Configuration Error', 'No configuration available');
            return;
        }

        try {
            await this.solaceClient.connect(this.config);
        } catch (error) {
            console.error('Connection error:', error);
            await ipcRenderer.invoke('show-error', 'Connection Error', `Failed to connect: ${error.message}`);
        }
    }

    disconnect() {
        this.solaceClient.disconnect();
        this.currentTopic = null;
        this.unsubscribeBtn.disabled = true;
    }

    subscribe() {
        const topic = this.topicInput.value.trim();
        if (!topic) {
            ipcRenderer.invoke('show-error', 'Topic Error', 'Please enter a topic name');
            return;
        }

        try {
            // Unsubscribe from current topic if any
            if (this.currentTopic) {
                this.solaceClient.unsubscribe(this.currentTopic);
            }

            this.solaceClient.subscribe(topic);
            this.currentTopic = topic;
            this.unsubscribeBtn.disabled = false;
            
            // Clear previous messages when subscribing to new topic
            this.clearMessages();
            
        } catch (error) {
            console.error('Subscription error:', error);
            ipcRenderer.invoke('show-error', 'Subscription Error', `Failed to subscribe: ${error.message}`);
        }
    }

    unsubscribe() {
        if (this.currentTopic) {
            try {
                this.solaceClient.unsubscribe(this.currentTopic);
                this.currentTopic = null;
                this.unsubscribeBtn.disabled = true;
            } catch (error) {
                console.error('Unsubscription error:', error);
                ipcRenderer.invoke('show-error', 'Unsubscription Error', `Failed to unsubscribe: ${error.message}`);
            }
        }
    }

    addMessage(messageData) {
        this.messages.push(messageData);
        this.updateMessageCount();
        this.renderMessage(messageData);
        
        // Auto-scroll if enabled
        if (this.autoScrollCheckbox.checked) {
            this.messageList.scrollTop = this.messageList.scrollHeight;
        }
    }

    renderMessage(messageData, isFiltered = false) {
        const messageElement = document.createElement('div');
        messageElement.className = 'message-item';
        if (!isFiltered) {
            messageElement.classList.add('new');
        }

        const timestamp = new Date(messageData.timestamp).toLocaleString();
        
        messageElement.innerHTML = `
            <div class="message-timestamp">${timestamp}</div>
            <div class="message-topic">Topic: ${messageData.topic}</div>
            <div class="message-payload">${this.escapeHtml(messageData.payload)}</div>
        `;

        this.messageList.appendChild(messageElement);
        
        // Remove 'new' class after animation
        if (!isFiltered) {
            setTimeout(() => {
                messageElement.classList.remove('new');
            }, 300);
        }
    }

    clearMessages() {
        this.messages = [];
        this.messageList.innerHTML = '';
        this.updateMessageCount();
    }

    filterMessages() {
        const filterText = this.filterInput.value.toLowerCase();
        this.messageList.innerHTML = '';
        
        const filteredMessages = this.messages.filter(message => 
            message.topic.toLowerCase().includes(filterText) ||
            message.payload.toLowerCase().includes(filterText)
        );
        
        filteredMessages.forEach(message => {
            this.renderMessage(message, true);
        });
    }

    updateMessageCount() {
        this.messageCount.textContent = this.messages.length;
    }

    startMessageRateCalculation() {
        let lastCount = 0;
        this.messageRateInterval = setInterval(() => {
            const currentCount = this.messages.length;
            const rate = currentCount - lastCount;
            this.messageRateDisplay.textContent = rate;
            lastCount = currentCount;
        }, 1000);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new MessageConsumerApp();
});
