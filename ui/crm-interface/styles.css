* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    height: 100vh;
    overflow: hidden;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 100%;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 300;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.connected {
    background-color: #4CAF50;
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
}

.status-indicator.disconnected {
    background-color: #f44336;
}

.status-indicator.connecting {
    background-color: #ff9800;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.controls {
    background: white;
    padding: 1rem 2rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    align-items: center;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-group label {
    font-weight: 500;
    color: #555;
}

input[type="text"] {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    min-width: 200px;
}

input[type="text"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-primary {
    background-color: #667eea;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.message-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    margin: 0 2rem 2rem 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
}

.message-header {
    padding: 1rem;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.message-header h3 {
    color: #333;
    font-weight: 500;
}

.message-stats {
    color: #666;
    font-size: 0.9rem;
}

.message-list {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.message-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    transition: all 0.2s;
}

.message-item:hover {
    background: #e9ecef;
    border-color: #667eea;
}

.message-item.new {
    animation: slideIn 0.3s ease-out;
    border-color: #28a745;
    background: #d4edda;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-timestamp {
    color: #666;
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
}

.message-topic {
    color: #667eea;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.message-payload {
    color: #333;
    word-break: break-all;
    white-space: pre-wrap;
}

.footer {
    background: #f8f9fa;
    padding: 0.75rem 2rem;
    border-top: 1px solid #e0e0e0;
    font-size: 0.85rem;
    color: #666;
}

.config-info {
    display: flex;
    gap: 2rem;
}

.config-info span {
    display: flex;
    gap: 0.25rem;
}

.config-info span span {
    font-weight: 500;
    color: #333;
}

/* Scrollbar styling */
.message-list::-webkit-scrollbar {
    width: 8px;
}

.message-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.message-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.message-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Customer Service Assessment Styles */
.assessment-container {
    background: white;
    margin: 0 2rem 1rem 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.assessment-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.assessment-header h2 {
    font-size: 1.25rem;
    font-weight: 500;
    margin: 0;
}

#close-assessment {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: bold;
    padding: 0;
}

#close-assessment:hover {
    background: rgba(255, 255, 255, 0.3);
}

.assessment-content {
    padding: 2rem;
}

.assessment-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 1.5rem;
}

.assessment-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    transition: all 0.2s;
}

.assessment-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.assessment-card h3 {
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

/* Customer Information Card */
.customer-info {
    grid-column: 1;
    grid-row: 1;
}

.customer-details {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 0.5rem 1rem;
    font-size: 0.9rem;
}

.customer-details .label {
    font-weight: 600;
    color: #666;
}

.customer-details .value {
    color: #333;
}

/* Priority & Urgency Card */
.priority-urgency {
    grid-column: 2;
    grid-row: 1;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.priority-section, .urgency-section {
    flex: 1;
}

.priority-section h3, .urgency-section h3 {
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.priority-info, .urgency-info {
    background: white;
    border-radius: 6px;
    padding: 1rem;
    border-left: 4px solid #ddd;
}

.priority-info.high, .urgency-info.high {
    border-left-color: #dc3545;
    background: #fff5f5;
}

.priority-info.medium, .urgency-info.medium {
    border-left-color: #ffc107;
    background: #fffbf0;
}

.priority-info.low, .urgency-info.low {
    border-left-color: #28a745;
    background: #f8fff9;
}

.level-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    margin-bottom: 0.5rem;
}

.level-badge.high {
    background: #dc3545;
    color: white;
}

.level-badge.medium {
    background: #ffc107;
    color: #333;
}

.level-badge.low {
    background: #28a745;
    color: white;
}

.reason-text {
    font-size: 0.85rem;
    color: #555;
    line-height: 1.4;
}

/* Transactions Card */
.transactions {
    grid-column: 1;
    grid-row: 2;
}

.transactions-chart {
    background: white;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;
}

.transactions-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-height: 150px;
    overflow-y: auto;
}

.transaction-item {
    background: white;
    border-radius: 6px;
    padding: 1rem;
    border: 1px solid #e9ecef;
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 1rem;
    align-items: center;
}

.transaction-date {
    font-size: 0.8rem;
    color: #666;
    font-weight: 500;
}

.transaction-type {
    font-weight: 600;
    color: #333;
}

.transaction-amount {
    font-weight: 700;
    font-size: 1rem;
}

.transaction-amount.positive {
    color: #28a745;
}

.transaction-amount.negative {
    color: #dc3545;
}

/* Location Card */
.location {
    grid-column: 2;
    grid-row: 2;
}

.location-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.address-section {
    background: white;
    border-radius: 6px;
    padding: 1rem;
    border: 1px solid #e9ecef;
}

.address-line {
    margin-bottom: 0.25rem;
    color: #333;
}

.coordinates-section {
    background: white;
    border-radius: 6px;
    padding: 1rem;
    border: 1px solid #e9ecef;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.coordinate-item {
    text-align: center;
}

.coordinate-label {
    font-size: 0.8rem;
    color: #666;
    font-weight: 600;
    text-transform: uppercase;
}

.coordinate-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: #333;
    font-family: 'Courier New', monospace;
}

.location-map {
    background: white;
    border-radius: 6px;
    padding: 1rem;
    border: 1px solid #e9ecef;
    margin-top: 1rem;
}

#map {
    border: 1px solid #ddd;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .assessment-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto auto;
    }

    .customer-info {
        grid-column: 1;
        grid-row: 1;
    }

    .priority-urgency {
        grid-column: 1;
        grid-row: 2;
    }

    .transactions {
        grid-column: 1;
        grid-row: 3;
    }

    .location {
        grid-column: 1;
        grid-row: 4;
    }
}
