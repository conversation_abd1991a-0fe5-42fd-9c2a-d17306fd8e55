# Customer Service Assessment Dashboard

An Electron application for consuming and displaying customer service assessment messages from Solace PubSub+ topics with professional data visualization.

## Features

- **Customer Service Assessment Dashboard**: Professional visualization of customer service data
- **Automatic Schema Detection**: Recognizes and parses JSON messages matching the analysis schema
- **Priority & Urgency Display**: Visual indicators for customer service priority and urgency levels
- **Customer Information Panel**: Complete customer details including contact and location information
- **Interactive Transaction Chart**: Bar graph visualization of recent financial transactions with Chart.js
- **Interactive Location Map**: Real-time map display of customer location using Leaflet maps
- **Geographic Visualization**: Customer address with precise coordinate mapping and area indicators
- **Real-time Message Display**: View all incoming messages with timestamps and topic information
- **Configurable Connection**: Use environment variables for Solace connection parameters
- **Topic Subscription Management**: Subscribe/unsubscribe to topics dynamically
- **Connection Status**: Visual indicators for connection state
- **Message Rate Monitoring**: Real-time message rate display
- **Auto-scroll**: Automatic scrolling for new messages
- **Responsive Design**: Professional UI that adapts to different screen sizes

## Prerequisites

- Node.js (v16 or higher)
- Access to a Solace PubSub+ broker
- Internet connection for enhanced maps and charts (optional - fallbacks available)

## Recent Updates

### Dependency Updates (v1.0.0)
- Updated Electron to v36.4.0 (latest stable)
- Updated electron-builder to v26.0.17 (latest stable)
- Updated solclientjs to v10.18.1 (latest stable)
- Updated dotenv to v16.4.7 (latest stable)
- **Fixed deprecated package warnings** by adding package overrides:
  - `glob`: Updated to v10.4.5 (from deprecated v7.2.3)
  - `inflight`: Replaced with `@isaacs/inflight` (memory leak fix)
  - `boolean`: Replaced with `yn` package (no longer supported)
- **Updated configuration** to use DATAPLANE Solace variables from root `.env` file:
  - Now reads from `../../.env` instead of local `.env`
  - Uses `DATAPLANE_SOLACE_BROKER_*` environment variables for consistency
- **Added Customer Service Assessment Dashboard**:
  - Automatic detection of analysis schema JSON messages
  - Professional display of customer service priority and urgency
  - Interactive customer information and transaction history
  - Interactive location mapping with Leaflet maps
  - Transaction visualization with Chart.js bar graphs
  - Scrollable assessment interface with custom scrollbar styling
  - Removed message filtering and auto-scroll controls for cleaner interface

## Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Ensure the root project `.env` file contains the DATAPLANE Solace configuration:
   ```env
   DATAPLANE_SOLACE_BROKER_URL=wss://your-solace-broker:443
   DATAPLANE_SOLACE_BROKER_VPN=your-vpn-name
   DATAPLANE_SOLACE_BROKER_USERNAME=your-username
   DATAPLANE_SOLACE_BROKER_PASSWORD=your-password
   SOLACE_TOPIC=your/default/topic
   ```

## Usage

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

### Building Distributables
```bash
npm run build
```

## Configuration

The application uses environment variables from the root project `.env` file for configuration. All connection parameters should be set using the DATAPLANE variables:

- `DATAPLANE_SOLACE_BROKER_URL`: WebSocket or TCP URL of your Solace broker
- `DATAPLANE_SOLACE_BROKER_VPN`: Message VPN name
- `DATAPLANE_SOLACE_BROKER_USERNAME`: Username for authentication
- `DATAPLANE_SOLACE_BROKER_PASSWORD`: Password for authentication
- `SOLACE_TOPIC`: Default topic to display in the subscription field (optional)

## Customer Service Assessment Dashboard

### Automatic Schema Detection
The application automatically detects incoming JSON messages that match the customer service analysis schema and displays them in a professional dashboard format.

### Assessment Display Components

#### Scrollable Interface
- **Responsive Height**: Maximum 70% of viewport height with automatic scrolling
- **Custom Scrollbar**: Professional styling matching the application theme
- **Fade Indicator**: Subtle gradient at bottom indicating more content below
- **Flexible Layout**: Adapts to different screen sizes and content amounts

#### Customer Information Panel
- **Customer Name**: First and last name display
- **Contact Information**: Phone number and complete address
- **Location Details**: City, province, postal code, and country
- **Geographic Coordinates**: Latitude and longitude with precision formatting

#### Priority & Urgency Indicators
- **Visual Priority Levels**: Color-coded badges (High=Red, Medium=Yellow, Low=Green)
- **Detailed Reasoning**: Explanatory text for priority and urgency assessments
- **Professional Styling**: Clean, easy-to-read format with appropriate visual hierarchy

#### Recent Transactions
- **Interactive Bar Chart**: Visual representation of transaction amounts using Chart.js
- **Fallback Chart**: Custom HTML5 Canvas implementation when Chart.js unavailable
- **Transaction History List**: Date, type, and amount for recent customer transactions
- **Currency Formatting**: Professional Canadian dollar formatting
- **Color-coded Visualization**: Green for deposits, red for withdrawals, yellow for transfers
- **Responsive Chart**: Automatically scales and formats for optimal viewing

#### Location Information & Mapping
- **Interactive Map**: Real-time map display using Leaflet with OpenStreetMap tiles
- **Fallback Map**: Professional coordinate display when Leaflet unavailable
- **Customer Marker**: Custom red marker indicating exact customer location (when available)
- **Area Indicator**: 500-meter radius circle showing the general customer area (when available)
- **Popup Information**: Click marker to view complete customer address details (when available)
- **Address Display**: Formatted customer address information with coordinates
- **Coordinate Precision**: Latitude and longitude coordinates with 4-decimal precision
- **Offline Support**: Graceful degradation when internet connection unavailable

### JSON Schema Support
The application expects incoming JSON messages to follow this structure:
```json
{
  "customer_service_assessment": {
    "priority": {
      "level": "High|Medium|Low",
      "reason": "Detailed explanation"
    },
    "urgency": {
      "level": "High|Medium|Low",
      "reason": "Detailed explanation"
    },
    "recent_transactions": [
      {
        "date": "YYYY-MM-DD",
        "type": "Deposit|Withdrawal|Transfer",
        "amount": 1234.56
      }
    ],
    "customer_location": {
      "first_name": "Customer",
      "last_name": "Name",
      "phone_number": "+1-xxx-xxx-xxxx",
      "address": "Street Address",
      "city": "City",
      "province": "Province",
      "postal_code": "Postal Code",
      "country": "Country",
      "coordinates": {
        "latitude": 45.1234,
        "longitude": -75.1234
      }
    }
  }
}
```

## Fallback Implementations

The application includes robust fallback implementations that ensure functionality even when external libraries are unavailable:

### Chart Fallback
- **HTML5 Canvas**: Custom bar chart implementation using native Canvas API
- **Professional Styling**: Maintains visual consistency with Chart.js version
- **Currency Formatting**: Full Canadian dollar formatting support
- **Color Coding**: Identical color scheme for transaction types
- **Automatic Scaling**: Dynamic scaling based on transaction amounts

### Map Fallback
- **Coordinate Display**: Professional presentation of latitude/longitude
- **Address Information**: Complete customer address formatting
- **Visual Design**: Gradient background with location icon
- **Responsive Layout**: Adapts to container size
- **Accessibility**: Clear, readable text with high contrast

### Automatic Detection
The application automatically detects library availability and seamlessly switches to fallback implementations without user intervention.

## Application Interface

### Connection Controls
- **Connect/Disconnect**: Establish or terminate connection to Solace broker
- **Status Indicator**: Visual connection state (green=connected, red=disconnected, orange=connecting)

### Topic Management
- **Topic Input**: Enter the topic pattern to subscribe to
- **Subscribe/Unsubscribe**: Manage topic subscriptions

### Message Display
- **Message List**: Real-time display of incoming messages
- **Message Counter**: Total number of received messages
- **Message Rate**: Messages per second
- **Auto-scroll**: Automatically scroll to newest messages
- **Clear**: Remove all displayed messages

### Footer Information
- Displays current connection configuration (URL, VPN, Username)

## Message Format

Each displayed message includes:
- **Timestamp**: When the message was received
- **Topic**: The topic the message was published to
- **Payload**: The message content

## Troubleshooting

### Connection Issues
1. Verify your Solace broker is running and accessible
2. Check the connection parameters in your `.env` file
3. Ensure the WebSocket port (typically 8008) is open
4. Verify your username/password credentials

### Subscription Issues
1. Ensure you're connected before subscribing
2. Check topic permissions for your user
3. Verify topic syntax (use `/` for hierarchy, `*` for wildcards)

### Performance
- For high message rates, consider using message filtering
- Clear messages periodically to maintain performance
- Disable auto-scroll for very high throughput scenarios

## Development

### Project Structure
```
├── main.js           # Main Electron process
├── renderer.js       # Renderer process (UI logic)
├── solace-client.js  # Solace messaging client wrapper
├── index.html        # Main application window
├── styles.css        # Application styling
├── package.json      # Dependencies and scripts
└── .env.example      # Configuration template
```

### Adding Features
- Message persistence
- Export functionality
- Multiple topic subscriptions
- Message publishing capabilities
- Advanced filtering options

## License

MIT License - see LICENSE file for details
