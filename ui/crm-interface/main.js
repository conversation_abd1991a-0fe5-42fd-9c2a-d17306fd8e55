const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

let mainWindow;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    icon: path.join(__dirname, 'assets/icon.png'), // Optional: add an icon
    title: 'Solace Message Consumer'
  });

  mainWindow.loadFile('index.html');

  // Open DevTools in development
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC handlers for communication with renderer process
ipcMain.handle('get-config', () => {
  return {
    url: process.env.DATAPLANE_SOLACE_BROKER_URL || 'ws://localhost:8008',
    vpn: process.env.DATAPLANE_SOLACE_BROKER_VPN || 'default',
    username: process.env.DATAPLANE_SOLACE_BROKER_USERNAME || 'default',
    password: process.env.DATAPLANE_SOLACE_BROKER_PASSWORD || '',
    topic: process.env.SOLACE_TOPIC || 'fab/customer/feedback/enriched/v1/>'
  };
});

ipcMain.handle('show-error', async (event, title, message) => {
  return dialog.showErrorBox(title, message);
});

ipcMain.handle('show-info', async (event, title, message) => {
  return dialog.showMessageBox(mainWindow, {
    type: 'info',
    title: title,
    message: message,
    buttons: ['OK']
  });
});
