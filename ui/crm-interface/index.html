<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Service Assessment Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <!-- Leaflet CSS for maps -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <!-- Chart.js for charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Leaflet JS for maps -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Customer Service Assessment Dashboard</h1>
            <div class="connection-status">
                <span id="status-indicator" class="status-indicator disconnected"></span>
                <span id="status-text">Disconnected</span>
            </div>
        </header>

        <div class="controls">
            <div class="control-group">
                <label for="topic-input">Topic:</label>
                <input type="text" id="topic-input" placeholder="Enter topic to subscribe to" />
                <button id="subscribe-btn" class="btn btn-primary">Subscribe</button>
                <button id="unsubscribe-btn" class="btn btn-secondary" disabled>Unsubscribe</button>
            </div>
            
            <div class="control-group">
                <button id="connect-btn" class="btn btn-success">Connect</button>
                <button id="disconnect-btn" class="btn btn-danger" disabled>Disconnect</button>
                <button id="clear-btn" class="btn btn-secondary">Clear Messages</button>
            </div>
        </div>



        <!-- Customer Service Assessment Section -->
        <div id="assessment-container" class="assessment-container" style="display: none;">
            <div class="assessment-header">
                <h2>Customer Service Assessment</h2>
                <button id="close-assessment" class="btn btn-secondary">×</button>
            </div>

            <div class="assessment-content">
                <div class="assessment-grid">
                    <!-- Customer Information -->
                    <div class="assessment-card customer-info">
                        <h3>Customer Information</h3>
                        <div id="customer-details" class="customer-details">
                            <!-- Customer details will be populated here -->
                        </div>
                    </div>

                    <!-- Priority & Urgency -->
                    <div class="assessment-card priority-urgency">
                        <div class="priority-section">
                            <h3>Priority</h3>
                            <div id="priority-info" class="priority-info">
                                <!-- Priority info will be populated here -->
                            </div>
                        </div>
                        <div class="urgency-section">
                            <h3>Urgency</h3>
                            <div id="urgency-info" class="urgency-info">
                                <!-- Urgency info will be populated here -->
                            </div>
                        </div>
                    </div>

                    <!-- Recent Transactions Chart -->
                    <div class="assessment-card transactions">
                        <h3>Recent Transactions</h3>
                        <div id="transactions-chart" class="transactions-chart">
                            <canvas id="transactionsCanvas" width="400" height="200"></canvas>
                        </div>
                        <div id="transactions-list" class="transactions-list">
                            <!-- Transactions will be populated here -->
                        </div>
                    </div>

                    <!-- Location & Map -->
                    <div class="assessment-card location">
                        <h3>Customer Location</h3>
                        <div id="location-info" class="location-info">
                            <!-- Location info will be populated here -->
                        </div>
                        <div id="location-map" class="location-map">
                            <div id="map" style="height: 200px; width: 100%; border-radius: 6px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="message-container">
            <div class="message-header">
                <h3>Messages (<span id="message-count">0</span>)</h3>
                <div class="message-stats">
                    <span>Rate: <span id="message-rate">0</span> msg/s</span>
                </div>
            </div>
            <div id="message-list" class="message-list"></div>
        </div>

        <div class="footer">
            <div class="config-info">
                <span>URL: <span id="config-url">-</span></span>
                <span>VPN: <span id="config-vpn">-</span></span>
                <span>User: <span id="config-username">-</span></span>
            </div>
        </div>
    </div>

    <script src="solace-client.js"></script>
    <script src="renderer.js"></script>
</body>
</html>
